/**
 * 页面访问控制测试
 * 
 * 测试PRD要求的页面级权限控制
 * 包括ProtectedRoute、PermissionGuard等组件的访问控制
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { PermissionGuard } from '@/components/auth/PermissionGuard'
import { useAuth } from '@/hooks/useAuth'
import { SimplePermissionManager } from '@/utils/auth/SimplePermissionManager'

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}))

jest.mock('@/hooks/useAuth', () => ({
  useAuth: jest.fn()
}))

jest.mock('@/utils/auth/SimplePermissionManager', () => ({
  SimplePermissionManager: {
    checkPermission: jest.fn(),
    getPermissionLevelDescription: jest.fn()
  }
}))

describe('页面访问控制', () => {
  const mockPush = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    })
  })

  describe('ProtectedRoute', () => {
    const mockUser = {
      id: 'user-1',
      username: 'testuser',
      roles: [{ code: 'employee', permissions: [] }]
    }

    it('应该在用户未认证时显示登录提示', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: null,
        isAuthenticated: false,
        isLoading: false
      })

      render(
        <ProtectedRoute>
          <div data-testid="protected-content">Protected Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByText('请先登录')).toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })

    it('应该在加载中时显示加载指示器', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: null,
        isAuthenticated: false,
        isLoading: true
      })

      render(
        <ProtectedRoute>
          <div data-testid="protected-content">Protected Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByText('正在验证权限...')).toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })

    it('应该在用户已认证且无权限要求时显示内容', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false
      })

      render(
        <ProtectedRoute>
          <div data-testid="protected-content">Protected Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByTestId('protected-content')).toBeInTheDocument()
    })

    it('应该在用户有权限时显示内容', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: true,
        userLevel: 'employee'
      })

      ;(SimplePermissionManager.getPermissionLevelDescription as jest.Mock).mockReturnValue('普通员工')

      render(
        <ProtectedRoute requiredPermission="data:view">
          <div data-testid="protected-content">Protected Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByTestId('protected-content')).toBeInTheDocument()
    })

    it('应该在用户无权限时显示权限不足提示', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: false,
        userLevel: 'employee'
      })

      ;(SimplePermissionManager.getPermissionLevelDescription as jest.Mock).mockReturnValue('普通员工')

      render(
        <ProtectedRoute requiredPermission="admin:users:create">
          <div data-testid="protected-content">Protected Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByText('权限不足')).toBeInTheDocument()
      expect(screen.getByText('需要权限: admin:users:create')).toBeInTheDocument()
      expect(screen.getByText('当前级别: 普通员工')).toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })

    it('应该在用户无权限时显示自定义fallback', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: false,
        userLevel: 'employee'
      })

      render(
        <ProtectedRoute 
          requiredPermission="admin:users:create"
          fallback={<div data-testid="custom-fallback">Custom Fallback</div>}
        >
          <div data-testid="protected-content">Protected Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })

    it('应该正确处理角色要求', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: {
          ...mockUser,
          roles: [{ code: 'admin', permissions: [] }]
        },
        isAuthenticated: true,
        isLoading: false
      })

      render(
        <ProtectedRoute requiredRole="admin">
          <div data-testid="protected-content">Admin Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByTestId('protected-content')).toBeInTheDocument()
    })

    it('应该在角色不匹配时显示权限不足', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser, // employee role
        isAuthenticated: true,
        isLoading: false
      })

      render(
        <ProtectedRoute requiredRole="admin">
          <div data-testid="protected-content">Admin Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByText('权限不足')).toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })
  })

  describe('PermissionGuard', () => {
    const mockUser = {
      id: 'user-1',
      username: 'testuser',
      roles: [{ code: 'employee', permissions: [] }]
    }

    it('应该在用户未认证时隐藏内容', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: null,
        isAuthenticated: false
      })

      render(
        <PermissionGuard permission="data:view">
          <div data-testid="guarded-content">Guarded Content</div>
        </PermissionGuard>
      )

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument()
    })

    it('应该在用户有权限时显示内容', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: true,
        userLevel: 'employee'
      })

      render(
        <PermissionGuard permission="data:view">
          <div data-testid="guarded-content">Guarded Content</div>
        </PermissionGuard>
      )

      expect(screen.getByTestId('guarded-content')).toBeInTheDocument()
    })

    it('应该在用户无权限时显示默认权限提示', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: false,
        userLevel: 'employee'
      })

      ;(SimplePermissionManager.getPermissionLevelDescription as jest.Mock).mockReturnValue('普通员工')

      render(
        <PermissionGuard permission="admin:users:create">
          <div data-testid="guarded-content">Guarded Content</div>
        </PermissionGuard>
      )

      expect(screen.getByText('权限不足')).toBeInTheDocument()
      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument()
    })

    it('应该在用户无权限时显示自定义fallback', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: false,
        userLevel: 'employee'
      })

      render(
        <PermissionGuard 
          permission="admin:users:create"
          fallback={<div data-testid="custom-guard-fallback">No Access</div>}
        >
          <div data-testid="guarded-content">Guarded Content</div>
        </PermissionGuard>
      )

      expect(screen.getByTestId('custom-guard-fallback')).toBeInTheDocument()
      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument()
    })

    it('应该在fallback为null时完全隐藏内容', () => {
      ;(useAuth as jest.Mock).mockReturnValue({
        user: mockUser,
        isAuthenticated: true
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: false,
        userLevel: 'employee'
      })

      render(
        <PermissionGuard 
          permission="admin:users:create"
          fallback={null}
        >
          <div data-testid="guarded-content">Guarded Content</div>
        </PermissionGuard>
      )

      expect(screen.queryByTestId('guarded-content')).not.toBeInTheDocument()
      expect(screen.queryByText('权限不足')).not.toBeInTheDocument()
    })
  })

  describe('权限级别测试', () => {
    it('管理员应该能访问所有页面', () => {
      const adminUser = {
        id: 'admin-1',
        username: 'admin',
        roles: [{ code: 'admin', permissions: [] }]
      }

      ;(useAuth as jest.Mock).mockReturnValue({
        user: adminUser,
        isAuthenticated: true,
        isLoading: false
      })

      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: true,
        userLevel: 'admin'
      })

      render(
        <ProtectedRoute requiredPermission="admin:users:create">
          <div data-testid="admin-content">Admin Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByTestId('admin-content')).toBeInTheDocument()
    })

    it('员工应该只能访问员工页面', () => {
      const employeeUser = {
        id: 'employee-1',
        username: 'employee',
        roles: [{ code: 'employee', permissions: [] }]
      }

      ;(useAuth as jest.Mock).mockReturnValue({
        user: employeeUser,
        isAuthenticated: true,
        isLoading: false
      })

      // 员工访问员工权限页面
      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: true,
        userLevel: 'employee'
      })

      render(
        <ProtectedRoute requiredPermission="data:view">
          <div data-testid="employee-content">Employee Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByTestId('employee-content')).toBeInTheDocument()
    })

    it('员工不应该能访问管理员页面', () => {
      const employeeUser = {
        id: 'employee-1',
        username: 'employee',
        roles: [{ code: 'employee', permissions: [] }]
      }

      ;(useAuth as jest.Mock).mockReturnValue({
        user: employeeUser,
        isAuthenticated: true,
        isLoading: false
      })

      // 员工访问管理员权限页面
      ;(SimplePermissionManager.checkPermission as jest.Mock).mockReturnValue({
        hasPermission: false,
        userLevel: 'employee'
      })

      ;(SimplePermissionManager.getPermissionLevelDescription as jest.Mock).mockReturnValue('普通员工')

      render(
        <ProtectedRoute requiredPermission="admin:users:create">
          <div data-testid="admin-content">Admin Content</div>
        </ProtectedRoute>
      )

      expect(screen.getByText('权限不足')).toBeInTheDocument()
      expect(screen.queryByTestId('admin-content')).not.toBeInTheDocument()
    })
  })
})
