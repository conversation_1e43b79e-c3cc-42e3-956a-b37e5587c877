/**
 * 简化权限控制组件测试
 * 
 * 测试PRD要求的极简版权限控制组件
 * 包括AdminOnly、EmployeeAndAbove、PermissionGuard等组件
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import {
  AdminOnly,
  EmployeeAndAbove,
  PermissionLevelDisplay,
  PermissionDenied,
  FeatureWrapper,
  RoleBadge,
  PermissionIndicator,
  PermissionButton
} from '@/components/auth/SimplePermissionComponents'
import { useSimplePermission, useAdminCheck, useEmployeeCheck } from '@/hooks/useSimplePermission'

// Mock hooks
jest.mock('@/hooks/useSimplePermission', () => ({
  useSimplePermission: jest.fn(),
  useAdminCheck: jest.fn(),
  useEmployeeCheck: jest.fn()
}))

describe('SimplePermissionComponents', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('AdminOnly', () => {
    it('应该在用户是管理员时显示内容', () => {
      ;(useAdminCheck as jest.Mock).mockReturnValue(true)

      render(
        <AdminOnly>
          <div data-testid="admin-content">Admin Only Content</div>
        </AdminOnly>
      )

      expect(screen.getByTestId('admin-content')).toBeInTheDocument()
    })

    it('应该在用户不是管理员时隐藏内容', () => {
      ;(useAdminCheck as jest.Mock).mockReturnValue(false)

      render(
        <AdminOnly>
          <div data-testid="admin-content">Admin Only Content</div>
        </AdminOnly>
      )

      expect(screen.queryByTestId('admin-content')).not.toBeInTheDocument()
    })

    it('应该在用户不是管理员时显示fallback内容', () => {
      ;(useAdminCheck as jest.Mock).mockReturnValue(false)

      render(
        <AdminOnly fallback={<div data-testid="fallback">No Permission</div>}>
          <div data-testid="admin-content">Admin Only Content</div>
        </AdminOnly>
      )

      expect(screen.queryByTestId('admin-content')).not.toBeInTheDocument()
      expect(screen.getByTestId('fallback')).toBeInTheDocument()
    })
  })

  describe('EmployeeAndAbove', () => {
    it('应该在用户是员工时显示内容', () => {
      ;(useEmployeeCheck as jest.Mock).mockReturnValue(true)

      render(
        <EmployeeAndAbove>
          <div data-testid="employee-content">Employee Content</div>
        </EmployeeAndAbove>
      )

      expect(screen.getByTestId('employee-content')).toBeInTheDocument()
    })

    it('应该在用户不是员工时隐藏内容', () => {
      ;(useEmployeeCheck as jest.Mock).mockReturnValue(false)

      render(
        <EmployeeAndAbove>
          <div data-testid="employee-content">Employee Content</div>
        </EmployeeAndAbove>
      )

      expect(screen.queryByTestId('employee-content')).not.toBeInTheDocument()
    })
  })

  describe('PermissionLevelDisplay', () => {
    it('应该显示管理员级别', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'admin',
        isAdmin: true,
        getLevelDescription: () => '系统管理员'
      })

      render(<PermissionLevelDisplay showIcon={true} />)

      expect(screen.getByText('系统管理员')).toBeInTheDocument()
    })

    it('应该显示员工级别', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'employee',
        isAdmin: false,
        getLevelDescription: () => '普通员工'
      })

      render(<PermissionLevelDisplay showIcon={true} />)

      expect(screen.getByText('普通员工')).toBeInTheDocument()
    })

    it('应该显示未登录状态', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: null,
        isAdmin: false,
        getLevelDescription: () => '未登录'
      })

      render(<PermissionLevelDisplay />)

      expect(screen.getByText('未登录')).toBeInTheDocument()
    })

    it('应该在showDescription为true时显示权限描述', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'admin',
        isAdmin: true,
        getLevelDescription: () => '系统管理员'
      })

      render(<PermissionLevelDisplay showDescription={true} />)

      expect(screen.getByText('拥有所有权限')).toBeInTheDocument()
    })
  })

  describe('PermissionDenied', () => {
    it('应该显示权限不足提示', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'employee',
        getLevelDescription: () => '普通员工'
      })

      render(<PermissionDenied requiredLevel="admin" />)

      expect(screen.getByText('权限不足')).toBeInTheDocument()
      expect(screen.getByText('此功能需要管理员权限')).toBeInTheDocument()
      expect(screen.getByText('当前权限: 普通员工')).toBeInTheDocument()
      expect(screen.getByText('需要权限: 系统管理员')).toBeInTheDocument()
    })

    it('应该显示自定义消息', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'employee',
        getLevelDescription: () => '普通员工'
      })

      render(<PermissionDenied message="自定义权限不足消息" />)

      expect(screen.getByText('自定义权限不足消息')).toBeInTheDocument()
    })

    it('应该在showUpgrade为true时显示升级提示', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'employee',
        getLevelDescription: () => '普通员工'
      })

      render(<PermissionDenied showUpgrade={true} />)

      expect(screen.getByText('请联系系统管理员升级您的账户权限')).toBeInTheDocument()
    })
  })

  describe('FeatureWrapper', () => {
    it('应该在有权限时显示内容', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(true)
      })

      render(
        <FeatureWrapper feature="data:view">
          <div data-testid="feature-content">Feature Content</div>
        </FeatureWrapper>
      )

      expect(screen.getByTestId('feature-content')).toBeInTheDocument()
    })

    it('应该在无权限时隐藏内容', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(false)
      })

      render(
        <FeatureWrapper feature="admin:users:create">
          <div data-testid="feature-content">Feature Content</div>
        </FeatureWrapper>
      )

      expect(screen.queryByTestId('feature-content')).not.toBeInTheDocument()
    })

    it('应该在无权限且showDenied为true时显示权限不足提示', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(false)
      })

      render(
        <FeatureWrapper feature="admin:users:create" showDenied={true}>
          <div data-testid="feature-content">Feature Content</div>
        </FeatureWrapper>
      )

      expect(screen.getByText('权限不足')).toBeInTheDocument()
    })
  })

  describe('RoleBadge', () => {
    it('应该显示管理员角色标识', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'admin'
      })

      render(<RoleBadge />)

      expect(screen.getByText('管理员')).toBeInTheDocument()
    })

    it('应该显示员工角色标识', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: 'employee'
      })

      render(<RoleBadge />)

      expect(screen.getByText('员工')).toBeInTheDocument()
    })

    it('应该显示指定的角色', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        userLevel: null
      })

      render(<RoleBadge role="admin" />)

      expect(screen.getByText('管理员')).toBeInTheDocument()
    })
  })

  describe('PermissionIndicator', () => {
    it('应该在有权限时显示绿色指示器', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(true)
      })

      render(<PermissionIndicator permission="data:view" />)

      expect(screen.getByText('有权限')).toBeInTheDocument()
    })

    it('应该在无权限时显示红色指示器', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(false)
      })

      render(<PermissionIndicator permission="admin:users:create" />)

      expect(screen.getByText('无权限')).toBeInTheDocument()
    })

    it('应该在showText为false时只显示指示器', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(true)
      })

      render(<PermissionIndicator permission="data:view" showText={false} />)

      expect(screen.queryByText('有权限')).not.toBeInTheDocument()
    })
  })

  describe('PermissionButton', () => {
    it('应该在有权限时启用按钮', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(true)
      })

      render(
        <PermissionButton permission="data:edit" onClick={jest.fn()}>
          Edit
        </PermissionButton>
      )

      const button = screen.getByRole('button', { name: 'Edit' })
      expect(button).not.toBeDisabled()
    })

    it('应该在无权限时禁用按钮', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(false)
      })

      render(
        <PermissionButton permission="admin:users:delete" onClick={jest.fn()}>
          Delete
        </PermissionButton>
      )

      const button = screen.getByRole('button', { name: 'Delete' })
      expect(button).toBeDisabled()
    })

    it('应该在无权限时显示权限提示', () => {
      ;(useSimplePermission as jest.Mock).mockReturnValue({
        hasPermission: jest.fn().mockReturnValue(false)
      })

      render(
        <PermissionButton permission="admin:users:delete" onClick={jest.fn()}>
          Delete
        </PermissionButton>
      )

      const button = screen.getByRole('button', { name: 'Delete' })
      expect(button).toHaveAttribute('title', '需要 admin:users:delete 权限')
    })
  })
})
