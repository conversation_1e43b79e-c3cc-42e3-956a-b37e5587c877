/**
 * 受保护路由组件
 * 
 * 提供页面级权限控制，检查用户是否有权限访问特定页面
 * 遵循PRD文档中的权限验证组件设计
 */

'use client'

import React from 'react'
import { Result, Button, Spin } from 'antd'
import { LockOutlined, ExclamationCircleOutlined } from '@ant-design/icons'
import { ProtectedRouteProps } from '@/types/auth'
import { useAuth } from '@/hooks/useAuth'
import { SimplePermissionManager } from '@/utils/auth/SimplePermissionManager'

/**
 * 受保护路由组件
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  fallback
}) => {
  const { user, isAuthenticated, isLoading, checkPermission, checkRole } = useAuth()

  // 加载中状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" tip="验证用户权限中..." />
      </div>
    )
  }

  // 未认证状态
  if (!isAuthenticated || !user) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <Result
        status="403"
        title="未登录"
        subTitle="请先登录后再访问此页面"
        icon={<LockOutlined />}
        extra={
          <Button type="primary" onClick={() => window.location.href = '/login'}>
            前往登录
          </Button>
        }
      />
    )
  }

  // 检查权限（支持简化权限模型）
  if (requiredPermission) {
    // 优先使用简化权限检查（PRD要求）
    const simplePermissionResult = SimplePermissionManager.checkPermission(user, requiredPermission)

    if (!simplePermissionResult.hasPermission) {
      if (fallback) {
        return <>{fallback}</>
      }

      return (
        <Result
          status="403"
          title="权限不足"
          subTitle={
            <div>
              <p>您没有访问此页面的权限</p>
              <p className="text-sm text-gray-500">需要权限: {requiredPermission}</p>
              <p className="text-sm text-gray-500">当前级别: {SimplePermissionManager.getPermissionLevelDescription(simplePermissionResult.userLevel)}</p>
            </div>
          }
          icon={<ExclamationCircleOutlined />}
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回上一页
            </Button>
          }
        />
      )
    }
  }

  // 检查角色
  if (requiredRole && !checkRole(requiredRole)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <Result
        status="403"
        title="角色权限不足"
        subTitle={`您的角色无法访问此页面。需要角色: ${requiredRole}`}
        icon={<ExclamationCircleOutlined />}
        extra={
          <Button type="primary" onClick={() => window.history.back()}>
            返回上一页
          </Button>
        }
      />
    )
  }

  // 权限验证通过，渲染子组件
  return <>{children}</>
}

export default ProtectedRoute
