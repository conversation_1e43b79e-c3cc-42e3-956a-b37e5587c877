/**
 * 简化角色选择器组件
 * 
 * 根据PRD要求，提供简单的admin/employee角色选择
 * 替代复杂的权限树组件，专注于小企业的基础需求
 */

'use client'

import React from 'react'
import { Card, Radio, Space, Tag, Descriptions, Alert } from 'antd'
import { UserOutlined, CrownOutlined, SafetyOutlined } from '@ant-design/icons'
import { SimplePermissionManager, SimplePermissionLevel } from '@/utils/auth/SimplePermissionManager'

/**
 * 简化角色选择器属性接口
 */
export interface SimpleRoleSelectorProps {
  /** 当前选中的角色 */
  value?: SimplePermissionLevel
  /** 角色变更回调 */
  onChange?: (role: SimplePermissionLevel) => void
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示权限详情 */
  showDetails?: boolean
  /** 组件大小 */
  size?: 'small' | 'middle' | 'large'
}

/**
 * 角色配置定义
 */
const ROLE_CONFIG = {
  admin: {
    label: '系统管理员',
    icon: <CrownOutlined />,
    color: 'red',
    description: '拥有系统所有权限，可以管理用户、配置系统',
    features: [
      '用户管理（增删改查）',
      '系统配置管理',
      '所有数据访问权限',
      '角色权限管理',
      '系统监控和维护'
    ]
  },
  employee: {
    label: '普通员工',
    icon: <UserOutlined />,
    color: 'blue',
    description: '具有基础操作权限，可以查看和编辑业务数据',
    features: [
      '查看业务数据',
      '编辑业务数据',
      '创建和更新订单',
      '生产管理操作',
      '仓库管理操作'
    ]
  }
} as const

/**
 * 简化角色选择器组件
 */
export const SimpleRoleSelector: React.FC<SimpleRoleSelectorProps> = ({
  value,
  onChange,
  readonly = false,
  showDetails = true,
  size = 'middle'
}) => {
  
  /**
   * 处理角色变更
   */
  const handleRoleChange = (role: SimplePermissionLevel) => {
    if (!readonly && onChange) {
      onChange(role)
    }
  }

  /**
   * 渲染角色选项
   */
  const renderRoleOption = (role: SimplePermissionLevel) => {
    const config = ROLE_CONFIG[role]
    const isSelected = value === role
    
    return (
      <Card
        key={role}
        size="small"
        className={`cursor-pointer transition-all ${
          isSelected 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-200 hover:border-blue-300'
        } ${readonly ? 'cursor-not-allowed opacity-60' : ''}`}
        onClick={() => !readonly && handleRoleChange(role)}
        bodyStyle={{ padding: '12px' }}
      >
        <div className="flex items-start space-x-3">
          <div className={`text-lg ${isSelected ? 'text-blue-600' : 'text-gray-500'}`}>
            {config.icon}
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <span className={`font-medium ${isSelected ? 'text-blue-900' : 'text-gray-900'}`}>
                {config.label}
              </span>
              <Tag color={config.color} size="small">
                {role}
              </Tag>
              {isSelected && (
                <Tag color="green" size="small">
                  已选择
                </Tag>
              )}
            </div>
            
            <p className="text-sm text-gray-600 mb-2">
              {config.description}
            </p>
            
            {showDetails && (
              <div className="space-y-1">
                {config.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-1 text-xs text-gray-500">
                    <SafetyOutlined className="text-green-500" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* 角色选择提示 */}
      <Alert
        message="角色权限说明"
        description="系统采用简化的两级权限模型，适合小企业快速上手使用。管理员拥有所有权限，员工拥有基础操作权限。"
        type="info"
        showIcon
        className="mb-4"
      />

      {/* 角色选项 */}
      <div className="space-y-3">
        {(Object.keys(ROLE_CONFIG) as SimplePermissionLevel[]).map(role => 
          renderRoleOption(role)
        )}
      </div>

      {/* 权限对比表 */}
      {showDetails && (
        <Card title="权限对比" size="small" className="mt-4">
          <Descriptions
            column={1}
            size="small"
            bordered
            items={[
              {
                key: 'user-management',
                label: '用户管理',
                children: (
                  <Space>
                    <Tag color="red">管理员</Tag>
                    <span className="text-gray-500">员工无此权限</span>
                  </Space>
                )
              },
              {
                key: 'system-config',
                label: '系统配置',
                children: (
                  <Space>
                    <Tag color="red">管理员</Tag>
                    <span className="text-gray-500">员工无此权限</span>
                  </Space>
                )
              },
              {
                key: 'data-view',
                label: '数据查看',
                children: (
                  <Space>
                    <Tag color="red">管理员</Tag>
                    <Tag color="blue">员工</Tag>
                  </Space>
                )
              },
              {
                key: 'data-edit',
                label: '数据编辑',
                children: (
                  <Space>
                    <Tag color="red">管理员</Tag>
                    <Tag color="blue">员工</Tag>
                  </Space>
                )
              },
              {
                key: 'data-delete',
                label: '数据删除',
                children: (
                  <Space>
                    <Tag color="red">管理员</Tag>
                    <span className="text-gray-500">员工无此权限</span>
                  </Space>
                )
              }
            ]}
          />
        </Card>
      )}

      {/* 当前选择状态 */}
      {value && (
        <Card size="small" className="bg-green-50 border-green-200">
          <div className="flex items-center space-x-2">
            <SafetyOutlined className="text-green-600" />
            <span className="text-green-800 font-medium">
              当前选择: {ROLE_CONFIG[value].label}
            </span>
            <Tag color={ROLE_CONFIG[value].color}>
              {value}
            </Tag>
          </div>
        </Card>
      )}
    </div>
  )
}

/**
 * 简化的角色选择器（单选模式）
 */
export const SimpleRoleRadio: React.FC<SimpleRoleSelectorProps> = ({
  value,
  onChange,
  readonly = false,
  size = 'middle'
}) => {
  return (
    <Radio.Group
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
      disabled={readonly}
      size={size}
    >
      <Space direction="vertical">
        {(Object.keys(ROLE_CONFIG) as SimplePermissionLevel[]).map(role => (
          <Radio key={role} value={role}>
            <Space>
              {ROLE_CONFIG[role].icon}
              <span>{ROLE_CONFIG[role].label}</span>
              <Tag color={ROLE_CONFIG[role].color} size="small">
                {role}
              </Tag>
            </Space>
          </Radio>
        ))}
      </Space>
    </Radio.Group>
  )
}

export default SimpleRoleSelector
