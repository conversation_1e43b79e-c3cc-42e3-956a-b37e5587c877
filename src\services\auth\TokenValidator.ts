/**
 * Token验证服务
 * 
 * 为中间件提供轻量级的Token验证功能
 * 避免在中间件中重复实现JWT解析逻辑
 */

import { JWT_CONFIG } from '@/config/jwt.config'

/**
 * Token验证结果接口
 */
export interface TokenValidationInfo {
  valid: boolean
  expired?: boolean
  payload?: any
  error?: string
}

/**
 * Token验证器类
 * 专门为中间件环境设计的轻量级Token验证
 */
export class TokenValidator {
  /**
   * 简单的JWT Token解析（不验证签名）
   * 在中间件环境中只检查Token格式和过期时间
   * 完整的签名验证在API路由中进行
   */
  static parseJWTToken(token: string): TokenValidationInfo {
    try {
      if (!token) {
        return { 
          valid: false, 
          error: 'Token不能为空' 
        }
      }

      const parts = token.split('.')
      if (parts.length !== 3) {
        return { 
          valid: false, 
          error: 'Token格式无效' 
        }
      }

      // 解析payload部分
      const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString())
      
      // 检查过期时间
      const now = Math.floor(Date.now() / 1000)
      const expired = payload.exp && payload.exp < now

      return {
        valid: true,
        expired,
        payload
      }
    } catch (error) {
      return { 
        valid: false, 
        error: 'Token解析失败' 
      }
    }
  }

  /**
   * 验证用户权限
   * 检查用户是否拥有访问特定路径的权限
   */
  static checkPagePermission(userPermissions: string[], pathname: string): boolean {
    // 页面权限配置
    const PAGE_PERMISSIONS: Record<string, string[]> = {
      '/admin': ['system:admin'],
      '/admin/users': ['user:read'],
      '/admin/roles': ['role:read'],
      '/sales': ['sales:read'],
      '/production': ['production:read'],
      '/warehouse': ['warehouse:read'],
      '/finance': ['finance:read']
    }

    const requiredPermissions = PAGE_PERMISSIONS[pathname]
    
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true // 没有特殊权限要求
    }

    // 检查用户是否拥有所需权限
    return requiredPermissions.some(permission => 
      userPermissions.includes(permission) || userPermissions.includes('system:admin')
    )
  }

  /**
   * 检查Token是否即将过期
   * @param payload Token载荷
   * @param thresholdMinutes 过期阈值（分钟）
   */
  static isTokenNearExpiry(payload: any, thresholdMinutes: number = 5): boolean {
    if (!payload || !payload.exp) {
      return false
    }

    const now = Math.floor(Date.now() / 1000)
    const timeToExpire = payload.exp - now
    return timeToExpire < (thresholdMinutes * 60)
  }

  /**
   * 获取Token剩余时间（秒）
   */
  static getTokenRemainingTime(payload: any): number {
    if (!payload || !payload.exp) {
      return 0
    }

    const now = Math.floor(Date.now() / 1000)
    return Math.max(0, payload.exp - now)
  }

  /**
   * 验证Token类型
   */
  static validateTokenType(payload: any, expectedType: 'access' | 'refresh'): boolean {
    if (!payload) {
      return false
    }

    // Access Token没有type字段，Refresh Token有type='refresh'
    if (expectedType === 'access') {
      return !payload.type || payload.type !== 'refresh'
    } else {
      return payload.type === 'refresh'
    }
  }

  /**
   * 提取用户基本信息
   */
  static extractUserInfo(payload: any): {
    userId: string
    username: string
    roles: string[]
    permissions: string[]
  } | null {
    if (!payload || !payload.userId) {
      return null
    }

    return {
      userId: payload.userId,
      username: payload.username || '',
      roles: payload.roles || [],
      permissions: payload.permissions || []
    }
  }

  /**
   * 生成请求ID
   */
  static generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
