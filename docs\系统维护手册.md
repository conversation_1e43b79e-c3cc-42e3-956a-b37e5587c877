# ERP系统维护手册

## 概述

本手册提供ERP系统认证和权限管理模块的日常维护指导，帮助系统管理员保持系统稳定运行。

## 日常维护任务

### 1. 系统监控

#### 1.1 应用状态监控
```bash
# 检查应用运行状态
pm2 status

# 查看应用日志
pm2 logs erp-system --lines 50

# 监控系统资源
pm2 monit
```

#### 1.2 系统资源监控
```bash
# 检查CPU和内存使用
htop

# 检查磁盘空间
df -h

# 检查网络连接
netstat -tulpn | grep :3000
```

#### 1.3 数据库监控
```sql
-- 检查数据库连接数
SHOW PROCESSLIST;

-- 检查数据库大小
SELECT 
    table_schema AS '数据库',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS '大小(MB)'
FROM information_schema.tables 
WHERE table_schema = 'erp_system'
GROUP BY table_schema;

-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

### 2. 日志管理

#### 2.1 应用日志
```bash
# 查看应用错误日志
tail -f /var/log/erp/error.log

# 查看访问日志
tail -f /var/log/erp/access.log

# 搜索特定错误
grep "ERROR" /var/log/erp/*.log | tail -20
```

#### 2.2 系统日志
```bash
# 查看系统日志
sudo journalctl -u erp-system -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/erp-system.error.log

# 查看MySQL日志
sudo tail -f /var/log/mysql/error.log
```

#### 2.3 日志清理
```bash
# 清理旧日志（保留30天）
find /var/log/erp -name "*.log" -mtime +30 -delete

# 压缩大日志文件
gzip /var/log/erp/*.log

# 重启日志轮转
sudo logrotate -f /etc/logrotate.d/erp-system
```

### 3. 数据库维护

#### 3.1 数据库备份
```bash
# 手动备份数据库
mysqldump -u erp_user -p erp_system > backup_$(date +%Y%m%d).sql

# 验证备份文件
gzip -t backup_$(date +%Y%m%d).sql.gz

# 查看备份文件大小
ls -lh backup_*.sql.gz
```

#### 3.2 数据库优化
```sql
-- 分析表结构
ANALYZE TABLE users, roles, permissions;

-- 优化表
OPTIMIZE TABLE users, roles, permissions;

-- 检查表完整性
CHECK TABLE users, roles, permissions;

-- 修复表（如有必要）
REPAIR TABLE table_name;
```

#### 3.3 数据库清理
```sql
-- 清理过期会话
DELETE FROM user_sessions 
WHERE expires_at < NOW() - INTERVAL 7 DAY;

-- 清理过期密码重置记录
DELETE FROM password_resets 
WHERE created_at < NOW() - INTERVAL 24 HOUR;

-- 更新统计信息
ANALYZE TABLE users, user_sessions;
```

## 安全维护

### 4. 用户账户管理

#### 4.1 定期安全检查
```sql
-- 检查长期未登录用户
SELECT username, last_login_at, status
FROM users 
WHERE last_login_at < NOW() - INTERVAL 90 DAY
   OR last_login_at IS NULL;

-- 检查管理员账户
SELECT username, status, created_at
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE r.code = 'admin';

-- 检查禁用账户
SELECT username, status, updated_at
FROM users 
WHERE status = 'inactive';
```

#### 4.2 密码安全维护
```sql
-- 检查密码最后更新时间
SELECT u.username, up.updated_at as password_updated
FROM users u
JOIN user_passwords up ON u.id = up.user_id
WHERE up.updated_at < NOW() - INTERVAL 90 DAY;
```

#### 4.3 会话管理
```sql
-- 查看活跃会话
SELECT u.username, s.created_at, s.expires_at, s.ip_address
FROM user_sessions s
JOIN users u ON s.user_id = u.id
WHERE s.expires_at > NOW()
ORDER BY s.created_at DESC;

-- 强制结束所有会话（紧急情况）
DELETE FROM user_sessions;
```

### 5. 系统安全

#### 5.1 SSL证书维护
```bash
# 检查证书有效期
sudo certbot certificates

# 手动续期证书
sudo certbot renew

# 测试证书配置
sudo nginx -t
```

#### 5.2 防火墙检查
```bash
# 检查防火墙状态
sudo ufw status

# 查看防火墙日志
sudo tail -f /var/log/ufw.log

# 检查失败登录尝试
sudo grep "Failed password" /var/log/auth.log | tail -10
```

#### 5.3 系统更新
```bash
# 更新系统包
sudo apt update && sudo apt upgrade

# 更新Node.js依赖
npm audit
npm audit fix

# 检查安全漏洞
npm audit --audit-level high
```

## 性能维护

### 6. 性能监控

#### 6.1 应用性能
```bash
# 运行性能测试
node scripts/performance-test.js

# 检查内存使用
pm2 show erp-system

# 监控CPU使用
top -p $(pgrep -f "erp-system")
```

#### 6.2 数据库性能
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log 
ORDER BY start_time DESC 
LIMIT 10;

-- 检查索引使用情况
SHOW INDEX FROM users;
SHOW INDEX FROM user_sessions;

-- 查看查询缓存状态
SHOW STATUS LIKE 'Qcache%';
```

#### 6.3 网络性能
```bash
# 检查网络连接
ss -tulpn | grep :3000

# 测试响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3000/api/health"

# 检查Nginx状态
curl http://localhost/nginx_status
```

### 7. 容量规划

#### 7.1 存储空间监控
```bash
# 检查磁盘使用趋势
df -h | grep -E "(/$|/var)"

# 查看大文件
find /var/log -type f -size +100M -ls

# 检查数据库大小增长
du -sh /var/lib/mysql/erp_system/
```

#### 7.2 用户增长监控
```sql
-- 用户增长统计
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_users
FROM users 
WHERE created_at >= NOW() - INTERVAL 30 DAY
GROUP BY DATE(created_at)
ORDER BY date;

-- 活跃用户统计
SELECT 
    DATE(last_login_at) as date,
    COUNT(DISTINCT user_id) as active_users
FROM user_sessions 
WHERE created_at >= NOW() - INTERVAL 30 DAY
GROUP BY DATE(last_login_at)
ORDER BY date;
```

## 故障处理

### 8. 常见问题处理

#### 8.1 应用无响应
```bash
# 检查进程状态
pm2 status

# 重启应用
pm2 restart erp-system

# 查看错误日志
pm2 logs erp-system --err --lines 50

# 检查端口占用
netstat -tulpn | grep :3000
```

#### 8.2 数据库连接问题
```bash
# 测试数据库连接
mysql -u erp_user -p -h localhost erp_system

# 检查数据库状态
sudo systemctl status mysql

# 重启数据库服务
sudo systemctl restart mysql

# 检查数据库错误日志
sudo tail -f /var/log/mysql/error.log
```

#### 8.3 内存不足
```bash
# 检查内存使用
free -h

# 查看进程内存使用
ps aux --sort=-%mem | head -10

# 重启应用释放内存
pm2 restart erp-system

# 清理系统缓存
sudo sync && sudo sysctl vm.drop_caches=3
```

#### 8.4 磁盘空间不足
```bash
# 清理日志文件
sudo find /var/log -name "*.log" -mtime +7 -delete

# 清理临时文件
sudo rm -rf /tmp/*

# 清理包缓存
sudo apt clean

# 分析磁盘使用
sudo du -sh /* | sort -hr
```

### 9. 紧急恢复

#### 9.1 数据库恢复
```bash
# 从备份恢复数据库
mysql -u erp_user -p erp_system < backup_20250101.sql

# 验证数据完整性
mysql -u erp_user -p -e "SELECT COUNT(*) FROM erp_system.users;"

# 重启应用
pm2 restart erp-system
```

#### 9.2 应用回滚
```bash
# 切换到上一个版本
git checkout previous-stable-tag

# 重新构建
npm run build

# 重启应用
pm2 restart erp-system
```

#### 9.3 系统恢复
```bash
# 恢复配置文件
sudo cp /var/backups/erp/nginx.conf.backup /etc/nginx/sites-available/erp-system

# 重启服务
sudo systemctl restart nginx
sudo systemctl restart mysql
pm2 restart erp-system
```

## 维护计划

### 10. 定期维护任务

#### 10.1 每日任务
- [ ] 检查应用运行状态
- [ ] 查看错误日志
- [ ] 监控系统资源使用
- [ ] 检查备份任务执行

#### 10.2 每周任务
- [ ] 数据库性能检查
- [ ] 清理过期会话和日志
- [ ] 检查SSL证书状态
- [ ] 运行安全扫描

#### 10.3 每月任务
- [ ] 系统安全更新
- [ ] 数据库优化
- [ ] 性能基准测试
- [ ] 容量规划评估
- [ ] 备份恢复测试

#### 10.4 每季度任务
- [ ] 全面安全审计
- [ ] 灾难恢复演练
- [ ] 系统架构评估
- [ ] 用户访问权限审查

## 监控告警

### 11. 告警配置

#### 11.1 系统告警
```bash
# CPU使用率超过80%
if [ $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1) -gt 80 ]; then
    echo "CPU使用率过高" | mail -s "系统告警" <EMAIL>
fi

# 内存使用率超过90%
if [ $(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}') -gt 90 ]; then
    echo "内存使用率过高" | mail -s "系统告警" <EMAIL>
fi

# 磁盘使用率超过85%
if [ $(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1) -gt 85 ]; then
    echo "磁盘空间不足" | mail -s "系统告警" <EMAIL>
fi
```

#### 11.2 应用告警
```bash
# 检查应用是否运行
if ! pm2 list | grep -q "erp-system.*online"; then
    echo "ERP应用已停止" | mail -s "应用告警" <EMAIL>
fi

# 检查数据库连接
if ! mysqladmin ping -h localhost -u erp_user -p$DB_PASSWORD; then
    echo "数据库连接失败" | mail -s "数据库告警" <EMAIL>
fi
```

## 联系信息

### 12. 技术支持

**系统管理员**: 
- 邮箱: <EMAIL>
- 电话: xxx-xxxx-xxxx

**技术支持**:
- 邮箱: <EMAIL>
- 工作时间: 周一至周五 9:00-18:00

**紧急联系**:
- 24小时热线: xxx-xxxx-xxxx
- 紧急邮箱: <EMAIL>

---

## 维护记录模板

### 维护记录表
| 日期 | 维护类型 | 执行人 | 描述 | 结果 | 备注 |
|------|----------|--------|------|------|------|
| 2025-01-01 | 日常检查 | 管理员 | 系统状态检查 | 正常 | 无异常 |
| 2025-01-02 | 数据库维护 | 管理员 | 清理过期会话 | 完成 | 清理1000条记录 |

---

## 自动化维护脚本

### 13. 维护脚本使用

#### 13.1 日常维护脚本
```bash
# 运行日常维护检查
./scripts/daily-maintenance.sh

# 运行数据库清理
./scripts/cleanup-database.sh

# 运行日志清理
./scripts/cleanup-logs.sh
```

#### 13.2 健康检查脚本
```bash
# 运行系统健康检查
./scripts/health-check.sh

# 运行性能检查
./scripts/performance-check.sh

# 运行安全检查
./scripts/security-check.sh
```

#### 13.3 备份脚本
```bash
# 运行完整备份
./scripts/backup-full.sh

# 运行增量备份
./scripts/backup-incremental.sh

# 验证备份完整性
./scripts/verify-backup.sh
```

---

*维护手册版本: v1.0*
*最后更新: 2025年1月*
