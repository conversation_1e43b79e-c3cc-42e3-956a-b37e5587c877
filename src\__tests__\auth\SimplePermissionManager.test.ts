/**
 * 简化权限管理器测试
 * 
 * 测试PRD要求的极简版权限管理功能
 * 包括权限检查、角色区分、权限级别比较等
 */

import { SimplePermissionManager } from '@/utils/auth/SimplePermissionManager'
import { User } from '@/types/auth'

describe('SimplePermissionManager', () => {
  // 测试用户数据
  const adminUser: User = {
    id: 'admin-1',
    username: 'admin',
    email: '<EMAIL>',
    fullName: '系统管理员',
    status: 'active',
    roles: [
      {
        id: 'role-admin',
        code: 'admin',
        name: '系统管理员',
        description: '系统管理员角色',
        permissions: []
      }
    ],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }

  const employeeUser: User = {
    id: 'employee-1',
    username: 'employee',
    email: '<EMAIL>',
    fullName: '普通员工',
    status: 'active',
    roles: [
      {
        id: 'role-employee',
        code: 'employee',
        name: '普通员工',
        description: '普通员工角色',
        permissions: []
      }
    ],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }

  const systemAdminUser: User = {
    id: 'system-admin-1',
    username: 'system_admin',
    email: '<EMAIL>',
    fullName: '系统超级管理员',
    status: 'active',
    roles: [
      {
        id: 'role-system-admin',
        code: 'system_admin',
        name: '系统超级管理员',
        description: '系统超级管理员角色',
        permissions: []
      }
    ],
    createdAt: '2025-01-01T00:00:00Z',
    updatedAt: '2025-01-01T00:00:00Z'
  }

  describe('getUserPermissionLevel', () => {
    it('应该正确识别管理员用户', () => {
      const level = SimplePermissionManager.getUserPermissionLevel(adminUser)
      expect(level).toBe('admin')
    })

    it('应该正确识别系统管理员用户', () => {
      const level = SimplePermissionManager.getUserPermissionLevel(systemAdminUser)
      expect(level).toBe('admin')
    })

    it('应该正确识别员工用户', () => {
      const level = SimplePermissionManager.getUserPermissionLevel(employeeUser)
      expect(level).toBe('employee')
    })

    it('应该正确处理包含管理员字样的角色名', () => {
      const managerUser: User = {
        ...employeeUser,
        roles: [
          {
            id: 'role-manager',
            code: 'manager',
            name: '部门管理员',
            description: '部门管理员角色',
            permissions: []
          }
        ]
      }

      const level = SimplePermissionManager.getUserPermissionLevel(managerUser)
      expect(level).toBe('admin')
    })
  })

  describe('checkPermission', () => {
    it('管理员应该拥有所有权限', () => {
      const result = SimplePermissionManager.checkPermission(adminUser, 'admin:users:create')
      
      expect(result.hasPermission).toBe(true)
      expect(result.userLevel).toBe('admin')
      expect(result.reason).toContain('管理员拥有所有权限')
    })

    it('管理员应该拥有任意权限', () => {
      const result = SimplePermissionManager.checkPermission(adminUser, 'any:random:permission')
      
      expect(result.hasPermission).toBe(true)
      expect(result.userLevel).toBe('admin')
    })

    it('员工应该拥有基础权限', () => {
      const result = SimplePermissionManager.checkPermission(employeeUser, 'data:view')
      
      expect(result.hasPermission).toBe(true)
      expect(result.userLevel).toBe('employee')
    })

    it('员工应该拥有员工级别权限', () => {
      const result = SimplePermissionManager.checkPermission(employeeUser, 'employee:data:edit')
      
      expect(result.hasPermission).toBe(true)
      expect(result.userLevel).toBe('employee')
    })

    it('员工不应该拥有管理员权限', () => {
      const result = SimplePermissionManager.checkPermission(employeeUser, 'admin:users:create')
      
      expect(result.hasPermission).toBe(false)
      expect(result.userLevel).toBe('employee')
      expect(result.reason).toContain('不满足权限要求')
    })

    it('员工不应该拥有系统配置权限', () => {
      const result = SimplePermissionManager.checkPermission(employeeUser, 'system:config')
      
      expect(result.hasPermission).toBe(false)
      expect(result.userLevel).toBe('employee')
    })
  })

  describe('getRequiredPermissionLevel', () => {
    it('应该正确识别管理员权限', () => {
      expect(SimplePermissionManager.getRequiredPermissionLevel('admin:users:create')).toBe('admin')
      expect(SimplePermissionManager.getRequiredPermissionLevel('admin:*')).toBe('admin')
      expect(SimplePermissionManager.getRequiredPermissionLevel('system:config')).toBe('admin')
    })

    it('应该正确识别员工权限', () => {
      expect(SimplePermissionManager.getRequiredPermissionLevel('data:view')).toBe('employee')
      expect(SimplePermissionManager.getRequiredPermissionLevel('employee:data:edit')).toBe('employee')
      expect(SimplePermissionManager.getRequiredPermissionLevel('orders:create')).toBe('employee')
    })

    it('应该为未知权限返回默认员工级别', () => {
      expect(SimplePermissionManager.getRequiredPermissionLevel('unknown:permission')).toBe('employee')
    })

    it('应该正确处理通配符权限', () => {
      expect(SimplePermissionManager.getRequiredPermissionLevel('admin:users:delete')).toBe('admin')
      expect(SimplePermissionManager.getRequiredPermissionLevel('admin:roles:manage')).toBe('admin')
    })
  })

  describe('comparePermissionLevels', () => {
    it('管理员级别应该满足管理员要求', () => {
      const result = SimplePermissionManager.comparePermissionLevels('admin', 'admin')
      expect(result).toBe(true)
    })

    it('管理员级别应该满足员工要求', () => {
      const result = SimplePermissionManager.comparePermissionLevels('admin', 'employee')
      expect(result).toBe(true)
    })

    it('员工级别应该满足员工要求', () => {
      const result = SimplePermissionManager.comparePermissionLevels('employee', 'employee')
      expect(result).toBe(true)
    })

    it('员工级别不应该满足管理员要求', () => {
      const result = SimplePermissionManager.comparePermissionLevels('employee', 'admin')
      expect(result).toBe(false)
    })
  })

  describe('isAdmin', () => {
    it('应该正确识别管理员用户', () => {
      expect(SimplePermissionManager.isAdmin(adminUser)).toBe(true)
      expect(SimplePermissionManager.isAdmin(systemAdminUser)).toBe(true)
    })

    it('应该正确识别非管理员用户', () => {
      expect(SimplePermissionManager.isAdmin(employeeUser)).toBe(false)
    })
  })

  describe('isEmployee', () => {
    it('应该正确识别员工用户', () => {
      expect(SimplePermissionManager.isEmployee(employeeUser)).toBe(true)
    })

    it('管理员不应该被识别为员工（严格区分）', () => {
      expect(SimplePermissionManager.isEmployee(adminUser)).toBe(false)
    })
  })

  describe('getUserAccessibleFeatures', () => {
    it('管理员应该拥有所有功能', () => {
      const features = SimplePermissionManager.getUserAccessibleFeatures(adminUser)
      
      expect(features).toContain('profile:view')
      expect(features).toContain('data:view')
      expect(features).toContain('admin:users:create')
      expect(features).toContain('system:config')
      expect(features).toContain('data:delete')
    })

    it('员工应该只拥有基础功能', () => {
      const features = SimplePermissionManager.getUserAccessibleFeatures(employeeUser)
      
      expect(features).toContain('profile:view')
      expect(features).toContain('data:view')
      expect(features).toContain('orders:create')
      expect(features).toContain('production:view')
      
      // 不应该有管理员功能
      expect(features).not.toContain('admin:users:create')
      expect(features).not.toContain('system:config')
      expect(features).not.toContain('data:delete')
    })
  })

  describe('getPermissionLevelDescription', () => {
    it('应该返回正确的权限级别描述', () => {
      expect(SimplePermissionManager.getPermissionLevelDescription('admin')).toBe('系统管理员')
      expect(SimplePermissionManager.getPermissionLevelDescription('employee')).toBe('普通员工')
    })
  })

  describe('validatePermissionConfig', () => {
    beforeEach(() => {
      // Mock console.log to avoid test output
      jest.spyOn(console, 'log').mockImplementation(() => {})
    })

    afterEach(() => {
      jest.restoreAllMocks()
    })

    it('应该验证权限配置的完整性', () => {
      const result = SimplePermissionManager.validatePermissionConfig()
      
      expect(result.isValid).toBe(true)
      expect(result.warnings).toHaveLength(0)
    })

    it('应该记录权限配置信息', () => {
      SimplePermissionManager.validatePermissionConfig()
      
      expect(console.log).toHaveBeenCalledWith('🔒 [SimplePermissionManager] 权限配置验证完成')
    })
  })

  describe('边界情况测试', () => {
    it('应该处理空角色的用户', () => {
      const userWithoutRoles: User = {
        ...employeeUser,
        roles: []
      }

      const level = SimplePermissionManager.getUserPermissionLevel(userWithoutRoles)
      expect(level).toBe('employee')
    })

    it('应该处理多个角色的用户', () => {
      const userWithMultipleRoles: User = {
        ...employeeUser,
        roles: [
          {
            id: 'role-employee',
            code: 'employee',
            name: '普通员工',
            description: '普通员工角色',
            permissions: []
          },
          {
            id: 'role-admin',
            code: 'admin',
            name: '系统管理员',
            description: '系统管理员角色',
            permissions: []
          }
        ]
      }

      const level = SimplePermissionManager.getUserPermissionLevel(userWithMultipleRoles)
      expect(level).toBe('admin') // 应该取最高权限
    })

    it('应该处理特殊字符的权限代码', () => {
      const result = SimplePermissionManager.checkPermission(adminUser, 'special:permission-with_underscore.dot')
      expect(result.hasPermission).toBe(true) // 管理员应该有所有权限
    })

    it('应该处理空权限代码', () => {
      const result = SimplePermissionManager.checkPermission(employeeUser, '')
      expect(result.hasPermission).toBe(true) // 空权限应该默认允许（员工级别）
    })
  })
})
