"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createStore: () => (/* reexport safe */ zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   \"default\": () => (/* binding */ react),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n\n\n\n\nconst { useDebugValue } = react__WEBPACK_IMPORTED_MODULE_1__;\nconst { useSyncExternalStoreWithSelector } = use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg)=>arg;\nfunction useStore(api, selector = identity, equalityFn) {\n    if (( false ? 0 : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n        console.warn(\"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\");\n        didWarnAboutEqualityFn = true;\n    }\n    const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getServerState || api.getInitialState, selector, equalityFn);\n    useDebugValue(slice);\n    return slice;\n}\nconst createImpl = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\" && typeof createState !== \"function\") {\n        console.warn(\"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\");\n    }\n    const api = typeof createState === \"function\" ? (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore)(createState) : createState;\n    const useBoundStore = (selector, equalityFn)=>useStore(api, selector, equalityFn);\n    Object.assign(useBoundStore, api);\n    return useBoundStore;\n};\nconst create = (createState)=>createState ? createImpl(createState) : createImpl;\nvar react = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\");\n    }\n    return create(createState);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial)=>(set, _get, api)=>{\n        api.dispatch = (action)=>{\n            set((state)=>reducer(state, action), false, action);\n            return action;\n        };\n        api.dispatchFromDevtools = true;\n        return {\n            dispatch: (...a)=>api.dispatch(...a),\n            ...initial\n        };\n    };\nconst redux = reduxImpl;\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name)=>{\n    const api = trackedConnections.get(name);\n    if (!api) return {};\n    return Object.fromEntries(Object.entries(api.stores).map(([key, api2])=>[\n            key,\n            api2.getState()\n        ]));\n};\nconst extractConnectionInformation = (store, extensionConnector, options)=>{\n    if (store === void 0) {\n        return {\n            type: \"untracked\",\n            connection: extensionConnector.connect(options)\n        };\n    }\n    const existingConnection = trackedConnections.get(options.name);\n    if (existingConnection) {\n        return {\n            type: \"tracked\",\n            store,\n            ...existingConnection\n        };\n    }\n    const newConnection = {\n        connection: extensionConnector.connect(options),\n        stores: {}\n    };\n    trackedConnections.set(options.name, newConnection);\n    return {\n        type: \"tracked\",\n        store,\n        ...newConnection\n    };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {})=>(set, get, api)=>{\n        const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n        let extensionConnector;\n        try {\n            extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n        } catch (_e) {}\n        if (!extensionConnector) {\n            if (( false ? 0 : void 0) !== \"production\" && enabled) {\n                console.warn(\"[zustand devtools middleware] Please install/enable Redux devtools extension\");\n            }\n            return fn(set, get, api);\n        }\n        const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n        let isRecording = true;\n        api.setState = (state, replace, nameOrAction)=>{\n            const r = set(state, replace);\n            if (!isRecording) return r;\n            const action = nameOrAction === void 0 ? {\n                type: anonymousActionType || \"anonymous\"\n            } : typeof nameOrAction === \"string\" ? {\n                type: nameOrAction\n            } : nameOrAction;\n            if (store === void 0) {\n                connection == null ? void 0 : connection.send(action, get());\n                return r;\n            }\n            connection == null ? void 0 : connection.send({\n                ...action,\n                type: `${store}/${action.type}`\n            }, {\n                ...getTrackedConnectionState(options.name),\n                [store]: api.getState()\n            });\n            return r;\n        };\n        const setStateFromDevtools = (...a)=>{\n            const originalIsRecording = isRecording;\n            isRecording = false;\n            set(...a);\n            isRecording = originalIsRecording;\n        };\n        const initialState = fn(api.setState, get, api);\n        if (connectionInformation.type === \"untracked\") {\n            connection == null ? void 0 : connection.init(initialState);\n        } else {\n            connectionInformation.stores[connectionInformation.store] = api;\n            connection == null ? void 0 : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(([key, store2])=>[\n                    key,\n                    key === connectionInformation.store ? initialState : store2.getState()\n                ])));\n        }\n        if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n            let didWarnAboutReservedActionType = false;\n            const originalDispatch = api.dispatch;\n            api.dispatch = (...a)=>{\n                if (( false ? 0 : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n                    console.warn('[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.');\n                    didWarnAboutReservedActionType = true;\n                }\n                originalDispatch(...a);\n            };\n        }\n        connection.subscribe((message)=>{\n            var _a;\n            switch(message.type){\n                case \"ACTION\":\n                    if (typeof message.payload !== \"string\") {\n                        console.error(\"[zustand devtools middleware] Unsupported action format\");\n                        return;\n                    }\n                    return parseJsonThen(message.payload, (action)=>{\n                        if (action.type === \"__setState\") {\n                            if (store === void 0) {\n                                setStateFromDevtools(action.state);\n                                return;\n                            }\n                            if (Object.keys(action.state).length !== 1) {\n                                console.error(`\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `);\n                            }\n                            const stateFromDevtools = action.state[store];\n                            if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                                return;\n                            }\n                            if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                                setStateFromDevtools(stateFromDevtools);\n                            }\n                            return;\n                        }\n                        if (!api.dispatchFromDevtools) return;\n                        if (typeof api.dispatch !== \"function\") return;\n                        api.dispatch(action);\n                    });\n                case \"DISPATCH\":\n                    switch(message.payload.type){\n                        case \"RESET\":\n                            setStateFromDevtools(initialState);\n                            if (store === void 0) {\n                                return connection == null ? void 0 : connection.init(api.getState());\n                            }\n                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                        case \"COMMIT\":\n                            if (store === void 0) {\n                                connection == null ? void 0 : connection.init(api.getState());\n                                return;\n                            }\n                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                        case \"ROLLBACK\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === void 0) {\n                                    setStateFromDevtools(state);\n                                    connection == null ? void 0 : connection.init(api.getState());\n                                    return;\n                                }\n                                setStateFromDevtools(state[store]);\n                                connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                            });\n                        case \"JUMP_TO_STATE\":\n                        case \"JUMP_TO_ACTION\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === void 0) {\n                                    setStateFromDevtools(state);\n                                    return;\n                                }\n                                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                                    setStateFromDevtools(state[store]);\n                                }\n                            });\n                        case \"IMPORT_STATE\":\n                            {\n                                const { nextLiftedState } = message.payload;\n                                const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n                                if (!lastComputedState) return;\n                                if (store === void 0) {\n                                    setStateFromDevtools(lastComputedState);\n                                } else {\n                                    setStateFromDevtools(lastComputedState[store]);\n                                }\n                                connection == null ? void 0 : connection.send(null, // FIXME no-any\n                                nextLiftedState);\n                                return;\n                            }\n                        case \"PAUSE_RECORDING\":\n                            return isRecording = !isRecording;\n                    }\n                    return;\n            }\n        });\n        return initialState;\n    };\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f)=>{\n    let parsed;\n    try {\n        parsed = JSON.parse(stringified);\n    } catch (e) {\n        console.error(\"[zustand devtools middleware] Could not parse the received json\", e);\n    }\n    if (parsed !== void 0) f(parsed);\n};\nconst subscribeWithSelectorImpl = (fn)=>(set, get, api)=>{\n        const origSubscribe = api.subscribe;\n        api.subscribe = (selector, optListener, options)=>{\n            let listener = selector;\n            if (optListener) {\n                const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n                let currentSlice = selector(api.getState());\n                listener = (state)=>{\n                    const nextSlice = selector(state);\n                    if (!equalityFn(currentSlice, nextSlice)) {\n                        const previousSlice = currentSlice;\n                        optListener(currentSlice = nextSlice, previousSlice);\n                    }\n                };\n                if (options == null ? void 0 : options.fireImmediately) {\n                    optListener(currentSlice, currentSlice);\n                }\n            }\n            return origSubscribe(listener);\n        };\n        const initialState = fn(set, get, api);\n        return initialState;\n    };\nconst subscribeWithSelector = subscribeWithSelectorImpl;\nconst combine = (initialState, create)=>(...a)=>Object.assign({}, initialState, create(...a));\nfunction createJSONStorage(getStorage, options) {\n    let storage;\n    try {\n        storage = getStorage();\n    } catch (_e) {\n        return;\n    }\n    const persistStorage = {\n        getItem: (name)=>{\n            var _a;\n            const parse = (str2)=>{\n                if (str2 === null) {\n                    return null;\n                }\n                return JSON.parse(str2, options == null ? void 0 : options.reviver);\n            };\n            const str = (_a = storage.getItem(name)) != null ? _a : null;\n            if (str instanceof Promise) {\n                return str.then(parse);\n            }\n            return parse(str);\n        },\n        setItem: (name, newValue)=>storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n        removeItem: (name)=>storage.removeItem(name)\n    };\n    return persistStorage;\n}\nconst toThenable = (fn)=>(input)=>{\n        try {\n            const result = fn(input);\n            if (result instanceof Promise) {\n                return result;\n            }\n            return {\n                then (onFulfilled) {\n                    return toThenable(onFulfilled)(result);\n                },\n                catch (_onRejected) {\n                    return this;\n                }\n            };\n        } catch (e) {\n            return {\n                then (_onFulfilled) {\n                    return this;\n                },\n                catch (onRejected) {\n                    return toThenable(onRejected)(e);\n                }\n            };\n        }\n    };\nconst oldImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            getStorage: ()=>localStorage,\n            serialize: JSON.stringify,\n            deserialize: JSON.parse,\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage;\n        try {\n            storage = options.getStorage();\n        } catch (_e) {}\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const thenableSerialize = toThenable(options.serialize);\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            let errorInSync;\n            const thenable = thenableSerialize({\n                state,\n                version: options.version\n            }).then((serializedValue)=>storage.setItem(options.name, serializedValue)).catch((e)=>{\n                errorInSync = e;\n            });\n            if (errorInSync) {\n                throw errorInSync;\n            }\n            return thenable;\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>cb(get()));\n            const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue)=>{\n                if (storageValue) {\n                    return options.deserialize(storageValue);\n                }\n            }).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            return options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return deserializedStorageValue.state;\n                    }\n                }\n            }).then((migratedState)=>{\n                var _a2;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                return setItem();\n            }).then(()=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.getStorage) {\n                    storage = newOptions.getStorage();\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? void 0 : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        hydrate();\n        return stateFromStorage || configResult;\n    };\nconst newImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            storage: createJSONStorage(()=>localStorage),\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage = options.storage;\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            return storage.setItem(options.name, {\n                state,\n                version: options.version\n            });\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        api.getInitialState = ()=>configResult;\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a, _b;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>{\n                var _a2;\n                return cb((_a2 = get()) != null ? _a2 : configResult);\n            });\n            const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            return [\n                                true,\n                                options.migrate(deserializedStorageValue.state, deserializedStorageValue.version)\n                            ];\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return [\n                            false,\n                            deserializedStorageValue.state\n                        ];\n                    }\n                }\n                return [\n                    false,\n                    void 0\n                ];\n            }).then((migrationResult)=>{\n                var _a2;\n                const [migrated, migratedState] = migrationResult;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                if (migrated) {\n                    return setItem();\n                }\n            }).then(()=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n                stateFromStorage = get();\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.storage) {\n                    storage = newOptions.storage;\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? void 0 : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        if (!options.skipHydration) {\n            hydrate();\n        }\n        return stateFromStorage || configResult;\n    };\nconst persistImpl = (config, baseOptions)=>{\n    if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n        if (( false ? 0 : void 0) !== \"production\") {\n            console.warn(\"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\");\n        }\n        return oldImpl(config, baseOptions);\n    }\n    return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   \"default\": () => (/* binding */ vanilla)\n/* harmony export */ });\nconst createStoreImpl = (createState)=>{\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (!Object.is(nextState, state)) {\n            const previousState = state;\n            state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const getInitialState = ()=>initialState;\n    const subscribe = (listener)=>{\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const destroy = ()=>{\n        if (( false ? 0 : void 0) !== \"production\") {\n            console.warn(\"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\");\n        }\n        listeners.clear();\n    };\n    const api = {\n        setState,\n        getState,\n        getInitialState,\n        subscribe,\n        destroy\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n};\nconst createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\");\n    }\n    return createStore(createState);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;