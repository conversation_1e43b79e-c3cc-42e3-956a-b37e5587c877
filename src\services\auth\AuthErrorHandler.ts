/**
 * 认证错误处理服务
 * 
 * 统一管理所有认证相关的错误处理和响应格式
 * 确保错误信息的一致性和安全性
 */

import { NextResponse } from 'next/server'
import { TokenValidator } from './TokenValidator'

/**
 * 认证错误代码枚举
 */
export enum AuthErrorCode {
  // Token相关错误
  TOKEN_NOT_FOUND = 'TOKEN_NOT_FOUND',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_GENERATION_ERROR = 'TOKEN_GENERATION_ERROR',
  
  // 认证相关错误
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  
  // 账户相关错误
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_INACTIVE = 'ACCOUNT_INACTIVE',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  
  // 请求相关错误
  INVALID_REQUEST = 'INVALID_REQUEST',
  TOO_MANY_ATTEMPTS = 'TOO_MANY_ATTEMPTS',
  
  // 系统错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

/**
 * 错误响应接口
 */
export interface AuthErrorResponse {
  status: 'error'
  data: null
  message: string
  code: AuthErrorCode
  timestamp: string
  requestId: string
}

/**
 * 认证错误处理器
 */
export class AuthErrorHandler {
  /**
   * 错误代码到HTTP状态码的映射
   */
  private static readonly ERROR_STATUS_MAP: Record<AuthErrorCode, number> = {
    [AuthErrorCode.TOKEN_NOT_FOUND]: 400,
    [AuthErrorCode.TOKEN_INVALID]: 401,
    [AuthErrorCode.TOKEN_EXPIRED]: 401,
    [AuthErrorCode.TOKEN_GENERATION_ERROR]: 500,
    [AuthErrorCode.INVALID_CREDENTIALS]: 401,
    [AuthErrorCode.UNAUTHORIZED]: 401,
    [AuthErrorCode.FORBIDDEN]: 403,
    [AuthErrorCode.ACCOUNT_LOCKED]: 403,
    [AuthErrorCode.ACCOUNT_INACTIVE]: 403,
    [AuthErrorCode.USER_NOT_FOUND]: 401,
    [AuthErrorCode.INVALID_REQUEST]: 400,
    [AuthErrorCode.TOO_MANY_ATTEMPTS]: 429,
    [AuthErrorCode.INTERNAL_SERVER_ERROR]: 500,
    [AuthErrorCode.SERVICE_UNAVAILABLE]: 503
  }

  /**
   * 错误代码到用户友好消息的映射
   */
  private static readonly ERROR_MESSAGE_MAP: Record<AuthErrorCode, string> = {
    [AuthErrorCode.TOKEN_NOT_FOUND]: 'Token不存在',
    [AuthErrorCode.TOKEN_INVALID]: 'Token无效或已过期',
    [AuthErrorCode.TOKEN_EXPIRED]: 'Token已过期',
    [AuthErrorCode.TOKEN_GENERATION_ERROR]: 'Token生成失败，请稍后重试',
    [AuthErrorCode.INVALID_CREDENTIALS]: '用户名或密码错误',
    [AuthErrorCode.UNAUTHORIZED]: '未授权访问',
    [AuthErrorCode.FORBIDDEN]: '权限不足',
    [AuthErrorCode.ACCOUNT_LOCKED]: '账户已被锁定',
    [AuthErrorCode.ACCOUNT_INACTIVE]: '账户状态异常',
    [AuthErrorCode.USER_NOT_FOUND]: '用户不存在',
    [AuthErrorCode.INVALID_REQUEST]: '请求参数无效',
    [AuthErrorCode.TOO_MANY_ATTEMPTS]: '请求过于频繁，请稍后重试',
    [AuthErrorCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
    [AuthErrorCode.SERVICE_UNAVAILABLE]: '服务暂时不可用'
  }

  /**
   * 创建标准错误响应
   */
  static createErrorResponse(
    code: AuthErrorCode,
    customMessage?: string
  ): AuthErrorResponse {
    return {
      status: 'error',
      data: null,
      message: customMessage || this.ERROR_MESSAGE_MAP[code],
      code,
      timestamp: new Date().toISOString(),
      requestId: TokenValidator.generateRequestId()
    }
  }

  /**
   * 创建NextResponse错误响应
   */
  static createNextErrorResponse(
    code: AuthErrorCode,
    customMessage?: string
  ): NextResponse {
    const errorResponse = this.createErrorResponse(code, customMessage)
    const statusCode = this.ERROR_STATUS_MAP[code]
    
    return NextResponse.json(errorResponse, { status: statusCode })
  }

  /**
   * 获取错误对应的HTTP状态码
   */
  static getStatusCode(code: AuthErrorCode): number {
    return this.ERROR_STATUS_MAP[code] || 500
  }

  /**
   * 获取错误对应的用户友好消息
   */
  static getMessage(code: AuthErrorCode): string {
    return this.ERROR_MESSAGE_MAP[code] || '未知错误'
  }

  /**
   * 记录认证错误日志
   */
  static logError(
    code: AuthErrorCode,
    context: string,
    details?: any
  ): void {
    const logLevel = this.getLogLevel(code)
    const message = `[Auth Error] ${context}: ${this.getMessage(code)}`
    
    switch (logLevel) {
      case 'error':
        console.error(message, details)
        break
      case 'warn':
        console.warn(message, details)
        break
      case 'info':
        console.log(message, details)
        break
      default:
        console.log(message, details)
    }
  }

  /**
   * 根据错误代码确定日志级别
   */
  private static getLogLevel(code: AuthErrorCode): 'error' | 'warn' | 'info' {
    switch (code) {
      case AuthErrorCode.INTERNAL_SERVER_ERROR:
      case AuthErrorCode.SERVICE_UNAVAILABLE:
      case AuthErrorCode.TOKEN_GENERATION_ERROR:
        return 'error'
      
      case AuthErrorCode.TOO_MANY_ATTEMPTS:
      case AuthErrorCode.ACCOUNT_LOCKED:
        return 'warn'
      
      default:
        return 'info'
    }
  }

  /**
   * 处理Token验证错误
   */
  static handleTokenValidationError(
    tokenInfo: { valid: boolean; expired?: boolean; error?: string },
    context: string = 'Token验证'
  ): AuthErrorResponse {
    if (!tokenInfo.valid) {
      if (tokenInfo.expired) {
        this.logError(AuthErrorCode.TOKEN_EXPIRED, context)
        return this.createErrorResponse(AuthErrorCode.TOKEN_EXPIRED)
      } else {
        this.logError(AuthErrorCode.TOKEN_INVALID, context, { error: tokenInfo.error })
        return this.createErrorResponse(AuthErrorCode.TOKEN_INVALID, tokenInfo.error)
      }
    }
    
    // 不应该到达这里
    this.logError(AuthErrorCode.INTERNAL_SERVER_ERROR, context, { tokenInfo })
    return this.createErrorResponse(AuthErrorCode.INTERNAL_SERVER_ERROR)
  }

  /**
   * 处理用户认证错误
   */
  static handleAuthenticationError(
    error: any,
    context: string = '用户认证'
  ): AuthErrorResponse {
    if (error && typeof error === 'object' && 'code' in error) {
      const code = error.code as AuthErrorCode
      this.logError(code, context, error)
      return this.createErrorResponse(code, error.message)
    }
    
    // 未知错误，记录详细信息
    this.logError(AuthErrorCode.INTERNAL_SERVER_ERROR, context, error)
    return this.createErrorResponse(AuthErrorCode.INTERNAL_SERVER_ERROR)
  }

  /**
   * 安全地记录敏感信息（过滤密码等）
   */
  static sanitizeLogData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data
    }

    const sanitized = { ...data }
    const sensitiveFields = ['password', 'token', 'secret', 'key']
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]'
      }
    }
    
    return sanitized
  }
}
