/**
 * 统一数据访问管理器
 * 提供单一入口点访问所有数据服务，实现标准化的跨模块数据调用
 */

import { ProductDataAccessService } from './ProductDataAccessService'
import { CustomerDataAccessService } from './CustomerDataAccessService'
import { EmployeeDataAccessService } from './EmployeeDataAccessService'
import { InventoryDataAccessService } from './InventoryDataAccessService'
import { OrderDataAccessService } from './OrderDataAccessService'
import { WorkTimeDataAccessService } from './WorkTimeDataAccessService'
import { ProductionOrderDataAccessService } from './ProductionOrderDataAccessService'
import { ProductionWorkOrderDataAccessService } from './ProductionWorkOrderDataAccessService'
import { WorkstationDataAccessService } from './WorkstationDataAccessService'
import { CostCalculationDataAccessService } from './CostCalculationDataAccessService'
import { AuthDataAccessService, IAuthDataAccess } from './AuthDataAccessService'
import { RoleDataAccessService, IRoleDataAccess } from './RoleDataAccessService'
import { TokenManagementService } from './TokenManagementService'
import { dataChangeNotifier } from './DataChangeNotifier'
// ✅ 架构合规：已移除独立CacheStrategyManager，使用内置缓存系统
import { performanceOptimizer } from './PerformanceOptimizer'
import { dataAccessPerformanceMonitor } from './DataAccessPerformanceMonitor'
// ✅ 架构合规：移除违规的独立缓存管理器，使用内置缓存
import { batchOperationController } from '@/utils/concurrencyControl'

/**
 * 性能监控指标接口
 */
export interface PerformanceMetrics {
  totalCalls: number
  successCalls: number
  errorCalls: number
  averageResponseTime: number
  minResponseTime: number
  maxResponseTime: number
  cacheHitRate: number
  slowQueries: Array<{
    method: string
    params: any
    duration: number
    timestamp: number
    cached?: boolean
  }>
  methodStats: Map<string, {
    calls: number
    totalTime: number
    averageTime: number
    successRate: number
    lastCall: number
  }>
  hourlyStats: Array<{
    hour: string
    calls: number
    averageTime: number
    errorRate: number
  }>
}

/**
 * 实时性能监控接口
 */
export interface RealTimeMetrics {
  currentConcurrency: number
  queueLength: number
  recentCalls: Array<{
    method: string
    duration: number
    status: 'success' | 'error'
    timestamp: number
  }>
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical'
    issues: string[]
    recommendations: string[]
  }
}
// 优先级同步服务已删除
import {
  IProductDataAccess,
  ICustomerDataAccess,
  IEmployeeDataAccess,
  IInventoryDataAccess,
  IOrderDataAccess,
  API_VERSION
} from './DataAccessLayer'
import { timestampGenerator } from '@/utils/business'
import { IWorkTimeDataAccess } from './WorkTimeDataAccessService'
import { IProductionOrderDataAccess } from './ProductionOrderDataAccessService'
import { IProductionWorkOrderDataAccess } from './ProductionWorkOrderDataAccessService'
import { IWorkstationDataAccess } from './DataAccessLayer'
import { ICostCalculationDataAccess } from './CostCalculationDataAccessService'

/**
 * 缓存条目接口 - 优化版
 */
interface CacheEntry<T = any> {
  data: T
  expiresAt: number
  accessCount: number
  lastAccessed: number
  createdAt: number
  // ✅ 优化：智能过期策略相关字段
  priority: 'low' | 'medium' | 'high' | 'critical'
  dataType: string
  estimatedSize: number
  accessPattern: 'frequent' | 'occasional' | 'rare'
  refreshable: boolean
}

/**
 * 缓存配置接口 - 优化版
 */
export interface CacheConfig {
  enableCaching: boolean
  defaultTTL: number
  maxSize: number
  strategies: {
    [serviceName: string]: {
      ttl: number
      enabled: boolean
      priority?: 'low' | 'medium' | 'high' | 'critical'
      maxEntries?: number
    }
  }
  monitoring: {
    enabled: boolean
    reportInterval: number
  }
  // ✅ 优化：智能缓存管理配置
  smartEviction: {
    enabled: boolean
    algorithm: 'lru' | 'lfu' | 'adaptive'
    thresholds: {
      memoryWarning: number // 内存使用率警告阈值
      memoryCritical: number // 内存使用率紧急阈值
    }
  }
  // ✅ 优化：缓存预热配置
  prewarming: {
    enabled: boolean
    strategies: string[]
    scheduleInterval: number
  }
}

/**
 * 数据访问管理器配置 - 简化版
 */
export interface DataAccessConfig {
  enableLogging: boolean
  enableCaching: boolean
  defaultTTL: number
  maxCacheSize: number
  retryAttempts: number
  retryDelay: number
}

/**
 * 默认缓存配置 - 优化版
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enableCaching: true,
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxSize: 1000,
  strategies: {
    orders: { ttl: 2 * 60 * 1000, enabled: true, priority: 'high', maxEntries: 200 },      // 2分钟
    products: { ttl: 10 * 60 * 1000, enabled: true, priority: 'medium', maxEntries: 300 },   // 10分钟
    workstations: { ttl: 30 * 1000, enabled: true, priority: 'critical', maxEntries: 50 },    // 30秒
    statistics: { ttl: 30 * 1000, enabled: true, priority: 'low', maxEntries: 100 },      // 30秒
    customers: { ttl: 15 * 60 * 1000, enabled: true, priority: 'medium', maxEntries: 150 },  // 15分钟
    employees: { ttl: 30 * 60 * 1000, enabled: true, priority: 'medium', maxEntries: 100 }   // 30分钟
  },
  monitoring: {
    enabled: true,
    reportInterval: 60000 // 1分钟
  },
  // ✅ 优化：智能缓存管理配置
  smartEviction: {
    enabled: true,
    algorithm: 'adaptive',
    thresholds: {
      memoryWarning: 0.7, // 70%内存使用率警告
      memoryCritical: 0.85 // 85%内存使用率紧急清理
    }
  },
  // ✅ 优化：缓存预热配置
  prewarming: {
    enabled: true,
    strategies: ['workstations', 'orders', 'products'],
    scheduleInterval: 30000 // 30秒预热间隔
  }
}

/**
 * 默认配置 - 简化版
 */
const DEFAULT_CONFIG: DataAccessConfig = {
  enableLogging: true,
  enableCaching: true,
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxCacheSize: 1000,
  retryAttempts: 3,
  retryDelay: 1000 // 1秒
}

/**
 * 数据访问日志记录
 */
interface AccessLog {
  timestamp: string
  service: string
  method: string
  params?: any
  duration: number
  success: boolean
  error?: string
}

/**
 * 缓存统计信息 - 优化版
 */
interface CacheStatistics {
  enabled: boolean
  size: number
  hits: number
  misses: number
  hitRate: number
  totalRequests: number
  // ✅ 优化：新增统计信息
  evictions: number
  prewarmHits: number
  memoryPressure: boolean
  averageEntrySize: number
  priorityDistribution: {
    critical: number
    high: number
    medium: number
    low: number
  }
}

/**
 * 统一数据访问管理器
 */
export class DataAccessManager {
  private static instance: DataAccessManager
  private config: DataAccessConfig
  private accessLogs: AccessLog[] = []
  // ✅ 优化：增强缓存管理属性
  private cache: Map<string, CacheEntry> = new Map()
  private cacheConfig: CacheConfig = DEFAULT_CACHE_CONFIG
  private requestCache = new Map<string, Promise<any>>()
  private cacheStats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    evictions: 0,
    prewarmHits: 0
  }
  // ✅ 优化：智能缓存管理
  private cacheAccessHistory: Map<string, number[]> = new Map() // 访问时间历史
  private prewarmTimer: NodeJS.Timeout | null = null
  private lastMemoryCheck = 0
  private memoryPressure = false

  // 性能监控相关属性
  private performanceMetrics: PerformanceMetrics = {
    totalCalls: 0,
    successCalls: 0,
    errorCalls: 0,
    averageResponseTime: 0,
    minResponseTime: 0,
    maxResponseTime: 0,
    cacheHitRate: 0,
    slowQueries: [],
    methodStats: new Map(),
    hourlyStats: []
  }

  // 统计信息属性 - 修复空指针异常
  private statistics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    cacheHits: 0
  }
  private recentCalls: Array<{
    method: string
    duration: number
    status: 'success' | 'error'
    timestamp: number
    cached?: boolean
  }> = []
  private methodStats: Map<string, {
    calls: number
    totalTime: number
    averageTime: number
    successRate: number
    lastCall: number
    errors: number
  }> = new Map()

  // 🔧 优化：批量性能报告
  private performanceBatch: Array<{
    service: string
    method: string
    duration: number
    success: boolean
    fromCache: boolean
    timestamp: number
  }> = []
  private batchReportTimer: NodeJS.Timeout | null = null
  private readonly BATCH_REPORT_INTERVAL = 5000 // 5秒批量报告一次
  
  // 服务实例
  private productService: IProductDataAccess
  private customerService: ICustomerDataAccess
  private employeeService: IEmployeeDataAccess
  private inventoryService: IInventoryDataAccess
  private orderService: IOrderDataAccess
  private workTimeService: IWorkTimeDataAccess
  private productionOrderService: IProductionOrderDataAccess
  private productionWorkOrderService: IProductionWorkOrderDataAccess
  private workstationService: IWorkstationDataAccess
  private costCalculationService: ICostCalculationDataAccess
  private authService: IAuthDataAccess
  private roleService: IRoleDataAccess
  private tokenManagementService: TokenManagementService

  private constructor(config: Partial<DataAccessConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }

    // ✅ 架构合规：使用内置缓存系统，移除违规的CacheStrategyManager

    // 为DataAccessManager实例添加唯一标识
    ;(this as any).__managerId = Math.random().toString(36).substr(2, 9)
    console.log('✅ [DataAccessManager] 创建实例，ID:', (this as any).__managerId, '内置缓存系统已启用')

    // 初始化内置缓存系统
    if (this.config.enableCaching) {
      console.log('🔧 [DataAccessManager] 内置缓存系统已启用')
      
      // ✅ 优化：启动智能缓存管理
      this.initializeAdvancedCaching()
    } else {
      console.log('🔧 [DataAccessManager] 缓存已禁用')
    }

    // 初始化性能监控
    this.initializePerformanceMonitoring()

    // 初始化服务实例
    this.productService = ProductDataAccessService.getInstance()
    this.customerService = CustomerDataAccessService.getInstance()
    this.employeeService = EmployeeDataAccessService.getInstance()
    this.inventoryService = InventoryDataAccessService.getInstance()
    this.orderService = OrderDataAccessService.getInstance()
    console.log('🔧 [DataAccessManager] 初始化OrderService，管理器ID:', (this as any).__managerId)
    console.log('🔧 [DataAccessManager] OrderService实例ID:', (this.orderService as any).__serviceId)
    this.workTimeService = WorkTimeDataAccessService.getInstance()
    this.productionOrderService = ProductionOrderDataAccessService.getInstance()
    this.productionWorkOrderService = ProductionWorkOrderDataAccessService.getInstance()
    this.workstationService = WorkstationDataAccessService.getInstance()
    this.costCalculationService = CostCalculationDataAccessService.getInstance()
    this.authService = AuthDataAccessService.getInstance()
    this.roleService = new RoleDataAccessService()
    this.tokenManagementService = TokenManagementService.getInstance()
    console.log('🔧 [DataAccessManager] 认证服务、角色服务和Token管理服务已初始化')

    // 初始化数据变更通知机制
    this.initializeDataChangeNotification()

    // 优先级同步服务已删除

    if (process.env.NODE_ENV === 'development') {
      console.log({
        version: API_VERSION,
        config: this.config,
        dataChangeNotifier: '已启用'
      })
    }
  }

  /**
   * 获取单例实例
   */
  public static getInstance(config?: Partial<DataAccessConfig>): DataAccessManager {
    if (!DataAccessManager.instance) {
      DataAccessManager.instance = new DataAccessManager(config)
    }
    return DataAccessManager.instance
  }

  // 优先级同步服务已删除

  /**
   * 记录访问日志（简化版 - 符合PRD极简要求）
   */
  private logAccess(
    service: string,
    method: string,
    params: any,
    duration: number,
    success: boolean,
    error?: string,
    fromCache?: boolean
  ): void {
    try {
      if (!this.config.enableLogging) return

      // 简化日志记录，只在开发环境记录基础信息
      if (process.env.NODE_ENV === 'development') {
        console.log(`📊 [DataAccess] ${service}.${method} - ${success ? '✅' : '❌'} (${duration}ms)${fromCache ? ' [缓存]' : ''}`)
        if (error) {
          console.error(`❌ [DataAccess] 错误: ${error}`)
        }
      }

      // 保持基础统计信息 - 添加空值检查
      if (this.statistics) {
        this.statistics.totalRequests++
        if (success) {
          this.statistics.successfulRequests++
        } else {
          this.statistics.failedRequests++
        }

        if (fromCache) {
          this.statistics.cacheHits++
        }
      }
    } catch (logError) {
      // 日志记录失败不应该影响主要功能
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️ [DataAccessManager] 日志记录失败:', logError)
      }
    }
  }

  // 🔧 优化：批量性能报告方法

  /**
   * 添加到性能批次
   */
  private addToPerformanceBatch(service: string, method: string, duration: number, success: boolean, fromCache: boolean): void {
    this.performanceBatch.push({
      service,
      method,
      duration,
      success,
      fromCache,
      timestamp: Date.now()
    })

    // 启动批量报告定时器
    if (!this.batchReportTimer) {
      this.batchReportTimer = setTimeout(() => {
        this.flushPerformanceBatch()
      }, this.BATCH_REPORT_INTERVAL)
    }
  }

  /**
   * 输出批量性能报告
   */
  private flushPerformanceBatch(): void {
    if (this.performanceBatch.length === 0) {
      this.batchReportTimer = null
      return
    }

    // 统计数据
    const stats = {
      totalOperations: this.performanceBatch.length,
      successCount: this.performanceBatch.filter(op => op.success).length,
      cacheHits: this.performanceBatch.filter(op => op.fromCache).length,
      averageTime: Math.round(this.performanceBatch.reduce((sum, op) => sum + op.duration, 0) / this.performanceBatch.length),
      services: new Set(this.performanceBatch.map(op => op.service)).size,
      timeRange: {
        start: new Date(Math.min(...this.performanceBatch.map(op => op.timestamp))).toLocaleTimeString(),
        end: new Date(Math.max(...this.performanceBatch.map(op => op.timestamp))).toLocaleTimeString()
      }
    }

    console.log('📊 [DataAccessManager] 批量性能报告:', stats)

    // 清空批次
    this.performanceBatch = []
    this.batchReportTimer = null
  }

  // 性能优化支持方法

  /**
   * 判断是否应该使用缓存
   */
  private shouldUseCache(method: string): boolean {
    // 🔧 临时修复：禁用getById的缓存，避免ID混淆问题
    if (method === 'getById') {
      return false
    }

    // 读操作使用缓存，写操作不使用
    const readMethods = ['get', 'find', 'search', 'list', 'statistics', 'utilization']
    return readMethods.some(readMethod => method.toLowerCase().includes(readMethod))
  }

  /**
   * 判断是否应该缓存结果
   */
  private shouldCacheResult(method: string, result: any): boolean {
    // 成功的读操作结果才缓存
    return this.shouldUseCache(method) && result?.status === 'success'
  }

  /**
   * 生成缓存键
   * 🔧 修复：统一缓存键格式，确保与清理模式匹配
   */
  private generateCacheKey(service: string, method: string, params: any): string {
    // 🔧 修复：使用冒号分隔符，与WorkstationUpdateService的清理模式保持一致
    const baseKey = `${service}:${method}`

    // 🔧 修复：标准化参数处理，确保键的一致性
    if (!params || (typeof params === 'object' && Object.keys(params).length === 0)) {
      return baseKey
    }

    // 对参数进行标准化处理
    let paramStr: string
    if (typeof params === 'object') {
      // 对对象参数进行排序，确保键的一致性
      const sortedParams = Object.keys(params).sort().reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {} as any)
      paramStr = JSON.stringify(sortedParams)
    } else {
      paramStr = String(params)
    }

    return `${baseKey}:${Buffer.from(paramStr).toString('base64').slice(0, 32)}`
  }



  /**
   * 从服务名获取数据类型
   */
  private getDataTypeFromService(service: string): 'orders' | 'workOrders' | 'workstations' | 'statistics' {
    if (service.includes('ProductionOrder')) return 'orders'
    if (service.includes('ProductionWorkOrder')) return 'workOrders'
    if (service.includes('OrderService') || service.includes('SalesOrder')) return 'orders' // 🔧 新增：支持销售订单
    if (service.includes('Workstation')) return 'workstations'
    return 'statistics'
  }



  /**
   * 获取访问频率（简化实现）
   */
  private getAccessFrequency(operationKey: string): number {
    // 从访问日志中统计频率
    const recentLogs = this.accessLogs.filter(log =>
      Date.now() - new Date(log.timestamp).getTime() < 5 * 60 * 1000 // 最近5分钟
    )

    return recentLogs.filter(log => `${log.service}.${log.method}` === operationKey).length
  }

  /**
   * 判断是否应该预加载
   */
  private shouldPreload(method: string, result: any): boolean {
    // 获取列表数据时触发预加载
    return method.includes('getAll') && result?.data?.items?.length > 0
  }

  // ==================== 内置缓存系统核心方法 ====================

  /**
   * 从缓存获取数据
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    // 检查是否过期
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      return null
    }

    entry.accessCount++
    entry.lastAccessed = Date.now()
    return entry.data
  }

  /**
   * 设置缓存数据 - 优化版
   */
  private setToCache<T>(key: string, data: T, service: string, method?: string): void {
    // ✅ 优化：智能缓存容量管理
    if (this.cache.size >= this.cacheConfig.maxSize) {
      this.smartEvictEntries()
    }

    const strategy = this.cacheConfig.strategies[service] || { 
      ttl: this.cacheConfig.defaultTTL,
      priority: 'medium'
    }
    
    // ✅ 优化：智能数据分析
    const dataType = this.getDataType(service, method)
    const priority = strategy.priority || 'medium'
    const estimatedSize = this.estimateDataSize(data)
    const accessPattern = this.predictAccessPattern(key, service)
    
    const entry: CacheEntry = {
      data,
      expiresAt: Date.now() + strategy.ttl,
      accessCount: 1,
      lastAccessed: Date.now(),
      createdAt: Date.now(),
      // ✅ 优化：智能缓存属性
      priority,
      dataType,
      estimatedSize,
      accessPattern,
      refreshable: this.isRefreshableData(service, method)
    }

    this.cache.set(key, entry)
    
    // ✅ 优化：记录访问历史
    this.recordCacheAccess(key)
  }

  /**
   * ✅ 优化：智能缓存清理策略
   */
  private smartEvictEntries(): void {
    const entries = Array.from(this.cache.entries())
    const now = Date.now()
    
    // 根据配置的算法选择清理策略
    switch (this.cacheConfig.smartEviction.algorithm) {
      case 'lru':
        this.evictByLRU(entries)
        break
      case 'lfu':
        this.evictByLFU(entries)
        break
      case 'adaptive':
      default:
        this.evictAdaptive(entries, now)
        break
    }
    
    this.cacheStats.evictions++
  }

  /**
   * ✅ 优化：自适应缓存清理
   */
  private evictAdaptive(entries: Array<[string, CacheEntry]>, now: number): void {
    // 综合考虑优先级、访问频率、大小等因素
    const scored = entries.map(([key, entry]) => {
      let score = 0
      
      // 优先级评分 (优先级越高分数越高，越不容易被清理)
      const priorityScores = { critical: 100, high: 70, medium: 40, low: 10 }
      score += priorityScores[entry.priority] || 40
      
      // 访问频率评分
      const age = (now - entry.createdAt) / (24 * 60 * 60 * 1000) // 天数
      const frequency = entry.accessCount / Math.max(age, 0.1)
      score += Math.min(frequency * 10, 50)
      
      // 最近访问评分
      const lastAccessAge = (now - entry.lastAccessed) / (60 * 60 * 1000) // 小时
      score += Math.max(50 - lastAccessAge * 2, 0)
      
      // 大小惩罚（大数据降低分数）
      score -= Math.min(entry.estimatedSize / 1000, 20)
      
      return { key, entry, score }
    })
    
    // 按分数排序，删除分数最低的条目
    scored.sort((a, b) => a.score - b.score)
    const deleteCount = Math.max(Math.floor(entries.length * 0.15), 1)
    
    for (let i = 0; i < deleteCount && i < scored.length; i++) {
      this.cache.delete(scored[i].key)
      this.cacheAccessHistory.delete(scored[i].key)
    }
  }

  /**
   * ✅ 优化：LRU清理策略
   */
  private evictByLRU(entries: Array<[string, CacheEntry]>): void {
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
    const deleteCount = Math.floor(entries.length * 0.1)
    
    for (let i = 0; i < deleteCount; i++) {
      this.cache.delete(entries[i][0])
      this.cacheAccessHistory.delete(entries[i][0])
    }
  }

  /**
   * ✅ 优化：LFU清理策略
   */
  private evictByLFU(entries: Array<[string, CacheEntry]>): void {
    entries.sort((a, b) => a[1].accessCount - b[1].accessCount)
    const deleteCount = Math.floor(entries.length * 0.1)
    
    for (let i = 0; i < deleteCount; i++) {
      this.cache.delete(entries[i][0])
      this.cacheAccessHistory.delete(entries[i][0])
    }
  }

  /**
   * 清理过期缓存 - 保留原有方法作为备用
   */
  private evictOldestEntries(): void {
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)

    // 删除最旧的10%条目
    const deleteCount = Math.floor(entries.length * 0.1)
    for (let i = 0; i < deleteCount; i++) {
      this.cache.delete(entries[i][0])
    }
  }

  /**
   * 统一的缓存执行方法
   */
  private async executeWithCache<T>(
    service: string,
    method: string,
    params: any,
    executor: () => Promise<T>
  ): Promise<T> {
    // ✅ 架构合规：使用内置缓存系统决策
    if (!this.config.enableCaching) {
      return executor()
    }

    // 简化的缓存决策：读操作才缓存
    const readMethods = ['get', 'find', 'search', 'list', 'statistics']
    const shouldCache = readMethods.some(readMethod => 
      method.toLowerCase().includes(readMethod)
    )

    if (!shouldCache) {
      return executor()
    }

    const cacheKey = this.generateCacheKey(service, method, params)

    // 请求去重
    if (this.requestCache.has(cacheKey)) {
      return this.requestCache.get(cacheKey)!
    }

    // ✅ 架构合规：使用DataAccessManager内置缓存系统
    const cachedEntry = this.cache.get(cacheKey)
    if (cachedEntry && Date.now() < cachedEntry.expiresAt) {
      this.cacheStats.hits++
      // ✅ 优化：更新访问信息
      cachedEntry.accessCount++
      cachedEntry.lastAccessed = Date.now()
      this.recordCacheAccess(cacheKey)
      return cachedEntry.data
    }

    // 执行请求
    const requestPromise = executor()
    this.requestCache.set(cacheKey, requestPromise)

    try {
      const result = await requestPromise
      this.cacheStats.misses++

      // ✅ 架构合规：使用DataAccessManager内置缓存系统设置缓存
      if (this.shouldCacheResult(method, result)) {
        this.setToCache(cacheKey, result, service, method)
      }

      return result
    } finally {
      this.requestCache.delete(cacheKey)
    }
  }

  // ==================== ✅ 优化：智能缓存辅助方法 ====================

  /**
   * ✅ 优化：初始化高级缓存功能
   */
  private initializeAdvancedCaching(): void {
    console.log('🔧 [DataAccessManager] 启动智能缓存管理')
    
    // 启动缓存预热
    if (this.cacheConfig.prewarming.enabled) {
      this.startCachePrewarming()
    }
    
    // 启动定期内存检查
    this.startMemoryMonitoring()
    
    console.log('✅ [DataAccessManager] 智能缓存管理已启动')
  }

  /**
   * ✅ 优化：启动缓存预热
   */
  private startCachePrewarming(): void {
    if (this.prewarmTimer) {
      clearInterval(this.prewarmTimer)
    }
    
    this.prewarmTimer = setInterval(() => {
      this.performCachePrewarming()
    }, this.cacheConfig.prewarming.scheduleInterval)
    
    // 立即执行一次预热
    setTimeout(() => this.performCachePrewarming(), 5000) // 5秒后开始
  }

  /**
   * ✅ 优化：执行缓存预热
   */
  private async performCachePrewarming(): Promise<void> {
    if (this.memoryPressure) {
      console.log('🔧 [DataAccessManager] 内存压力过高，跳过预热')
      return
    }
    
    console.log('🔥 [DataAccessManager] 开始缓存预热')
    
    try {
      for (const strategy of this.cacheConfig.prewarming.strategies) {
        await this.prewarmStrategy(strategy)
      }
    } catch (error) {
      console.error('❌ [DataAccessManager] 缓存预热失败:', error)
    }
  }

  /**
   * ✅ 优化：预热特定策略
   */
  private async prewarmStrategy(strategy: string): Promise<void> {
    switch (strategy) {
      case 'workstations':
        await this.prewarmWorkstations()
        break
      case 'orders':
        await this.prewarmOrders()
        break
      case 'products':
        await this.prewarmProducts()
        break
    }
  }

  /**
   * ✅ 优化：预热工位数据
   */
  private async prewarmWorkstations(): Promise<void> {
    try {
      await this.workstations.getActiveWorkstations()

      // 安全地更新统计信息
      try {
        if (this.cacheStats) {
          this.cacheStats.prewarmHits++
        }
      } catch (statsError) {
        // 统计更新失败不影响预热功能
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ [DataAccessManager] 预热统计更新失败:', statsError)
        }
      }

      console.log('🔥 [DataAccessManager] 工位数据预热完成')
    } catch (error) {
      console.warn('⚠️ [DataAccessManager] 工位数据预热失败:', error)
    }
  }

  /**
   * ✅ 优化：预热订单数据
   */
  private async prewarmOrders(): Promise<void> {
    try {
      await this.orders.getAll({ limit: 50 }) // 最近50个订单

      // 安全地更新统计信息
      try {
        if (this.cacheStats) {
          this.cacheStats.prewarmHits++
        }
      } catch (statsError) {
        // 统计更新失败不影响预热功能
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ [DataAccessManager] 预热统计更新失败:', statsError)
        }
      }

      console.log('🔥 [DataAccessManager] 订单数据预热完成')
    } catch (error) {
      console.warn('⚠️ [DataAccessManager] 订单数据预热失败:', error)
    }
  }

  /**
   * ✅ 优化：预热产品数据
   */
  private async prewarmProducts(): Promise<void> {
    try {
      await this.products.getActive()

      // 安全地更新统计信息
      try {
        if (this.cacheStats) {
          this.cacheStats.prewarmHits++
        }
      } catch (statsError) {
        // 统计更新失败不影响预热功能
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ [DataAccessManager] 预热统计更新失败:', statsError)
        }
      }

      console.log('🔥 [DataAccessManager] 产品数据预热完成')
    } catch (error) {
      console.warn('⚠️ [DataAccessManager] 产品数据预热失败:', error)
    }
  }

  /**
   * ✅ 优化：启动内存监控
   */
  private startMemoryMonitoring(): void {
    setInterval(() => {
      this.checkMemoryPressure()
    }, 30000) // 每30秒检查一次
  }

  /**
   * ✅ 优化：检查内存压力
   */
  private checkMemoryPressure(): void {
    const now = Date.now()
    if (now - this.lastMemoryCheck < 10000) return // 10秒内不重复检查
    
    this.lastMemoryCheck = now
    
    try {
      // 简单的内存压力检测（基于缓存大小）
      const cacheMemoryRatio = this.cache.size / this.cacheConfig.maxSize
      
      if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryCritical) {
        this.memoryPressure = true
        this.handleCriticalMemoryPressure()
      } else if (cacheMemoryRatio > this.cacheConfig.smartEviction.thresholds.memoryWarning) {
        this.memoryPressure = false
        this.handleMemoryWarning()
      } else {
        this.memoryPressure = false
      }
    } catch (error) {
      console.warn('⚠️ [DataAccessManager] 内存压力检查失败:', error)
    }
  }

  /**
   * ✅ 优化：处理紧急内存压力
   */
  private handleCriticalMemoryPressure(): void {
    console.warn('🚨 [DataAccessManager] 检测到紧急内存压力，执行强制清理')
    
    // 强制清理缓存
    this.smartEvictEntries()
    
    // 暂停预热
    if (this.prewarmTimer) {
      clearInterval(this.prewarmTimer)
      this.prewarmTimer = null
    }
  }

  /**
   * ✅ 优化：处理内存警告
   */
  private handleMemoryWarning(): void {
    console.warn('⚠️ [DataAccessManager] 检测到内存压力警告，执行适度清理')
    
    // 清理低优先级缓存
    this.cleanupLowPriorityCache()
  }

  /**
   * ✅ 优化：清理低优先级缓存
   */
  private cleanupLowPriorityCache(): void {
    const entries = Array.from(this.cache.entries())
    let deletedCount = 0
    
    for (const [key, entry] of entries) {
      if (entry.priority === 'low' && Date.now() > entry.expiresAt - 60000) { // 提前1分钟清理低优先级
        this.cache.delete(key)
        this.cacheAccessHistory.delete(key)
        deletedCount++
      }
    }
    
    console.log(`🧹 [DataAccessManager] 清理了${deletedCount}个低优先级缓存条目`)
  }

  /**
   * ✅ 优化：获取数据类型
   */
  private getDataType(service: string, method?: string): string {
    if (!method) return service
    
    if (method.includes('statistics') || method.includes('utilization')) return 'statistics'
    if (method.includes('list') || method.includes('getAll')) return 'list'
    if (method.includes('getById') || method.includes('getBy')) return 'detail'
    
    return service
  }

  /**
   * ✅ 优化：预测访问模式
   */
  private predictAccessPattern(key: string, service: string): 'frequent' | 'occasional' | 'rare' {
    const history = this.cacheAccessHistory.get(key) || []
    
    if (history.length < 2) {
      // 根据服务类型预测
      if (service === 'workstations' || service === 'statistics') return 'frequent'
      if (service === 'orders' || service === 'products') return 'occasional'
      return 'rare'
    }
    
    // 基于历史访问频率
    const recentAccesses = history.filter(time => Date.now() - time < 60 * 60 * 1000).length // 1小时内
    if (recentAccesses > 10) return 'frequent'
    if (recentAccesses > 3) return 'occasional'
    return 'rare'
  }

  /**
   * ✅ 优化：估算数据大小
   */
  private estimateDataSize(data: any): number {
    try {
      const jsonString = JSON.stringify(data)
      return jsonString.length * 2 // 大致估算，考虑Unicode字符
    } catch {
      return 1000 // 默认估算
    }
  }

  /**
   * ✅ 优化：判断数据是否可刷新
   */
  private isRefreshableData(service: string, method?: string): boolean {
    // 统计数据、状态数据等是可刷新的
    if (method?.includes('statistics') || method?.includes('status') || method?.includes('utilization')) {
      return true
    }
    
    // 工位数据是频繁变化的
    if (service === 'workstations') return true
    
    return false
  }

  /**
   * ✅ 优化：记录缓存访问
   */
  private recordCacheAccess(key: string): void {
    const history = this.cacheAccessHistory.get(key) || []
    history.push(Date.now())
    
    // 只保留最近100次访问记录
    if (history.length > 100) {
      history.splice(0, history.length - 100)
    }
    
    this.cacheAccessHistory.set(key, history)
  }

  /**
   * ✅ 架构合规：获取缓存TTL
   */
  private getCacheTTL(service: string, method: string): number {
    // 根据服务类型和方法返回不同的TTL
    const serviceConfig: { [key: string]: number } = {
      orders: 2 * 60 * 1000,      // 订单数据: 2分钟
      products: 10 * 60 * 1000,   // 产品数据: 10分钟
      workstations: 30 * 1000,    // 工位数据: 30秒
      statistics: 30 * 1000,      // 统计数据: 30秒
      customers: 15 * 60 * 1000,  // 客户数据: 15分钟
      employees: 30 * 60 * 1000   // 员工数据: 30分钟
    }
    
    return serviceConfig[service] || this.config.defaultTTL || 5 * 60 * 1000 // 默认5分钟
  }

  /**
   * 初始化性能监控
   */
  private initializePerformanceMonitoring(): void {
    this.performanceMetrics = {
      totalCalls: 0,
      successCalls: 0,
      errorCalls: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      cacheHitRate: 0,
      slowQueries: [],
      methodStats: new Map(),
      hourlyStats: []
    }

    // 启动定期清理任务
    this.startPerformanceCleanupTask()
  }

  /**
   * 启动性能数据清理任务
   */
  private startPerformanceCleanupTask(): void {
    // 每小时清理一次过期的性能数据
    setInterval(() => {
      this.cleanupPerformanceData()
    }, 60 * 60 * 1000) // 1小时
  }

  /**
   * 清理过期的性能数据
   */
  private cleanupPerformanceData(): void {
    const now = Date.now()
    const oneHourAgo = now - 60 * 60 * 1000
    const oneDayAgo = now - 24 * 60 * 60 * 1000

    // 清理最近调用记录（保留1小时）
    this.recentCalls = this.recentCalls.filter(call => call.timestamp > oneHourAgo)

    // 清理慢查询记录（保留1天，最多100条）
    this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries
      .filter(query => query.timestamp > oneDayAgo)
      .slice(-100)

    // 更新小时统计
    this.updateHourlyStats()
  }

  /**
   * 更新小时统计
   */
  private updateHourlyStats(): void {
    const now = new Date()
    const currentHour = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:00`

    const oneHourAgo = Date.now() - 60 * 60 * 1000
    const hourCalls = this.recentCalls.filter(call => call.timestamp > oneHourAgo)

    if (hourCalls.length > 0) {
      const totalTime = hourCalls.reduce((sum, call) => sum + call.duration, 0)
      const errorCount = hourCalls.filter(call => call.status === 'error').length

      const hourStat = {
        hour: currentHour,
        calls: hourCalls.length,
        averageTime: totalTime / hourCalls.length,
        errorRate: errorCount / hourCalls.length
      }

      // 更新或添加当前小时的统计
      const existingIndex = this.performanceMetrics.hourlyStats.findIndex(stat => stat.hour === currentHour)
      if (existingIndex >= 0) {
        this.performanceMetrics.hourlyStats[existingIndex] = hourStat
      } else {
        this.performanceMetrics.hourlyStats.push(hourStat)
      }

      // 保留最近24小时的统计
      this.performanceMetrics.hourlyStats = this.performanceMetrics.hourlyStats.slice(-24)
    }
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(
    operationKey: string,
    duration: number,
    success: boolean,
    cached: boolean = false
  ): void {
    // 更新总体指标
    this.performanceMetrics.totalCalls++
    if (success) {
      this.performanceMetrics.successCalls++
    } else {
      this.performanceMetrics.errorCalls++
    }

    // 更新响应时间统计
    this.performanceMetrics.minResponseTime = Math.min(this.performanceMetrics.minResponseTime, duration)
    this.performanceMetrics.maxResponseTime = Math.max(this.performanceMetrics.maxResponseTime, duration)

    // 更新平均响应时间
    const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalCalls - 1) + duration
    this.performanceMetrics.averageResponseTime = totalTime / this.performanceMetrics.totalCalls

    // 更新缓存命中率
    if (cached) {
      const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate + 1
      this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls
    } else {
      const cacheHits = this.performanceMetrics.totalCalls * this.performanceMetrics.cacheHitRate
      this.performanceMetrics.cacheHitRate = cacheHits / this.performanceMetrics.totalCalls
    }

    // 更新方法统计
    const methodStat = this.methodStats.get(operationKey) || {
      calls: 0,
      totalTime: 0,
      averageTime: 0,
      successRate: 0,
      lastCall: 0,
      errors: 0
    }

    methodStat.calls++
    methodStat.totalTime += duration
    methodStat.averageTime = methodStat.totalTime / methodStat.calls
    methodStat.lastCall = Date.now()

    if (success) {
      methodStat.successRate = (methodStat.successRate * (methodStat.calls - 1) + 1) / methodStat.calls
    } else {
      methodStat.errors++
      methodStat.successRate = (methodStat.successRate * (methodStat.calls - 1)) / methodStat.calls
    }

    this.methodStats.set(operationKey, methodStat)

    // 记录慢查询
    if (duration > 1000) { // 超过1秒的查询
      this.performanceMetrics.slowQueries.push({
        method: operationKey,
        params: {},
        duration,
        timestamp: Date.now(),
        cached
      })

      // 保持慢查询记录数量限制
      if (this.performanceMetrics.slowQueries.length > 100) {
        this.performanceMetrics.slowQueries = this.performanceMetrics.slowQueries.slice(-50)
      }
    }

    // 记录最近调用
    this.recentCalls.push({
      method: operationKey,
      duration,
      status: success ? 'success' : 'error',
      timestamp: Date.now(),
      cached
    })

    // 保持最近调用记录数量限制
    if (this.recentCalls.length > 1000) {
      this.recentCalls = this.recentCalls.slice(-500)
    }
  }

  // 注意：executeWithCaching方法已移动到内置缓存系统部分

  /**
   * 执行带日志记录和性能监控的方法调用 - 统一改造版
   */
  private async executeWithLogging<T>(
    service: string,
    method: string,
    params: any,
    executor: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now()
    const operationKey = `${service}.${method}`

    try {
      // 使用内置缓存系统
      const result = await this.executeWithCache(service, method, params, executor)

      const duration = Date.now() - startTime

      // 安全地更新性能指标和日志记录（成功）
      try {
        this.updatePerformanceMetrics(operationKey, duration, true, false)
        this.logAccess(service, method, params, duration, true)
        dataAccessPerformanceMonitor.recordRequest(operationKey, duration, true, params)
      } catch (loggingError) {
        // 日志记录失败不应该影响主要功能
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ [DataAccessManager] 成功日志记录失败:', loggingError)
        }
      }

      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      // 安全地更新性能指标和日志记录（错误）
      try {
        this.updatePerformanceMetrics(operationKey, duration, false, false)
        this.logAccess(service, method, params, duration, false, errorMessage)
        dataAccessPerformanceMonitor.recordRequest(operationKey, duration, false, params)
      } catch (loggingError) {
        // 日志记录失败不应该影响主要功能
        if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ [DataAccessManager] 错误日志记录失败:', loggingError)
        }
      }

      throw error
    }
  }

  // executeWithUnifiedCache方法已移除，使用内置缓存系统

  // invalidateUnifiedCache方法已移除，使用内置缓存系统

  // executeWithWorkstationCaching方法已移除，使用内置缓存系统

  // 统一缓存相关方法已移除，使用内置缓存系统

  // ==================== 产品数据访问 ====================

  /**
   * 产品数据访问器
   */
  get products() {
    return {
      getAll: (params?: any) => this.executeWithLogging(
        'ProductService', 'getProducts', params,
        () => this.productService.getProducts(params)
      ),
      
      getById: (id: string) => this.executeWithLogging(
        'ProductService', 'getProductById', { id },
        () => this.productService.getProductById(id)
      ),
      
      getByCode: (code: string) => this.executeWithLogging(
        'ProductService', 'getProductByCode', { code },
        () => this.productService.getProductByCode(code)
      ),
      
      create: (data: any) => this.executeWithLogging(
        'ProductService', 'createProduct', data,
        () => this.productService.createProduct(data)
      ),
      
      update: (id: string, updates: any) => this.executeWithLogging(
        'ProductService', 'updateProduct', { id, updates },
        () => this.productService.updateProduct(id, updates)
      ),
      
      delete: (id: string) => this.executeWithLogging(
        'ProductService', 'deleteProduct', { id },
        () => this.productService.deleteProduct(id)
      ),
      
      getActive: () => this.executeWithLogging(
        'ProductService', 'getActiveProducts', {},
        () => this.productService.getActiveProducts()
      ),
      
      getByCategory: (category: string) => this.executeWithLogging(
        'ProductService', 'getProductsByCategory', { category },
        () => this.productService.getProductsByCategory(category)
      ),
      
      search: (keyword: string) => this.executeWithLogging(
        'ProductService', 'searchProducts', { keyword },
        () => this.productService.searchProducts(keyword)
      ),
      
      getByMold: (moldId: string) => this.executeWithLogging(
        'ProductService', 'getProductsByMold', { moldId },
        () => this.productService.getProductsByMold(moldId)
      ),
      
      getMoldUsage: () => this.executeWithLogging(
        'ProductService', 'getMoldUsageStatistics', {},
        () => this.productService.getMoldUsageStatistics()
      )
    }
  }

  // ==================== 客户数据访问 ====================

  /**
   * 客户数据访问器
   */
  get customers() {
    return {
      getAll: (params?: any) => this.executeWithLogging(
        'CustomerService', 'getCustomers', params,
        () => this.customerService.getCustomers(params)
      ),
      
      getById: (id: string) => this.executeWithLogging(
        'CustomerService', 'getCustomerById', { id },
        () => this.customerService.getCustomerById(id)
      ),
      
      create: (data: any) => this.executeWithLogging(
        'CustomerService', 'createCustomer', data,
        () => this.customerService.createCustomer(data)
      ),
      
      update: (id: string, updates: any) => this.executeWithLogging(
        'CustomerService', 'updateCustomer', { id, updates },
        () => this.customerService.updateCustomer(id, updates)
      ),
      
      delete: (id: string) => this.executeWithLogging(
        'CustomerService', 'deleteCustomer', { id },
        () => this.customerService.deleteCustomer(id)
      ),
      
      getActive: () => this.executeWithLogging(
        'CustomerService', 'getActiveCustomers', {},
        () => this.customerService.getActiveCustomers()
      ),
      
      search: (keyword: string) => this.executeWithLogging(
        'CustomerService', 'searchCustomers', { keyword },
        () => this.customerService.searchCustomers(keyword)
      )
    }
  }

  // ==================== 员工数据访问 ====================

  /**
   * 员工数据访问器
   */
  get employees() {
    return {
      getAll: (params?: any) => this.executeWithLogging(
        'EmployeeService', 'getEmployees', params,
        () => this.employeeService.getEmployees(params)
      ),
      
      getById: (id: string) => this.executeWithLogging(
        'EmployeeService', 'getEmployeeById', { id },
        () => this.employeeService.getEmployeeById(id)
      ),
      
      create: (data: any) => this.executeWithLogging(
        'EmployeeService', 'createEmployee', data,
        () => this.employeeService.createEmployee(data)
      ),
      
      update: (id: string, updates: any) => this.executeWithLogging(
        'EmployeeService', 'updateEmployee', { id, updates },
        () => this.employeeService.updateEmployee(id, updates)
      ),
      
      delete: (id: string) => this.executeWithLogging(
        'EmployeeService', 'deleteEmployee', { id },
        () => this.employeeService.deleteEmployee(id)
      ),
      
      getActive: () => this.executeWithLogging(
        'EmployeeService', 'getActiveEmployees', {},
        () => this.employeeService.getActiveEmployees()
      ),
      
      getByDepartment: (department: string) => this.executeWithLogging(
        'EmployeeService', 'getEmployeesByDepartment', { department },
        () => this.employeeService.getEmployeesByDepartment(department)
      ),
      
      getByRole: (role: string) => this.executeWithLogging(
        'EmployeeService', 'getEmployeesByRole', { role },
        () => this.employeeService.getEmployeesByRole(role)
      ),
      
      getSales: () => this.executeWithLogging(
        'EmployeeService', 'getSalesEmployees', {},
        () => this.employeeService.getSalesEmployees()
      )
    }
  }

  // ==================== 库存数据访问 ====================

  /**
   * 库存数据访问器
   */
  get inventory() {
    return {
      getAll: (params?: any) => this.executeWithLogging(
        'InventoryService', 'getProductInventory', params,
        () => this.inventoryService.getProductInventory(params)
      ),

      getByProductCode: (productCode: string) => this.executeWithLogging(
        'InventoryService', 'getInventoryByProductCode', { productCode },
        () => this.inventoryService.getInventoryByProductCode(productCode)
      ),

      update: (productCode: string, updates: any) => this.executeWithLogging(
        'InventoryService', 'updateInventory', { productCode, updates },
        () => this.inventoryService.updateInventory(productCode, updates)
      ),

      getLowStock: (threshold?: number) => this.executeWithLogging(
        'InventoryService', 'getLowStockProducts', { threshold },
        () => this.inventoryService.getLowStockProducts(threshold)
      ),

      getValue: () => this.executeWithLogging(
        'InventoryService', 'getInventoryValue', {},
        () => this.inventoryService.getInventoryValue()
      )
    }
  }

  // ==================== 认证数据访问 ====================

  /**
   * 认证数据访问器
   */
  get auth() {
    return {
      // 认证相关
      login: (credentials: any) => this.executeWithLogging(
        'AuthService', 'login', credentials,
        () => this.authService.login(credentials)
      ),

      logout: (refreshToken: string) => this.executeWithLogging(
        'AuthService', 'logout', { refreshToken },
        () => this.authService.logout(refreshToken)
      ),

      refreshToken: (request: any) => this.executeWithLogging(
        'AuthService', 'refreshToken', request,
        () => this.authService.refreshToken(request)
      ),

      validateToken: (token: string) => this.executeWithLogging(
        'AuthService', 'validateToken', { token },
        () => this.authService.validateToken(token)
      ),

      // 用户管理
      getUserById: (id: string) => this.executeWithLogging(
        'AuthService', 'getUserById', { id },
        () => this.authService.getUserById(id)
      ),

      getUserByUsername: (username: string) => this.executeWithLogging(
        'AuthService', 'getUserByUsername', { username },
        () => this.authService.getUserByUsername(username)
      ),

      createUser: (userData: any) => this.executeWithLogging(
        'AuthService', 'createUser', userData,
        () => this.authService.createUser(userData)
      ),

      updateUser: (id: string, updates: any) => this.executeWithLogging(
        'AuthService', 'updateUser', { id, updates },
        () => this.authService.updateUser(id, updates)
      ),

      deleteUser: (id: string) => this.executeWithLogging(
        'AuthService', 'deleteUser', { id },
        () => this.authService.deleteUser(id)
      ),

      // 会话管理
      createSession: (userId: string, sessionData: any) => this.executeWithLogging(
        'AuthService', 'createSession', { userId, sessionData },
        () => this.authService.createSession(userId, sessionData)
      ),

      getSession: (sessionId: string) => this.executeWithLogging(
        'AuthService', 'getSession', { sessionId },
        () => this.authService.getSession(sessionId)
      ),

      updateSession: (sessionId: string, updates: any) => this.executeWithLogging(
        'AuthService', 'updateSession', { sessionId, updates },
        () => this.authService.updateSession(sessionId, updates)
      ),

      deleteSession: (sessionId: string) => this.executeWithLogging(
        'AuthService', 'deleteSession', { sessionId },
        () => this.authService.deleteSession(sessionId)
      ),

      getUserSessions: (userId: string) => this.executeWithLogging(
        'AuthService', 'getUserSessions', { userId },
        () => this.authService.getUserSessions(userId)
      ),

      // 密码管理
      changePassword: (userId: string, currentPassword: string, newPassword: string) => this.executeWithLogging(
        'AuthService', 'changePassword', { userId },
        () => this.authService.changePassword(userId, currentPassword, newPassword)
      ),

      resetPassword: (userId: string, newPassword: string) => this.executeWithLogging(
        'AuthService', 'resetPassword', { userId },
        () => this.authService.resetPassword(userId, newPassword)
      )
    }
  }

  // ==================== Token管理数据访问 ====================

  /**
   * Token管理数据访问器
   */
  get tokenManagement() {
    return {
      // Token生成
      generateAccessToken: (payload: any) => this.executeWithLogging(
        'TokenManagementService', 'generateAccessToken', payload,
        () => this.tokenManagementService.generateAccessToken(payload)
      ),

      generateRefreshToken: (userId: string, sessionId: string) => this.executeWithLogging(
        'TokenManagementService', 'generateRefreshToken', { userId, sessionId },
        () => this.tokenManagementService.generateRefreshToken(userId, sessionId)
      ),

      // Token验证
      verifyAccessToken: (token: string) => this.executeWithLogging(
        'TokenManagementService', 'verifyAccessToken', { tokenLength: token?.length },
        () => this.tokenManagementService.verifyAccessToken(token)
      ),

      verifyRefreshToken: (token: string) => this.executeWithLogging(
        'TokenManagementService', 'verifyRefreshToken', { tokenLength: token?.length },
        () => this.tokenManagementService.verifyRefreshToken(token)
      ),

      // Token工具方法
      extractUserInfo: (token: string) => this.executeWithLogging(
        'TokenManagementService', 'extractUserInfo', { tokenLength: token?.length },
        () => this.tokenManagementService.extractUserInfo(token)
      ),

      shouldRefreshToken: (token: string, thresholdMinutes?: number) => this.executeWithLogging(
        'TokenManagementService', 'shouldRefreshToken', { tokenLength: token?.length, thresholdMinutes },
        () => this.tokenManagementService.shouldRefreshToken(token, thresholdMinutes)
      ),

      getTokenRemainingTime: (token: string) => this.executeWithLogging(
        'TokenManagementService', 'getTokenRemainingTime', { tokenLength: token?.length },
        () => this.tokenManagementService.getTokenRemainingTime(token)
      ),

      generateSessionId: () => this.executeWithLogging(
        'TokenManagementService', 'generateSessionId', {},
        () => this.tokenManagementService.generateSessionId()
      ),

      // 配置检查
      checkConfiguration: () => this.executeWithLogging(
        'TokenManagementService', 'checkConfiguration', {},
        () => this.tokenManagementService.checkConfiguration()
      )
    }
  }

  // ==================== 角色权限数据访问 ====================

  /**
   * 角色权限数据访问器
   */
  get roles() {
    return {
      // 角色管理
      getAll: () => this.executeWithLogging(
        'RoleService', 'getRoles', {},
        () => this.roleService.getRoles()
      ),

      getById: (id: string) => this.executeWithLogging(
        'RoleService', 'getRoleById', { id },
        () => this.roleService.getRoleById(id)
      ),

      getByCode: (code: string) => this.executeWithLogging(
        'RoleService', 'getRoleByCode', { code },
        () => this.roleService.getRoleByCode(code)
      ),

      create: (roleData: any) => this.executeWithLogging(
        'RoleService', 'createRole', roleData,
        () => this.roleService.createRole(roleData)
      ),

      update: (id: string, updates: any) => this.executeWithLogging(
        'RoleService', 'updateRole', { id, updates },
        () => this.roleService.updateRole(id, updates)
      ),

      delete: (id: string) => this.executeWithLogging(
        'RoleService', 'deleteRole', { id },
        () => this.roleService.deleteRole(id)
      ),

      // 权限管理
      getPermissions: () => this.executeWithLogging(
        'RoleService', 'getPermissions', {},
        () => this.roleService.getPermissions()
      ),

      getPermissionsByModule: (module: string) => this.executeWithLogging(
        'RoleService', 'getPermissionsByModule', { module },
        () => this.roleService.getPermissionsByModule(module)
      ),

      assignPermissions: (roleId: string, permissionIds: string[]) => this.executeWithLogging(
        'RoleService', 'assignRolePermissions', { roleId, permissionIds },
        () => this.roleService.assignRolePermissions(roleId, permissionIds)
      )
    }
  }

  /**
   * 订单数据访问器
   */
  get orders() {
    return {
      getAll: (params?: any) => {
        console.log('🔍 [DataAccessManager] orders.getAll 被调用，管理器ID:', (this as any).__managerId)
        console.log('🔍 [DataAccessManager] 使用的OrderService实例ID:', (this.orderService as any).__serviceId)
        return this.executeWithLogging(
          'OrderService', 'getOrders', params,
          () => this.orderService.getOrders(params)
        )
      },

      getById: (id: string) => this.executeWithLogging(
        'OrderService', 'getOrderById', { id },
        () => this.orderService.getOrderById(id)
      ),

      getByNumber: (orderNumber: string) => this.executeWithLogging(
        'OrderService', 'getOrderByNumber', { orderNumber },
        () => this.orderService.getOrderByNumber(orderNumber)
      ),

      create: (data: any) => {
        console.log('🔍 [DataAccessManager] orders.create 被调用，管理器ID:', (this as any).__managerId)
        console.log('🔍 [DataAccessManager] 使用的OrderService实例ID:', (this.orderService as any).__serviceId)
        return this.executeWithLogging(
          'OrderService', 'createOrder', data,
          () => this.orderService.createOrder(data)
        )
      },

      update: (id: string, updates: any) => this.executeWithLogging(
        'OrderService', 'updateOrder', { id, updates },
        () => this.orderService.updateOrder(id, updates)
      ),

      delete: (id: string) => this.executeWithLogging(
        'OrderService', 'deleteOrder', { id },
        () => this.orderService.deleteOrder(id)
      ),

      getByStatus: (status: string) => this.executeWithLogging(
        'OrderService', 'getOrdersByStatus', { status },
        () => this.orderService.getOrdersByStatus(status)
      ),

      getByCustomer: (customerId: string) => this.executeWithLogging(
        'OrderService', 'getOrdersByCustomer', { customerId },
        () => this.orderService.getOrdersByCustomer(customerId)
      ),

      getBySalesRep: (salesRepId: string) => this.executeWithLogging(
        'OrderService', 'getOrdersBySalesRep', { salesRepId },
        () => this.orderService.getOrdersBySalesRep(salesRepId)
      ),

      getByDateRange: (startDate: string, endDate: string) => this.executeWithLogging(
        'OrderService', 'getOrdersByDateRange', { startDate, endDate },
        () => this.orderService.getOrdersByDateRange(startDate, endDate)
      ),

      // 订单号生成和验证方法已移除
      // 请直接使用 BusinessIdGenerator 的相关方法
      // 检查订单是否存在请使用 getByNumber() 方法
    }
  }

  /**
   * 工作时间配置数据访问器
   */
  get workTime() {
    return {
      getConfigurations: () => this.executeWithLogging(
        'WorkTimeService', 'getConfigurations', {},
        () => this.workTimeService.getConfigurations()
      ),

      getById: (id: string) => this.executeWithLogging(
        'WorkTimeService', 'getConfigurationById', { id },
        () => this.workTimeService.getConfigurationById(id)
      ),

      create: (data: any) => this.executeWithLogging(
        'WorkTimeService', 'createConfiguration', data,
        () => this.workTimeService.createConfiguration(data)
      ),

      update: (id: string, data: any) => this.executeWithLogging(
        'WorkTimeService', 'updateConfiguration', { id, ...data },
        () => this.workTimeService.updateConfiguration(id, data)
      ),

      delete: (id: string) => this.executeWithLogging(
        'WorkTimeService', 'deleteConfiguration', { id },
        () => this.workTimeService.deleteConfiguration(id)
      ),

      addWorkTimeSlot: (configId: string, slot: any) => this.executeWithLogging(
        'WorkTimeService', 'addWorkTimeSlot', { configId, slot },
        () => this.workTimeService.addWorkTimeSlot(configId, slot)
      ),

      updateWorkTimeSlot: (configId: string, slotId: string, data: any) => this.executeWithLogging(
        'WorkTimeService', 'updateWorkTimeSlot', { configId, slotId, ...data },
        () => this.workTimeService.updateWorkTimeSlot(configId, slotId, data)
      ),

      deleteWorkTimeSlot: (configId: string, slotId: string) => this.executeWithLogging(
        'WorkTimeService', 'deleteWorkTimeSlot', { configId, slotId },
        () => this.workTimeService.deleteWorkTimeSlot(configId, slotId)
      ),



      calculateWorkingMinutes: (workTimeSlots: any[], breakTimeSlots?: any[]) => {
        const startTime = Date.now()
        const result = this.workTimeService.calculateWorkingMinutes(workTimeSlots, breakTimeSlots)
        const duration = Date.now() - startTime
        this.logAccess('WorkTimeService', 'calculateWorkingMinutes', { workTimeSlots, breakTimeSlots }, duration, true)
        return result
      },

      validateTimeSlot: (startTime: string, endTime: string) => {
        const start = Date.now()
        const result = this.workTimeService.validateTimeSlot(startTime, endTime)
        const duration = Date.now() - start
        this.logAccess('WorkTimeService', 'validateTimeSlot', { startTime, endTime }, duration, true)
        return result
      },

      getDefault: () => this.executeWithLogging(
        'WorkTimeService', 'getDefaultConfiguration', {},
        () => this.workTimeService.getDefaultConfiguration()
      )
    }
  }

  /**
   * 生产订单数据访问器
   */
  get productionOrders() {
    return {
      getAll: (params?: any) => this.executeWithLogging(
        'ProductionOrderService', 'getAll', params,
        () => this.productionOrderService.getAll(params)
      ),

      getById: (id: string) => this.executeWithLogging(
        'ProductionOrderService', 'getById', { id },
        () => this.productionOrderService.getById(id)
      ),

      getByOrderNumber: (orderNumber: string) => this.executeWithLogging(
        'ProductionOrderService', 'getByOrderNumber', { orderNumber },
        () => this.productionOrderService.getByOrderNumber(orderNumber)
      ),

      createFromMRP: (data: any) => this.executeWithLogging(
        'ProductionOrderService', 'createFromMRP', data,
        () => this.productionOrderService.createFromMRP(data)
      ),

      // 添加通用的create方法（用于测试）
      create: (data: any) => this.executeWithLogging(
        'ProductionOrderService', 'createFromMRP', data,
        () => {
          // 为测试目的添加默认MRP字段
          const mrpData = {
            ...data,
            mrpExecutionId: data.mrpExecutionId || `mrp_${Date.now()}`,
            mrpExecutedBy: data.mrpExecutedBy || 'test-user',
            mrpExecutedAt: data.mrpExecutedAt || new Date().toISOString()
          }
          return this.productionOrderService.createFromMRP(mrpData)
        }
      ),



      update: (id: string, data: any) => this.executeWithLogging(
        'ProductionOrderService', 'update', { id, ...data },
        () => this.productionOrderService.update(id, data)
      ),

      delete: (id: string) => this.executeWithLogging(
        'ProductionOrderService', 'delete', { id },
        () => this.productionOrderService.delete(id)
      ),

      getByStatus: (status: string) => this.executeWithLogging(
        'ProductionOrderService', 'getByStatus', { status },
        () => this.productionOrderService.getByStatus(status)
      ),

      getBySalesOrderId: (salesOrderId: string) => this.executeWithLogging(
        'ProductionOrderService', 'getBySalesOrderId', { salesOrderId },
        () => this.productionOrderService.getBySalesOrderId(salesOrderId)
      ),

      // 添加统计方法
      getStatistics: () => this.executeWithLogging(
        'ProductionOrderService', 'getStatistics', {},
        () => this.productionOrderService.getStatistics()
      ),

      // 🔧 新增：重置所有订单状态到初始状态

    }
  }

  /**
   * 生产工单数据访问器
   */
  get productionWorkOrders() {
    return {
      getAll: (params?: any) => this.executeWithLogging(
        'ProductionWorkOrderService', 'getAll', params,
        () => this.productionWorkOrderService.getAll(params)
      ),

      getById: (id: string) => this.executeWithLogging(
        'ProductionWorkOrderService', 'getById', { id },
        () => this.productionWorkOrderService.getById(id)
      ),

      getByBatchNumber: (batchNumber: string) => this.executeWithLogging(
        'ProductionWorkOrderService', 'getByBatchNumber', { batchNumber },
        () => this.productionWorkOrderService.getByBatchNumber(batchNumber)
      ),

      create: (data: any) => this.executeWithLogging(
        'ProductionWorkOrderService', 'create', data,
        () => this.productionWorkOrderService.create(data)
      ),

      update: (id: string, data: any) => this.executeWithLogging(
        'ProductionWorkOrderService', 'update', { id, ...data },
        () => this.productionWorkOrderService.update(id, data)
      ),

      delete: (id: string) => this.executeWithLogging(
        'ProductionWorkOrderService', 'delete', { id },
        () => this.productionWorkOrderService.delete(id)
      ),

      getByStatus: (status: string) => this.executeWithLogging(
        'ProductionWorkOrderService', 'getByStatus', { status },
        () => this.productionWorkOrderService.getByStatus(status)
      ),

      getBySourceOrderId: (sourceOrderId: string) => this.executeWithLogging(
        'ProductionWorkOrderService', 'getBySourceOrderId', { sourceOrderId },
        () => this.productionWorkOrderService.getBySourceOrderId(sourceOrderId)
      ),

      getByWorkstation: (workstation: string) => this.executeWithLogging(
        'ProductionWorkOrderService', 'getByWorkstation', { workstation },
        () => this.productionWorkOrderService.getByWorkstation(workstation)
      )
    }
  }

  /**
   * 工位数据访问器
   * 🔧 修复：为工位数据添加特殊的缓存策略，确保排程算法获取最新状态
   */
  get workstations() {
    return {
      // 查询方法 - 使用内置缓存系统
      getAll: (params?: any) => this.executeWithLogging(
        'WorkstationService', 'getAll', params,
        () => this.workstationService.getAll()
      ),

      getWorkstations: (params?: any) => this.executeWithLogging(
        'WorkstationService', 'getWorkstations', params,
        () => this.workstationService.getWorkstations(params)
      ),

      getById: (id: string) => this.executeWithLogging(
        'WorkstationService', 'getWorkstationById', { id },
        () => this.workstationService.getWorkstationById(id)
      ),

      getActiveWorkstations: () => this.executeWithLogging(
        'WorkstationService', 'getActiveWorkstations', {},
        () => this.workstationService.getActiveWorkstations()
      ),

      // CRUD操作
      create: (data: any) => this.executeWithLogging(
        'WorkstationService', 'create', data,
        () => this.workstationService.create(data)
      ),

      update: (id: string, data: any) => this.executeWithLogging(
        'WorkstationService', 'update', { id, ...data },
        () => this.workstationService.update(id, data)
      ),

      delete: (id: string) => this.executeWithLogging(
        'WorkstationService', 'delete', { id },
        () => this.workstationService.delete(id)
      ),

      // 状态管理
      getStatus: (id: string) => this.executeWithLogging(
        'WorkstationService', 'getWorkstationStatus', { id },
        () => this.workstationService.getWorkstationStatus(id)
      ),

      updateStatus: (id: string, status: any) => this.executeWithLogging(
        'WorkstationService', 'updateWorkstationStatus', { id, ...status },
        () => this.workstationService.updateWorkstationStatus(id, status)
      ),

      // 队列管理
      addToQueue: (workstationId: string, batchNumber: string) => this.executeWithLogging(
        'WorkstationService', 'addToQueue', { workstationId, batchNumber },
        () => this.workstationService.addToQueue(workstationId, batchNumber)
      ),

      migrateBatchNumberFormats: () => this.executeWithLogging(
        'WorkstationService', 'migrateBatchNumberFormats', {},
        () => this.workstationService.migrateBatchNumberFormats()
      ),

      // 🔧 统一的工位重置方法
      resetAllWorkstationsToIdle: () => this.executeWithLogging(
        'WorkstationService', 'resetAllWorkstationsToIdle', {},
        () => this.workstationService.resetAllWorkstationsToIdle()
      )

    }
  }

  // ==================== 管理功能 ====================

  /**
   * 获取访问日志
   */
  getAccessLogs(limit?: number): AccessLog[] {
    return limit ? this.accessLogs.slice(-limit) : [...this.accessLogs]
  }

  /**
   * 清除访问日志
   */
  clearAccessLogs(): void {
    this.accessLogs = []
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const totalRequests = this.accessLogs.length
    const successfulRequests = this.accessLogs.filter(log => log.success).length
    const failedRequests = totalRequests - successfulRequests
    const averageDuration = totalRequests > 0 
      ? this.accessLogs.reduce((sum, log) => sum + log.duration, 0) / totalRequests 
      : 0

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      successRate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
      averageDuration: Math.round(averageDuration),
      config: this.config
    }
  }

  /**
   * 初始化数据变更通知机制
   */
  private initializeDataChangeNotification(): void {
    // 这里可以添加额外的初始化逻辑
    // 数据变更通知器已经在导入时自动初始化
  }



  /**
   * 获取数据变更通知器
   */
  getDataChangeNotifier() {
    return dataChangeNotifier
  }

  // 优先级同步服务已删除

  /**
   * 成本计算数据访问器
   */
  get costCalculations() {
    return {
      getAllCalculations: (params?: any) => this.executeWithLogging(
        'CostCalculationService', 'getAllCalculations', params,
        () => this.costCalculationService.getAllCalculations(params)
      ),

      getCalculationById: (id: string) => this.executeWithLogging(
        'CostCalculationService', 'getCalculationById', { id },
        () => this.costCalculationService.getCalculationById(id)
      ),

      createCalculation: (data: any) => this.executeWithLogging(
        'CostCalculationService', 'createCalculation', data,
        () => this.costCalculationService.createCalculation(data)
      ),

      updateCalculation: (id: string, data: any) => this.executeWithLogging(
        'CostCalculationService', 'updateCalculation', { id, ...data },
        () => this.costCalculationService.updateCalculation(id, data)
      ),

      deleteCalculation: (id: string) => this.executeWithLogging(
        'CostCalculationService', 'deleteCalculation', { id },
        () => this.costCalculationService.deleteCalculation(id)
      ),

      getCalculationsByProduct: (productModelCode: string) => this.executeWithLogging(
        'CostCalculationService', 'getCalculationsByProduct', { productModelCode },
        () => this.costCalculationService.getCalculationsByProduct(productModelCode)
      ),

      getPendingReconciliations: () => this.executeWithLogging(
        'CostCalculationService', 'getPendingReconciliations', {},
        () => this.costCalculationService.getPendingReconciliations()
      ),

      getStatistics: () => this.executeWithLogging(
        'CostCalculationService', 'getStatistics', {},
        () => this.costCalculationService.getStatistics()
      ),

      getCostSummary: () => this.executeWithLogging(
        'CostCalculationService', 'getCostSummary', {},
        () => this.costCalculationService.getCostSummary()
      )
    }
  }

  // 旧缓存管理方法已移除，使用内置缓存系统

  // 移除重复的方法定义，使用下面的统一缓存管理方法

  // invalidateCache(tags: string[]): void {
  //   this.cacheManager.deleteByTags(tags)
  // }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<DataAccessConfig>): void {
    this.config = { ...this.config, ...newConfig }

    // 如果禁用缓存，清空现有缓存
    if (newConfig.enableCaching === false) {
      this.cache.clear()
      this.requestCache.clear()
      if (this.config.enableLogging) {
        console.log('[DataAccessManager] 缓存已禁用并清空')
      }
    }

    if (this.config.enableLogging) {
      console.log('[DataAccessManager] 配置已更新:', this.config)
    }
  }

  // ==================== 缓存管理接口 ====================

  /**
   * 清除特定服务的缓存
   * 符合数据调用规范的缓存管理接口
   */
  clearServiceCache(serviceName: string, operation?: string): number {
    if (!this.config.enableCaching) {
      if (this.config.enableLogging) {
        console.log('[DataAccessManager] 缓存未启用，无需清理')
      }
      return 0
    }

    // ✅ 架构合规：使用内置缓存系统清理服务缓存
    let deletedCount = 0
    const pattern = `${serviceName}:`
    
    for (const [key] of Array.from(this.cache)) {
      if (key.startsWith(pattern)) {
        this.cache.delete(key)
        deletedCount++
      }
    }

    if (this.config.enableLogging) {
      console.log(`[DataAccessManager] 清除服务缓存: ${serviceName}${operation ? ` (${operation})` : ''}，删除了 ${deletedCount} 个缓存项`)
    }

    return deletedCount
  }

  /**
   * 清除特定数据类型的缓存
   * 符合数据调用规范的缓存管理接口
   */
  clearDataTypeCache(dataType: string, affectedIds?: string[]): number {
    if (!this.config.enableCaching) {
      if (this.config.enableLogging) {
        console.log('[DataAccessManager] 缓存未启用，无需清理')
      }
      return 0
    }

    let deletedCount = 0
    const serviceMap: { [key: string]: string } = {
      'products': 'ProductService',
      'orders': 'OrderService',
      'workstations': 'WorkstationService',
      'statistics': 'StatisticsService'
    }

    const serviceName = serviceMap[dataType] || dataType

    for (const [key] of Array.from(this.cache)) {
      if (key.startsWith(`${serviceName}:`)) {
        // 如果指定了特定ID，只删除相关的缓存
        if (affectedIds && affectedIds.length > 0) {
          const hasMatchingId = affectedIds.some(id => key.includes(id))
          if (hasMatchingId) {
            this.cache.delete(key)
            deletedCount++
          }
        } else {
          // 删除所有该数据类型的缓存
          this.cache.delete(key)
          deletedCount++
        }
      }
    }

    if (this.config.enableLogging) {
      console.log(`[DataAccessManager] 清除数据类型缓存: ${dataType}，影响ID: ${affectedIds?.join(', ') || '全部'}，删除了 ${deletedCount} 个缓存项`)
    }

    return deletedCount
  }

  /**
   * 清除所有缓存
   * 符合数据调用规范的缓存管理接口
   */
  clearAllCache(): void {
    if (!this.config.enableCaching) {
      if (this.config.enableLogging) {
        console.log('[DataAccessManager] 缓存未启用，无需清理')
      }
      return
    }

    // ✅ 架构合规：使用内置缓存系统清理所有缓存
    const beforeSize = this.cache.size
    this.cache.clear()
    this.requestCache.clear()

    if (this.config.enableLogging) {
      console.log(`[DataAccessManager] 清除所有缓存，删除了 ${beforeSize} 个缓存项`)
    }
  }

  /**
   * 获取缓存统计信息 - 优化版
   * 符合数据调用规范的监控接口
   */
  getCacheStatistics(): CacheStatistics {
    // ✅ 架构合规：使用内置缓存系统获取统计信息
    const entries = Array.from(this.cache.values())
    const totalSize = entries.reduce((sum, entry) => sum + entry.estimatedSize, 0)
    const averageEntrySize = entries.length > 0 ? totalSize / entries.length : 0
    
    // 计算优先级分布
    const priorityDistribution = { critical: 0, high: 0, medium: 0, low: 0 }
    entries.forEach(entry => {
      priorityDistribution[entry.priority]++
    })
    
    return {
      enabled: this.config.enableCaching,
      size: this.cache.size,
      hits: this.cacheStats.hits,
      misses: this.cacheStats.misses,
      hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0,
      totalRequests: this.cacheStats.totalRequests,
      // ✅ 优化：新增统计信息
      evictions: this.cacheStats.evictions,
      prewarmHits: this.cacheStats.prewarmHits,
      memoryPressure: this.memoryPressure,
      averageEntrySize,
      priorityDistribution
    }
  }

  /**
   * 更新缓存配置
   */
  updateCacheConfig(newConfig: Partial<CacheConfig>): void {
    this.cacheConfig = { ...this.cacheConfig, ...newConfig }
    console.log('[DataAccessManager] 缓存配置已更新')
  }

  // ✅ 架构合规：移除违规的CacheStrategyManager方法，使用内置缓存系统替代

  /**
   * 批量操作方法
   */
  get batch() {
    return {
      // 批量创建生产订单
      createProductionOrders: (orders: any[]) =>
        performanceOptimizer.batchCreateProductionOrders(orders),

      // 批量更新生产订单
      updateProductionOrders: (updates: Array<{id: string, data: any}>) =>
        performanceOptimizer.batchUpdateProductionOrders(updates),

      // 批量创建生产工单 - 使用并发控制优化
      createProductionWorkOrders: async (workOrders: any[]) => {
        const startTime = performance.now()
        try {
          // 创建任务数组
          const tasks = workOrders.map(workOrder => async () => {
            const result = await this.productionWorkOrders.create(workOrder)
            if (result.status !== 'success') {
              throw new Error(result.message || '创建工单失败')
            }
            return result.data
          })

          const taskNames = workOrders.map((_, index) => `批量创建工单-${index + 1}`)
          const batchResult = await batchOperationController.executeBatch(tasks, taskNames)

          const duration = performance.now() - startTime
          const { successful, failed } = batchResult
          const successfulData = successful.map(result => result.data!).filter(Boolean)

          dataAccessPerformanceMonitor.recordRequest('batch.createProductionWorkOrders', duration, failed.length === 0)

          return {
            status: 'success' as const,
            data: successfulData,
            message: `成功批量创建${successfulData.length}个生产工单${failed.length > 0 ? `，${failed.length}个失败` : ''}`,
            batchResult: {
              successful: successful.length,
              failed: failed.length,
              successRate: batchResult.successRate,
              totalDuration: duration
            }
          }
        } catch (error) {
          const duration = performance.now() - startTime
          dataAccessPerformanceMonitor.recordRequest('batch.createProductionWorkOrders', duration, false)
          throw error
        }
      },

      // 批量更新工位状态
      updateWorkstationStatuses: async (updates: Array<{id: string, status: any}>) => {
        const startTime = performance.now()
        try {
          const results = await Promise.all(
            updates.map(({ id, status }) => this.workstations.updateStatus(id, status))
          )
          const duration = performance.now() - startTime
          dataAccessPerformanceMonitor.recordRequest('batch.updateWorkstationStatuses', duration, true)

          // 使用内置缓存系统清理相关缓存
          this.clearDataTypeCache('workstations', updates.map(u => u.id))

          return {
            status: 'success' as const,
            data: results.filter(r => r.status === 'success').map(r => r.data),
            message: `成功批量更新${results.length}个工位状态`
          }
        } catch (error) {
          const duration = performance.now() - startTime
          dataAccessPerformanceMonitor.recordRequest('batch.updateWorkstationStatuses', duration, false)
          throw error
        }
      }
    }
  }

  /**
   * 性能监控方法
   */
  get performance() {
    return {
      // 获取性能指标
      getMetrics: () => dataAccessPerformanceMonitor.getMetrics(),

      // 获取性能警告
      getAlerts: () => dataAccessPerformanceMonitor.getAlerts(),

      // 获取优化建议
      getSuggestions: () => dataAccessPerformanceMonitor.getOptimizationSuggestions(),

      // 生成性能报告
      generateReport: () => dataAccessPerformanceMonitor.generatePerformanceReport(),

      // 重置性能指标
      reset: () => dataAccessPerformanceMonitor.reset(),

      // 优化缓存策略
      optimizeCache: () => {
        // 使用内置缓存系统进行优化
        this.evictOldestEntries()
        console.log('[DataAccessManager] 缓存优化完成')
      },

      // 获取缓存性能指标
      getCacheMetrics: () => {
        return this.getCacheStatistics()
      },

      // 获取性能优化器指标
      getOptimizerMetrics: () => performanceOptimizer.getPerformanceMetrics()
    }
  }

  // 旧的缓存管理方法已移除，使用内置缓存系统的统一接口

  /**
   * 性能监控管理方法
   */
  getPerformanceMetrics(): PerformanceMetrics {
    // 更新方法统计到性能指标中
    this.performanceMetrics.methodStats = this.methodStats
    return { ...this.performanceMetrics }
  }

  getRealTimeMetrics(): RealTimeMetrics {
    const batchControllerStatus = batchOperationController.getStatus()

    // 分析系统健康状况
    const systemHealth = this.analyzeSystemHealth()

    return {
      currentConcurrency: batchControllerStatus.running,
      queueLength: batchControllerStatus.queued,
      recentCalls: [...this.recentCalls.slice(-50)], // 最近50次调用
      systemHealth
    }
  }

  private analyzeSystemHealth(): RealTimeMetrics['systemHealth'] {
    const issues: string[] = []
    const recommendations: string[] = []

    // 检查错误率
    const recentErrorRate = this.recentCalls.length > 0
      ? this.recentCalls.filter(call => call.status === 'error').length / this.recentCalls.length
      : 0

    if (recentErrorRate > 0.1) { // 错误率超过10%
      issues.push(`错误率过高: ${(recentErrorRate * 100).toFixed(2)}%`)
      recommendations.push('检查网络连接和服务状态')
    }

    // 检查响应时间
    if (this.performanceMetrics.averageResponseTime > 2000) { // 平均响应时间超过2秒
      issues.push(`平均响应时间过长: ${this.performanceMetrics.averageResponseTime.toFixed(0)}ms`)
      recommendations.push('考虑优化查询或增加缓存')
    }

    // 检查缓存命中率
    if (this.performanceMetrics.cacheHitRate < 0.3) { // 缓存命中率低于30%
      issues.push(`缓存命中率较低: ${(this.performanceMetrics.cacheHitRate * 100).toFixed(2)}%`)
      recommendations.push('检查缓存策略和TTL设置')
    }

    // 检查并发队列
    const batchStatus = batchOperationController.getStatus()
    if (batchStatus.queued > 10) {
      issues.push(`批量操作队列积压: ${batchStatus.queued}个任务`)
      recommendations.push('考虑增加并发数或优化任务处理')
    }

    // 确定系统状态
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    if (issues.length > 0) {
      status = recentErrorRate > 0.2 || this.performanceMetrics.averageResponseTime > 5000 ? 'critical' : 'warning'
    }

    return { status, issues, recommendations }
  }

  resetPerformanceMetrics(): void {
    this.initializePerformanceMonitoring()
    console.log('[DataAccessManager] 性能指标已重置')
  }

  getSlowQueries(limit: number = 20): PerformanceMetrics['slowQueries'] {
    return this.performanceMetrics.slowQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  getMethodStatistics(): Array<{
    method: string
    calls: number
    averageTime: number
    successRate: number
    lastCall: string
  }> {
    return Array.from(this.methodStats.entries())
      .map(([method, stats]) => ({
        method,
        calls: stats.calls,
        averageTime: stats.averageTime,
        successRate: stats.successRate,
        lastCall: new Date(stats.lastCall).toLocaleString()
      }))
      .sort((a, b) => b.calls - a.calls) // 按调用次数排序
  }

  exportPerformanceReport(): {
    timestamp: string
    summary: PerformanceMetrics
    realTime: RealTimeMetrics
    topMethods: Array<{ method: string; calls: number; averageTime: number }>
    slowQueries: PerformanceMetrics['slowQueries']
  } {
    return {
      timestamp: new Date().toISOString(),
      summary: this.getPerformanceMetrics(),
      realTime: this.getRealTimeMetrics(),
      topMethods: this.getMethodStatistics().slice(0, 10),
      slowQueries: this.getSlowQueries(10)
    }
  }

  // ==================== 批量操作 ====================

  /**
   * 批量操作访问器
   * 提供批量创建、更新、删除等操作
   */
  get batch() {
    return {
      // 批量创建生产订单
      createProductionOrders: async (orders: any[]) => {
        const results = []
        const errors = []
        
        for (const order of orders) {
          try {
            const result = await this.productionOrders.create(order)
            if (result.status === 'success') {
              results.push(result.data)
            } else {
              errors.push(result.message || '创建失败')
            }
          } catch (error) {
            errors.push(error instanceof Error ? error.message : '未知错误')
          }
        }
        
        if (errors.length === 0) {
          return {
            status: 'success' as const,
            data: results,
            message: `成功创建${results.length}个生产订单`
          }
        } else {
          return {
            status: 'error' as const,
            data: results,
            message: `创建过程中发生错误: ${errors.join(', ')}`,
            errors
          }
        }
      },

      // 批量更新生产订单
      updateProductionOrders: async (updates: Array<{id: string, data: any}>) => {
        const results = []
        const errors = []
        
        for (const { id, data } of updates) {
          try {
            const result = await this.productionOrders.update(id, data)
            if (result.status === 'success') {
              results.push(result.data)
            } else {
              errors.push(`ID ${id}: ${result.message || '更新失败'}`)
            }
          } catch (error) {
            errors.push(`ID ${id}: ${error instanceof Error ? error.message : '未知错误'}`)
          }
        }
        
        if (errors.length === 0) {
          return {
            status: 'success' as const,
            data: results,
            message: `成功更新${results.length}个生产订单`
          }
        } else {
          return {
            status: 'error' as const,
            data: results,
            message: `更新过程中发生错误: ${errors.join(', ')}`,
            errors
          }
        }
      },

      // 批量删除生产订单
      deleteProductionOrders: async (ids: string[]) => {
        const successIds = []
        const errors = []
        
        for (const id of ids) {
          try {
            const result = await this.productionOrders.delete(id)
            if (result.status === 'success') {
              successIds.push(id)
            } else {
              errors.push(`ID ${id}: ${result.message || '删除失败'}`)
            }
          } catch (error) {
            errors.push(`ID ${id}: ${error instanceof Error ? error.message : '未知错误'}`)
          }
        }
        
        if (errors.length === 0) {
          return {
            status: 'success' as const,
            data: true,
            message: `成功删除${successIds.length}个生产订单`
          }
        } else {
          return {
            status: 'error' as const,
            data: false,
            message: `删除过程中发生错误: ${errors.join(', ')}`,
            errors
          }
        }
      },

      // 批量查询生产订单
      getProductionOrders: async (ids: string[]) => {
        const results = []
        const errors = []
        
        for (const id of ids) {
          try {
            const result = await this.productionOrders.getById(id)
            if (result.status === 'success') {
              results.push(result.data)
            } else {
              errors.push(`ID ${id}: ${result.message || '查询失败'}`)
            }
          } catch (error) {
            errors.push(`ID ${id}: ${error instanceof Error ? error.message : '未知错误'}`)
          }
        }
        
        if (errors.length === 0) {
          return {
            status: 'success' as const,
            data: results,
            message: `成功查询${results.length}个生产订单`
          }
        } else {
          return {
            status: 'error' as const,
            data: results,
            message: `查询过程中发生错误: ${errors.join(', ')}`,
            errors
          }
        }
      }
    }
  }

  // ==================== 配置管理 ====================

  /**
   * 系统配置管理
   * 提供动态配置更新和查询功能
   */
  private systemConfig = {
    enableCaching: true,
    cacheTimeout: 5 * 60 * 1000, // 5分钟
    enableLogging: true,
    logLevel: 'INFO' as 'DEBUG' | 'INFO' | 'WARN' | 'ERROR',
    enablePerformanceMonitoring: true,
    maxConcurrentRequests: 10,
    retryAttempts: 3,
    retryDelay: 1000
  }

  /**
   * 获取系统配置
   */
  getConfig() {
    return { ...this.systemConfig }
  }

  /**
   * 更新系统配置
   */
  updateConfig(config: Partial<typeof this.systemConfig>) {
    this.systemConfig = { ...this.systemConfig, ...config }
    console.log('[DataAccessManager] 系统配置已更新:', config)
    
    // 根据配置更新对应的系统行为
    if (config.enableCaching !== undefined) {
      console.log(`[DataAccessManager] 缓存状态: ${config.enableCaching ? '已启用' : '已禁用'}`)
    }
    
    if (config.cacheTimeout !== undefined) {
      console.log(`[DataAccessManager] 缓存超时时间: ${config.cacheTimeout}ms`)
    }
    
    if (config.enableLogging !== undefined) {
      console.log(`[DataAccessManager] 日志状态: ${config.enableLogging ? '已启用' : '已禁用'}`)
    }
    
    if (config.logLevel !== undefined) {
      console.log(`[DataAccessManager] 日志级别: ${config.logLevel}`)
    }
  }

  /**
   * 重置配置到默认值
   */
  resetConfig() {
    this.systemConfig = {
      enableCaching: true,
      cacheTimeout: 5 * 60 * 1000,
      enableLogging: true,
      logLevel: 'INFO',
      enablePerformanceMonitoring: true,
      maxConcurrentRequests: 10,
      retryAttempts: 3,
      retryDelay: 1000
    }
    console.log('[DataAccessManager] 系统配置已重置')
  }
}

// 创建单例实例
export const dataAccessManager = DataAccessManager.getInstance()

// 🔧 将DataAccessManager暴露到全局作用域（用于调试和测试）
if (typeof window !== 'undefined') {
  (window as any).dataAccessManager = dataAccessManager
  console.log('🔧 [DataAccessManager] 已暴露到全局作用域 window.dataAccessManager')
}

export const {
  products,
  customers,
  employees,
  inventory,
  productionOrders,
  productionWorkOrders,
  workstations,
  costCalculations,
  auth,
  tokenManagement
} = dataAccessManager
