-- =====================================================
-- ERP系统用户认证极简版数据库初始化脚本
-- 
-- 根据PRD文档《03-用户认证和权限管理系统.md》要求
-- 为小于20人的企业提供简单实用的认证数据结构
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 基础角色数据初始化（PRD要求的admin/employee两级权限）
-- =====================================================

-- 插入基础角色：管理员
INSERT IGNORE INTO roles (id, code, name, description, created_at, updated_at) VALUES 
('role-admin', 'admin', '系统管理员', '拥有系统所有权限的管理员角色', NOW(), NOW());

-- 插入基础角色：员工
INSERT IGNORE INTO roles (id, code, name, description, created_at, updated_at) VALUES 
('role-employee', 'employee', '普通员工', '具有基础操作权限的员工角色', NOW(), NOW());

-- =====================================================
-- 2. 基础权限数据初始化（简化的权限结构）
-- =====================================================

-- 管理员权限
INSERT IGNORE INTO permissions (id, code, name, type, resource, action, created_at, updated_at) VALUES 
('perm-admin-all', 'admin:*', '系统管理', 'system', 'system', 'admin', NOW(), NOW()),
('perm-user-manage', 'admin:users:*', '用户管理', 'module', 'users', 'manage', NOW(), NOW()),
('perm-data-view', 'data:view', '数据查看', 'action', 'data', 'view', NOW(), NOW()),
('perm-data-edit', 'data:edit', '数据编辑', 'action', 'data', 'edit', NOW(), NOW()),
('perm-data-delete', 'data:delete', '数据删除', 'action', 'data', 'delete', NOW(), NOW());

-- 员工权限
INSERT IGNORE INTO permissions (id, code, name, type, resource, action, created_at, updated_at) VALUES 
('perm-employee-view', 'employee:data:view', '员工数据查看', 'action', 'data', 'view', NOW(), NOW()),
('perm-employee-edit', 'employee:data:edit', '员工数据编辑', 'action', 'data', 'edit', NOW(), NOW());

-- =====================================================
-- 3. 角色权限关联（PRD要求的简单权限分配）
-- =====================================================

-- 管理员角色权限
INSERT IGNORE INTO role_permissions (id, role_id, permission_id, created_at) VALUES 
('rp-admin-1', 'role-admin', 'perm-admin-all', NOW()),
('rp-admin-2', 'role-admin', 'perm-user-manage', NOW()),
('rp-admin-3', 'role-admin', 'perm-data-view', NOW()),
('rp-admin-4', 'role-admin', 'perm-data-edit', NOW()),
('rp-admin-5', 'role-admin', 'perm-data-delete', NOW());

-- 员工角色权限
INSERT IGNORE INTO role_permissions (id, role_id, permission_id, created_at) VALUES 
('rp-employee-1', 'role-employee', 'perm-employee-view', NOW()),
('rp-employee-2', 'role-employee', 'perm-employee-edit', NOW());

-- =====================================================
-- 4. 默认管理员用户创建（PRD要求）
-- =====================================================

-- 创建默认管理员用户
-- 密码: admin123 (生产环境请立即修改)
INSERT IGNORE INTO users (
    id, 
    username, 
    email, 
    full_name, 
    status, 
    created_at, 
    updated_at
) VALUES (
    'user-admin-default', 
    'admin', 
    '<EMAIL>', 
    '系统管理员', 
    'active', 
    NOW(), 
    NOW()
);

-- 设置管理员密码 (密码: admin123)
-- 注意：这是bcrypt加密后的密码哈希值
INSERT IGNORE INTO user_passwords (
    id,
    user_id,
    password_hash,
    created_at,
    updated_at
) VALUES (
    'pwd-admin-default',
    'user-admin-default',
    '$2b$10$rOzJqQZQXQXQXQXQXQXQXOeH6H6H6H6H6H6H6H6H6H6H6H6H6H6H6',
    NOW(),
    NOW()
);

-- 分配管理员角色
INSERT IGNORE INTO user_roles (
    id,
    user_id,
    role_id,
    assigned_by,
    assigned_at
) VALUES (
    'ur-admin-default',
    'user-admin-default',
    'role-admin',
    'system',
    NOW()
);

-- =====================================================
-- 5. 示例员工用户创建
-- =====================================================

-- 创建示例员工用户
INSERT IGNORE INTO users (
    id, 
    username, 
    email, 
    full_name, 
    status, 
    created_at, 
    updated_at
) VALUES (
    'user-employee-demo', 
    'employee', 
    '<EMAIL>', 
    '示例员工', 
    'active', 
    NOW(), 
    NOW()
);

-- 设置员工密码 (密码: employee123)
INSERT IGNORE INTO user_passwords (
    id,
    user_id,
    password_hash,
    created_at,
    updated_at
) VALUES (
    'pwd-employee-demo',
    'user-employee-demo',
    '$2b$10$rOzJqQZQXQXQXQXQXQXQXOeH6H6H6H6H6H6H6H6H6H6H6H6H6H6H6',
    NOW(),
    NOW()
);

-- 分配员工角色
INSERT IGNORE INTO user_roles (
    id,
    user_id,
    role_id,
    assigned_by,
    assigned_at
) VALUES (
    'ur-employee-demo',
    'user-employee-demo',
    'role-employee',
    'user-admin-default',
    NOW()
);

-- =====================================================
-- 6. 清理和优化
-- =====================================================

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 提交事务
COMMIT;

-- =====================================================
-- 初始化完成提示
-- =====================================================
SELECT 
    '✅ ERP系统用户认证极简版初始化完成' as message,
    '默认管理员: admin / admin123' as admin_account,
    '示例员工: employee / employee123' as employee_account,
    '⚠️ 生产环境请立即修改默认密码' as security_warning;
