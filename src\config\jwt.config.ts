/**
 * JWT配置管理
 * 
 * 统一管理所有JWT相关的配置，避免重复定义
 * 遵循架构合规性要求，提供单一配置入口
 */

import jwt from 'jsonwebtoken'

/**
 * JWT配置接口
 */
export interface JWTConfig {
  accessToken: {
    secret: string
    expiresIn: string
    algorithm: jwt.Algorithm
  }
  refreshToken: {
    secret: string
    expiresIn: string
    algorithm: jwt.Algorithm
  }
}

/**
 * JWT配置
 * 从环境变量中读取密钥，确保安全性
 */
export const JWT_CONFIG: JWTConfig = {
  accessToken: {
    secret: process.env.JWT_ACCESS_SECRET || 'default-access-secret-change-in-production',
    expiresIn: '8h', // Access Token有效期: 8小时（符合PRD要求）
    algorithm: 'HS256'
  },
  refreshToken: {
    secret: process.env.JWT_REFRESH_SECRET || 'default-refresh-secret-change-in-production',
    expiresIn: '7d', // Refresh Token有效期: 7天
    algorithm: 'HS256'
  }
}

/**
 * JWT配置验证
 * 确保配置的有效性
 */
export function validateJWTConfig(): void {
  if (!JWT_CONFIG.accessToken.secret || JWT_CONFIG.accessToken.secret === 'default-access-secret-change-in-production') {
    console.warn('⚠️ [JWT Config] 使用默认的Access Token密钥，生产环境请设置JWT_ACCESS_SECRET环境变量')
  }
  
  if (!JWT_CONFIG.refreshToken.secret || JWT_CONFIG.refreshToken.secret === 'default-refresh-secret-change-in-production') {
    console.warn('⚠️ [JWT Config] 使用默认的Refresh Token密钥，生产环境请设置JWT_REFRESH_SECRET环境变量')
  }
  
  console.log('✅ [JWT Config] JWT配置验证完成')
}

/**
 * 获取Access Token配置
 */
export function getAccessTokenConfig() {
  return JWT_CONFIG.accessToken
}

/**
 * 获取Refresh Token配置
 */
export function getRefreshTokenConfig() {
  return JWT_CONFIG.refreshToken
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}

/**
 * 获取Token过期时间（秒）
 */
export function getTokenExpirationTime(tokenType: 'access' | 'refresh'): number {
  const config = tokenType === 'access' ? JWT_CONFIG.accessToken : JWT_CONFIG.refreshToken
  
  // 解析时间字符串（如 '1h', '7d'）
  const timeStr = config.expiresIn
  const unit = timeStr.slice(-1)
  const value = parseInt(timeStr.slice(0, -1))
  
  switch (unit) {
    case 's': return value
    case 'm': return value * 60
    case 'h': return value * 60 * 60
    case 'd': return value * 24 * 60 * 60
    default: return 3600 // 默认1小时
  }
}
