"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_services_scheduling_SchedulingStateManager_ts"],{

/***/ "(app-pages-browser)/./src/services/scheduling/SchedulingStateManager.ts":
/*!***********************************************************!*\
  !*** ./src/services/scheduling/SchedulingStateManager.ts ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchedulingStateManager: function() { return /* binding */ SchedulingStateManager; }\n/* harmony export */ });\n/* harmony import */ var _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/dataAccess/DataAccessManager */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\");\n/**\n * 排程状态管理服务\n * 负责管理排程过程中的虚拟状态计算和状态变更记录\n * 注意：实际状态应用已统一到 SameMoldPrioritySchedulingService.applySchedulingResults\n */ \n/**\n * 排程状态管理器\n */ class SchedulingStateManager {\n    /**\n   * 创建工位状态快照（使用dataAccessManager）\n   */ static async createWorkstationSnapshot(description) {\n        try {\n            var _response_data;\n            // 使用dataAccessManager获取当前工位状态\n            const response = await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.workstations.getWorkstations();\n            if (response.status !== \"success\" || !((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.items)) {\n                throw new Error(\"获取工位数据失败\");\n            }\n            const workstations = response.data.items;\n            // 记录状态变更\n            const changeRecord = {\n                id: \"snapshot_\".concat(Date.now()),\n                timestamp: new Date().toISOString(),\n                operation: \"snapshot\",\n                description: description || \"排程前工位状态快照\",\n                affectedWorkstations: workstations.map((ws)=>ws.id),\n                affectedWorkOrders: []\n            };\n            this.changeHistory.push(changeRecord);\n            console.log(\"\\uD83D\\uDCF8 [SchedulingStateManager] 创建工位状态快照: \".concat(workstations.length, \" 个工位\"));\n            return workstations;\n        } catch (error) {\n            console.error(\"❌ [SchedulingStateManager] 创建工位状态快照失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 创建虚拟工位状态（用于计算阶段）\n   */ static createVirtualWorkstationStates(originalWorkstations) {\n        const virtualWorkstations = originalWorkstations.map((ws)=>({\n                ...JSON.parse(JSON.stringify(ws)),\n                isVirtual: true,\n                originalState: {\n                    ...ws\n                }\n            }));\n        return virtualWorkstations;\n    }\n    /**\n   * 计算虚拟工位状态变更（不影响实际状态）\n   */ static calculateVirtualStateChanges(virtualWorkstations, results) {\n        // 按工位分组排程结果\n        const resultsByWorkstation = results.reduce((groups, result)=>{\n            const key = result.workstationCode;\n            if (!groups[key]) {\n                groups[key] = [];\n            }\n            groups[key].push(result);\n            return groups;\n        }, {});\n        // 更新虚拟工位状态\n        const updatedVirtualWorkstations = virtualWorkstations.map((vws)=>{\n            const workstationResults = resultsByWorkstation[vws.code] || [];\n            if (workstationResults.length === 0) {\n                return vws // 无变更\n                ;\n            }\n            // 按结束时间排序，取最后一个作为最终状态\n            const sortedResults = workstationResults.sort((a, b)=>new Date(a.plannedEndTime).getTime() - new Date(b.plannedEndTime).getTime());\n            const lastResult = sortedResults[sortedResults.length - 1];\n            // 更新虚拟工位状态\n            const updatedVws = {\n                ...vws,\n                lastEndTime: lastResult.plannedEndTime,\n                currentMoldNumber: lastResult.formingMoldNumber,\n                currentBatchNumber: lastResult.batchNumber,\n                batchNumberQueue: [\n                    ...vws.batchNumberQueue || [],\n                    ...workstationResults.map((r)=>r.batchNumber)\n                ]\n            };\n            console.log(\"\\uD83D\\uDD04 [SchedulingStateManager] 虚拟工位状态更新: \".concat(vws.code));\n            return updatedVws;\n        });\n        // 记录虚拟计算操作\n        const changeRecord = {\n            id: \"virtual_calc_\".concat(Date.now()),\n            timestamp: new Date().toISOString(),\n            operation: \"virtual_calculation\",\n            description: \"虚拟工位状态计算完成\",\n            affectedWorkstations: [\n                ...new Set(results.map((r)=>r.workstationCode))\n            ],\n            affectedWorkOrders: results.map((r)=>r.workOrderId)\n        };\n        this.changeHistory.push(changeRecord);\n        return updatedVirtualWorkstations;\n    }\n    /**\n   * @deprecated 此方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults\n   * 实际状态应用已统一到 SameMoldPrioritySchedulingService.applySchedulingResults 方法\n   */ static async applySchedulingToActualState(results) {\n        console.warn(\"⚠️ [SchedulingStateManager] applySchedulingToActualState 方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults\");\n        throw new Error(\"此方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults\");\n    }\n    /**\n   * @deprecated 此方法已废弃，回滚功能需要通过具体的业务逻辑实现\n   * 排程回滚应该通过重新计算和应用来实现，而不是依赖快照恢复\n   */ static rollbackStateChanges(snapshot) {\n        console.warn(\"⚠️ [SchedulingStateManager] rollbackStateChanges 方法已废弃\");\n        console.warn(\"排程回滚应该通过重新计算和应用来实现，而不是依赖快照恢复\");\n        // 记录回滚尝试\n        const changeRecord = {\n            id: \"rollback_attempt_\".concat(Date.now()),\n            timestamp: new Date().toISOString(),\n            operation: \"virtual_calculation\",\n            description: \"尝试回滚操作（已废弃）\",\n            affectedWorkstations: [],\n            affectedWorkOrders: []\n        };\n        this.changeHistory.push(changeRecord);\n    }\n    /**\n   * @deprecated 此方法已废弃，工位状态更新逻辑已统一到 SameMoldPrioritySchedulingService\n   */ static calculateWorkstationUpdates(results) {\n        console.warn(\"⚠️ [SchedulingStateManager] calculateWorkstationUpdates 方法已废弃\");\n        return [];\n    }\n    /**\n   * 获取状态变更历史\n   */ static getChangeHistory() {\n        return [\n            ...this.changeHistory\n        ];\n    }\n    /**\n   * 清理状态变更历史\n   */ static clearChangeHistory() {\n        this.changeHistory = [];\n    }\n    /**\n   * 获取当前排程状态信息\n   */ static getCurrentSchedulingState() {\n        return {\n            changeHistoryCount: this.changeHistory.length,\n            lastChangeTime: this.changeHistory.length > 0 ? this.changeHistory[this.changeHistory.length - 1].timestamp : null,\n            operations: this.changeHistory.map((record)=>({\n                    id: record.id,\n                    operation: record.operation,\n                    description: record.description,\n                    timestamp: record.timestamp\n                }))\n        };\n    }\n}\nSchedulingStateManager.changeHistory = [];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/scheduling/SchedulingStateManager.ts\n"));

/***/ })

}]);