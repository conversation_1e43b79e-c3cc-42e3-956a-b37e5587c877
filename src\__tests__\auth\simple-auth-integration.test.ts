/**
 * 简化认证系统集成测试
 * 
 * 验证重构后的认证系统功能完整性
 */

import { SimpleAuthService, SimpleUser } from '@/services/auth/SimpleAuthService'
import { SimplePermissionManager } from '@/utils/auth/SimplePermissionManager'
import { User, Role, Permission } from '@/types/auth'

// 模拟用户数据
const mockAdminUser: User = {
  id: 'user-admin-test',
  username: 'admin',
  fullName: '测试管理员',
  email: '<EMAIL>',
  status: 'active',
  roles: [
    {
      id: 'role-admin',
      code: 'admin',
      name: '系统管理员',
      description: '系统管理员角色',
      permissions: [
        {
          id: 'perm-admin',
          code: 'system:admin',
          name: '系统管理',
          type: 'module',
          resource: 'system',
          action: 'admin',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

const mockEmployeeUser: User = {
  id: 'user-employee-test',
  username: 'employee',
  fullName: '测试员工',
  email: '<EMAIL>',
  status: 'active',
  roles: [
    {
      id: 'role-user',
      code: 'user',
      name: '普通用户',
      description: '普通用户角色',
      permissions: [
        {
          id: 'perm-read',
          code: 'data:read',
          name: '数据查看',
          type: 'operation',
          resource: 'data',
          action: 'read',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

describe('简化认证系统集成测试', () => {
  
  describe('角色映射功能', () => {
    test('应该正确映射管理员角色', () => {
      const simpleUser = SimpleAuthService.createSimpleUser(mockAdminUser)
      
      expect(simpleUser.role).toBe('admin')
      expect(SimpleAuthService.isAdmin(simpleUser)).toBe(true)
      expect(SimpleAuthService.isEmployee(simpleUser)).toBe(false)
    })

    test('应该正确映射员工角色', () => {
      const simpleUser = SimpleAuthService.createSimpleUser(mockEmployeeUser)
      
      expect(simpleUser.role).toBe('employee')
      expect(SimpleAuthService.isAdmin(simpleUser)).toBe(false)
      expect(SimpleAuthService.isEmployee(simpleUser)).toBe(true)
    })

    test('应该处理空角色列表', () => {
      const userWithoutRoles: User = {
        ...mockEmployeeUser,
        roles: []
      }
      
      const simpleUser = SimpleAuthService.createSimpleUser(userWithoutRoles)
      expect(simpleUser.role).toBe('employee')
    })
  })

  describe('权限检查功能', () => {
    test('管理员应该拥有所有权限', () => {
      expect(SimpleAuthService.hasPermission('admin', 'admin')).toBe(true)
      expect(SimpleAuthService.hasPermission('admin', 'employee')).toBe(true)
    })

    test('员工应该只有员工权限', () => {
      expect(SimpleAuthService.hasPermission('employee', 'admin')).toBe(false)
      expect(SimpleAuthService.hasPermission('employee', 'employee')).toBe(true)
    })
  })

  describe('SimplePermissionManager集成', () => {
    test('权限级别应该与SimpleAuthService一致', () => {
      const adminLevel = SimplePermissionManager.getUserPermissionLevel(mockAdminUser)
      const employeeLevel = SimplePermissionManager.getUserPermissionLevel(mockEmployeeUser)
      
      expect(adminLevel).toBe('admin')
      expect(employeeLevel).toBe('employee')
    })

    test('权限检查应该正常工作', () => {
      const adminPermission = SimplePermissionManager.checkPermission(mockAdminUser, 'admin:users:create')
      const employeePermission = SimplePermissionManager.checkPermission(mockEmployeeUser, 'admin:users:create')
      
      expect(adminPermission.hasPermission).toBe(true)
      expect(employeePermission.hasPermission).toBe(false)
    })
  })

  describe('角色映射验证', () => {
    test('应该验证角色映射的正确性', () => {
      const adminValidation = SimpleAuthService.validateRoleMapping(mockAdminUser.roles, 'admin')
      const employeeValidation = SimpleAuthService.validateRoleMapping(mockEmployeeUser.roles, 'employee')
      
      expect(adminValidation.isValid).toBe(true)
      expect(employeeValidation.isValid).toBe(true)
    })

    test('应该检测权限降级问题', () => {
      const validation = SimpleAuthService.validateRoleMapping(mockAdminUser.roles, 'employee')
      
      expect(validation.isValid).toBe(false)
      expect(validation.warnings.length).toBeGreaterThan(0)
    })
  })

  describe('辅助功能', () => {
    test('应该返回正确的角色显示名称', () => {
      expect(SimpleAuthService.getRoleDisplayName('admin')).toBe('管理员')
      expect(SimpleAuthService.getRoleDisplayName('employee')).toBe('员工')
    })

    test('应该返回权限描述', () => {
      const adminUser: SimpleUser = { id: '1', username: 'admin', role: 'admin' }
      const employeeUser: SimpleUser = { id: '2', username: 'employee', role: 'employee' }
      
      const adminDesc = SimpleAuthService.getUserPermissionDescription(adminUser)
      const employeeDesc = SimpleAuthService.getUserPermissionDescription(employeeUser)
      
      expect(adminDesc).toContain('系统所有权限')
      expect(employeeDesc).toContain('业务功能')
    })
  })

  describe('安全配置', () => {
    test('应该返回安全配置信息', () => {
      const config = SimpleAuthService.getSecurityConfig()
      
      expect(config).toHaveProperty('passwordPolicy')
      expect(config).toHaveProperty('sessionDuration')
      expect(config).toHaveProperty('maxLoginAttempts')
      expect(config).toHaveProperty('lockoutDuration')
    })

    test('密码验证应该正常工作', () => {
      const validPassword = SimpleAuthService.validatePasswordStrength('123456')
      const invalidPassword = SimpleAuthService.validatePasswordStrength('123')
      
      expect(validPassword.isValid).toBe(true)
      expect(invalidPassword.isValid).toBe(false)
      expect(invalidPassword.errors.length).toBeGreaterThan(0)
    })
  })
})
