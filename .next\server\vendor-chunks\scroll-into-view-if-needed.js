"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/scroll-into-view-if-needed";
exports.ids = ["vendor-chunks/scroll-into-view-if-needed"];
exports.modules = {

/***/ "(ssr)/./node_modules/scroll-into-view-if-needed/dist/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/scroll-into-view-if-needed/dist/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\n/* harmony import */ var compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! compute-scroll-into-view */ \"(ssr)/./node_modules/compute-scroll-into-view/dist/index.js\");\n\nconst o = (t)=>!1 === t ? {\n        block: \"end\",\n        inline: \"nearest\"\n    } : ((t)=>t === Object(t) && 0 !== Object.keys(t).length)(t) ? t : {\n        block: \"start\",\n        inline: \"nearest\"\n    };\nfunction e(e, r) {\n    if (!e.isConnected || !((t)=>{\n        let o = t;\n        for(; o && o.parentNode;){\n            if (o.parentNode === document) return !0;\n            o = o.parentNode instanceof ShadowRoot ? o.parentNode.host : o.parentNode;\n        }\n        return !1;\n    })(e)) return;\n    const n = ((t)=>{\n        const o = window.getComputedStyle(t);\n        return {\n            top: parseFloat(o.scrollMarginTop) || 0,\n            right: parseFloat(o.scrollMarginRight) || 0,\n            bottom: parseFloat(o.scrollMarginBottom) || 0,\n            left: parseFloat(o.scrollMarginLeft) || 0\n        };\n    })(e);\n    if (((t)=>\"object\" == typeof t && \"function\" == typeof t.behavior)(r)) return r.behavior((0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__.compute)(e, r));\n    const l = \"boolean\" == typeof r || null == r ? void 0 : r.behavior;\n    for (const { el: a, top: i, left: s } of (0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__.compute)(e, o(r))){\n        const t = i - n.top + n.bottom, o = s - n.left + n.right;\n        a.scroll({\n            top: t,\n            left: o,\n            behavior: l\n        });\n    }\n}\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/scroll-into-view-if-needed/dist/index.js\n");

/***/ })

};
;