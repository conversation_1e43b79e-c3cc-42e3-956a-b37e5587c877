#!/usr/bin/env node

/**
 * 认证架构合规性检查脚本
 * 
 * 自动检查认证系统的架构合规性
 * 可以在CI/CD流程中使用
 */

const fs = require('fs')
const path = require('path')

class ArchitectureChecker {
  constructor() {
    this.violations = []
    this.warnings = []
    this.srcDir = path.join(process.cwd(), 'src')
  }

  /**
   * 运行所有检查
   */
  async runAllChecks() {
    console.log('🔍 开始认证架构合规性检查...\n')

    this.checkDuplicateTokenManagers()
    this.checkJWTConfigDuplication()
    this.checkAPIRouteCompliance()
    this.checkImportViolations()
    this.checkFileStructure()

    this.printResults()
    
    // 如果有违规，退出码为1
    if (this.violations.length > 0) {
      process.exit(1)
    }
  }

  /**
   * 检查重复的Token管理器
   */
  checkDuplicateTokenManagers() {
    console.log('📋 检查重复的Token管理器...')
    
    const authDir = path.join(this.srcDir, 'utils/auth')
    const duplicateFiles = [
      'SimpleTokenManager.ts',
      'ServerTokenManager.ts'
    ]
    
    duplicateFiles.forEach(file => {
      const filePath = path.join(authDir, file)
      if (fs.existsSync(filePath)) {
        this.violations.push(`❌ 发现重复的Token管理器: ${file}`)
      }
    })
    
    console.log('✅ Token管理器重复检查完成\n')
  }

  /**
   * 检查JWT配置重复
   */
  checkJWTConfigDuplication() {
    console.log('📋 检查JWT配置重复...')
    
    const jwtConfigPattern = /const\s+JWT_CONFIG.*=.*{/g
    let configCount = 0
    const configFiles = []
    
    this.scanDirectory(this.srcDir, (filePath, content) => {
      if (filePath.endsWith('.ts') && !filePath.endsWith('.test.ts')) {
        const matches = content.match(jwtConfigPattern)
        if (matches) {
          configCount += matches.length
          configFiles.push(filePath)
        }
      }
    })
    
    if (configCount > 1) {
      this.violations.push(`❌ 发现${configCount}个JWT配置定义，应该只有一个`)
      configFiles.forEach(file => {
        this.violations.push(`   - ${file}`)
      })
    }
    
    console.log('✅ JWT配置重复检查完成\n')
  }

  /**
   * 检查API路由合规性
   */
  checkAPIRouteCompliance() {
    console.log('📋 检查API路由架构合规性...')
    
    const apiDir = path.join(this.srcDir, 'app/api')
    
    if (!fs.existsSync(apiDir)) {
      this.warnings.push('⚠️ API目录不存在，跳过API路由检查')
      return
    }
    
    this.scanDirectory(apiDir, (filePath, content) => {
      if (filePath.endsWith('.ts')) {
        // 检查直接导入Token管理器
        const directImports = [
          /import.*SimpleTokenManager/,
          /import.*ServerTokenManager/,
          /import.*TokenManager.*from.*utils\/auth/
        ]
        
        directImports.forEach(pattern => {
          if (pattern.test(content)) {
            this.violations.push(`❌ API路由直接导入Token管理器: ${filePath}`)
          }
        })
        
        // 检查是否使用dataAccessManager.tokenManagement
        if (content.includes('TokenManager.') && !content.includes('dataAccessManager.tokenManagement')) {
          this.violations.push(`❌ API路由未使用dataAccessManager.tokenManagement: ${filePath}`)
        }
      }
    })
    
    console.log('✅ API路由合规性检查完成\n')
  }

  /**
   * 检查导入违规
   */
  checkImportViolations() {
    console.log('📋 检查导入违规...')
    
    const violationPatterns = [
      {
        pattern: /import.*SimpleTokenManager.*from/g,
        message: '不应该导入SimpleTokenManager'
      },
      {
        pattern: /import.*ServerTokenManager.*from/g,
        message: '不应该导入ServerTokenManager'
      }
    ]
    
    this.scanDirectory(this.srcDir, (filePath, content) => {
      if (filePath.endsWith('.ts') && !filePath.endsWith('.test.ts')) {
        violationPatterns.forEach(({ pattern, message }) => {
          if (pattern.test(content)) {
            this.violations.push(`❌ ${message}: ${filePath}`)
          }
        })
      }
    })
    
    console.log('✅ 导入违规检查完成\n')
  }

  /**
   * 检查文件结构
   */
  checkFileStructure() {
    console.log('📋 检查文件结构...')
    
    const requiredFiles = [
      'src/config/jwt.config.ts',
      'src/services/auth/TokenValidator.ts',
      'src/services/auth/AuthErrorHandler.ts',
      'src/services/dataAccess/TokenManagementService.ts'
    ]
    
    requiredFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file)
      if (!fs.existsSync(filePath)) {
        this.violations.push(`❌ 缺少必需文件: ${file}`)
      }
    })
    
    console.log('✅ 文件结构检查完成\n')
  }

  /**
   * 扫描目录
   */
  scanDirectory(dir, callback) {
    if (!fs.existsSync(dir)) return
    
    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
        this.scanDirectory(filePath, callback)
      } else if (stat.isFile()) {
        try {
          const content = fs.readFileSync(filePath, 'utf-8')
          callback(filePath, content)
        } catch (error) {
          // 忽略读取错误
        }
      }
    })
  }

  /**
   * 打印检查结果
   */
  printResults() {
    console.log('📊 检查结果汇总:')
    console.log('=' .repeat(50))
    
    if (this.violations.length === 0 && this.warnings.length === 0) {
      console.log('🎉 所有检查通过！认证架构完全合规。')
    } else {
      if (this.violations.length > 0) {
        console.log(`\n❌ 发现 ${this.violations.length} 个违规问题:`)
        this.violations.forEach(violation => console.log(violation))
      }
      
      if (this.warnings.length > 0) {
        console.log(`\n⚠️ 发现 ${this.warnings.length} 个警告:`)
        this.warnings.forEach(warning => console.log(warning))
      }
    }
    
    console.log('\n' + '=' .repeat(50))
    console.log(`总计: ${this.violations.length} 个违规, ${this.warnings.length} 个警告`)
  }
}

// 运行检查
if (require.main === module) {
  const checker = new ArchitectureChecker()
  checker.runAllChecks().catch(error => {
    console.error('检查过程中发生错误:', error)
    process.exit(1)
  })
}

module.exports = ArchitectureChecker
