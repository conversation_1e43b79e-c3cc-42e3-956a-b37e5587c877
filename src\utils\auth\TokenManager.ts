/**
 * JWT Token管理工具类
 * 
 * 负责JWT Token的生成、验证、刷新等核心功能
 * 遵循PRD文档中的Token安全策略
 */

import jwt from 'jsonwebtoken'
import { JWTPayload, TokenValidationResult, AuthErrorCode } from '@/types/auth'
import { JWT_CONFIG, validateJWTConfig } from '@/config/jwt.config'

// 验证JWT配置
validateJWTConfig()

/**
 * Token管理器类
 * 提供JWT Token的完整生命周期管理
 */
export class TokenManager {
  /**
   * 生成Access Token
   * @param payload JWT载荷数据
   * @returns 生成的JWT字符串
   */
  static generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    try {
      const tokenPayload: JWTPayload = {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
      }

      return jwt.sign(tokenPayload, JWT_CONFIG.accessToken.secret, {
        algorithm: JWT_CONFIG.accessToken.algorithm,
        expiresIn: JWT_CONFIG.accessToken.expiresIn
      })
    } catch (error) {
      console.error('生成Access Token失败:', error)
      throw new Error('Token生成失败')
    }
  }

  /**
   * 生成Refresh Token
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 生成的Refresh Token字符串
   */
  static generateRefreshToken(userId: string, sessionId: string): string {
    try {
      const payload = {
        userId,
        sessionId,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000)
      }

      return jwt.sign(payload, JWT_CONFIG.refreshToken.secret, {
        algorithm: JWT_CONFIG.refreshToken.algorithm,
        expiresIn: JWT_CONFIG.refreshToken.expiresIn
      })
    } catch (error) {
      console.error('生成Refresh Token失败:', error)
      throw new Error('Refresh Token生成失败')
    }
  }

  /**
   * 验证Access Token
   * @param token JWT字符串
   * @returns Token验证结果
   */
  static verifyAccessToken(token: string): TokenValidationResult {
    try {
      if (!token) {
        return {
          isValid: false,
          error: 'Token不能为空'
        }
      }

      const payload = jwt.verify(token, JWT_CONFIG.accessToken.secret) as JWTPayload
      
      // 检查Token是否过期
      const now = Math.floor(Date.now() / 1000)
      if (payload.exp && payload.exp < now) {
        return {
          isValid: false,
          error: 'Token已过期',
          isExpired: true
        }
      }

      return {
        isValid: true,
        payload
      }
    } catch (error) {
      if (error && typeof error === 'object' && 'name' in error) {
        if (error.name === 'TokenExpiredError') {
          return {
            isValid: false,
            error: 'Token已过期',
            isExpired: true
          }
        } else if (error.name === 'JsonWebTokenError') {
          return {
            isValid: false,
            error: 'Token格式无效'
          }
        }
      }
      return {
        isValid: false,
        error: 'Token验证失败'
      }
    }
  }

  /**
   * 验证Refresh Token
   * @param token Refresh Token字符串
   * @returns 验证结果，包含用户ID和会话ID
   */
  static verifyRefreshToken(token: string): { isValid: boolean; userId?: string; sessionId?: string; error?: string } {
    try {
      if (!token) {
        return {
          isValid: false,
          error: 'Refresh Token不能为空'
        }
      }

      const payload = jwt.verify(token, JWT_CONFIG.refreshToken.secret) as any
      
      if (payload.type !== 'refresh') {
        return {
          isValid: false,
          error: 'Token类型无效'
        }
      }

      return {
        isValid: true,
        userId: payload.userId,
        sessionId: payload.sessionId
      }
    } catch (error) {
      if (error && typeof error === 'object' && 'name' in error) {
        if (error.name === 'TokenExpiredError') {
          return {
            isValid: false,
            error: 'Refresh Token已过期'
          }
        } else if (error.name === 'JsonWebTokenError') {
          return {
            isValid: false,
            error: 'Refresh Token格式无效'
          }
        }
      }
      return {
        isValid: false,
        error: 'Refresh Token验证失败'
      }
    }
  }

  /**
   * 检查Token是否即将过期
   * @param token JWT字符串
   * @param thresholdMinutes 过期阈值（分钟），默认5分钟
   * @returns 是否需要刷新
   */
  static shouldRefreshToken(token: string, thresholdMinutes: number = 5): boolean {
    try {
      const validation = this.verifyAccessToken(token)
      if (!validation.isValid || !validation.payload) {
        return false
      }

      const now = Math.floor(Date.now() / 1000)
      const threshold = thresholdMinutes * 60
      
      return validation.payload.exp - now <= threshold
    } catch (error) {
      return false
    }
  }

  /**
   * 从Token中提取用户信息
   * @param token JWT字符串
   * @returns 用户信息或null
   */
  static extractUserInfo(token: string): { userId: string; username: string; roles: string[]; permissions: string[] } | null {
    try {
      const validation = this.verifyAccessToken(token)
      if (!validation.isValid || !validation.payload) {
        return null
      }

      const { userId, username, roles, permissions } = validation.payload
      return { userId, username, roles, permissions }
    } catch (error) {
      return null
    }
  }

  /**
   * 生成会话ID
   * @returns 唯一的会话ID
   */
  static generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取Token剩余有效时间（秒）
   * @param token JWT字符串
   * @returns 剩余秒数，-1表示无效或已过期
   */
  static getTokenRemainingTime(token: string): number {
    try {
      const validation = this.verifyAccessToken(token)
      if (!validation.isValid || !validation.payload) {
        return -1
      }

      const now = Math.floor(Date.now() / 1000)
      return Math.max(0, validation.payload.exp - now)
    } catch (error) {
      return -1
    }
  }

  /**
   * 检查环境变量配置
   * @returns 配置检查结果
   */
  static checkConfiguration(): { isValid: boolean; warnings: string[] } {
    const warnings: string[] = []

    if (JWT_CONFIG.accessToken.secret === 'default-access-secret-change-in-production') {
      warnings.push('使用默认的Access Token密钥，生产环境中请设置JWT_ACCESS_SECRET环境变量')
    }

    if (JWT_CONFIG.refreshToken.secret === 'default-refresh-secret-change-in-production') {
      warnings.push('使用默认的Refresh Token密钥，生产环境中请设置JWT_REFRESH_SECRET环境变量')
    }

    if (JWT_CONFIG.accessToken.secret === JWT_CONFIG.refreshToken.secret) {
      warnings.push('Access Token和Refresh Token使用相同密钥，建议使用不同的密钥')
    }

    return {
      isValid: warnings.length === 0,
      warnings
    }
  }
}

/**
 * Token管理器单例实例
 * 提供全局访问点
 */
export const tokenManager = TokenManager

/**
 * 导出JWT配置（只读）
 */
export const getJWTConfig = () => ({
  accessTokenExpiresIn: JWT_CONFIG.accessToken.expiresIn,
  refreshTokenExpiresIn: JWT_CONFIG.refreshToken.expiresIn,
  algorithm: JWT_CONFIG.accessToken.algorithm
})
