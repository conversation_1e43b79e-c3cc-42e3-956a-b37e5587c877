'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  Statistic,
  App,
  Popconfirm,
  Descriptions,
  Checkbox,
  Switch,
  Alert
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined,
  KeyOutlined,
  TeamOutlined,
  SettingOutlined,
  CrownOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { User, UserStatus } from '@/types/auth'
import { styleHelpers } from '@/utils/styles/antdHelpers'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PermissionGuard from '@/components/auth/PermissionGuard'

const { Option } = Select
const { TextArea } = Input

function UsersManagementPage() {
  const { message } = App.useApp()

  // 状态管理
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [simpleMode, setSimpleMode] = useState(false) // 简化模式开关
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [form] = Form.useForm()
  const [passwordForm] = Form.useForm()
  const [searchText, setSearchText] = useState('')
  const [filterStatus, setFilterStatus] = useState<UserStatus | undefined>(undefined)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [isBatchStatusModalVisible, setIsBatchStatusModalVisible] = useState(false)
  const [batchStatusForm] = Form.useForm()

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 加载用户数据
  const loadUsers = async (page = 1, pageSize = 10) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/users?page=${page}&pageSize=${pageSize}&search=${searchText}&status=${filterStatus || ''}`)
      const result = await response.json()
      
      if (result.success && result.data) {
        setUsers(result.data.users)
        setPagination(prev => ({
          ...prev,
          current: page,
          pageSize,
          total: result.data.total
        }))
      } else {
        message.error(result.error || '获取用户列表失败')
      }
    } catch (error) {
      console.error('加载用户数据失败:', error)
      message.error('加载用户数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadUsers()
  }, [searchText, filterStatus])

  // 状态标签渲染
  const renderStatusTag = (status: UserStatus) => {
    const statusConfig = {
      active: { color: 'green', text: '正常' },
      inactive: { color: 'orange', text: '禁用' },
      locked: { color: 'red', text: '锁定' }
    }
    const config = statusConfig[status] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 角色标签渲染
  const renderRoleTags = (roles: User['roles']) => {
    return roles.map(role => (
      <Tag key={role.id} color="blue">
        {role.name}
      </Tag>
    ))
  }

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
      render: (text, record) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      width: 120
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      render: (text) => text || '-'
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      render: (text) => text || '-'
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 150,
      render: renderRoleTags
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: renderStatusTag
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 150,
      render: (text) => text ? new Date(text).toLocaleString() : '从未登录'
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewUser(record)}
          >
            查看
          </Button>
          <PermissionGuard permission="admin:users:update">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
            >
              编辑
            </Button>
          </PermissionGuard>
          <PermissionGuard permission="admin:users:update">
            <Button
              type="link"
              size="small"
              icon={<KeyOutlined />}
              onClick={() => handleResetPassword(record)}
            >
              重置密码
            </Button>
          </PermissionGuard>
          <PermissionGuard permission="admin:users:update">
            <Button
              type="link"
              size="small"
              onClick={() => handleForcePasswordChange(record)}
            >
              强制修改密码
            </Button>
          </PermissionGuard>
          <PermissionGuard permission="admin:users:delete">
            <Popconfirm
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDeleteUser(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          </PermissionGuard>
        </Space>
      )
    }
  ]

  // 简化模式的表格列定义
  const simpleColumns: ColumnsType<User> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 150,
      render: (text, record) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      width: 150
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 120,
      render: (roles: User['roles']) => {
        // 简化角色显示：只显示admin或employee
        const hasAdmin = roles.some(role => role.code === 'admin' || role.code === 'system_admin')
        return (
          <Tag color={hasAdmin ? 'red' : 'blue'} icon={hasAdmin ? <CrownOutlined /> : <UserOutlined />}>
            {hasAdmin ? '管理员' : '员工'}
          </Tag>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: renderStatusTag
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 150,
      render: (text) => text ? new Date(text).toLocaleDateString() : '从未登录'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
          >
            编辑
          </Button>
          {record.username !== 'admin' && (
            <PermissionGuard permission="admin:users:delete">
              <Popconfirm
                title="确定要删除这个用户吗？"
                onConfirm={() => handleDeleteUser(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </PermissionGuard>
          )}
        </Space>
      )
    }
  ]

  // 事件处理函数
  const handleViewUser = (user: User) => {
    setSelectedUser(user)
    setIsDetailModalVisible(true)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    form.setFieldsValue({
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      phone: user.phone,
      status: user.status,
      roleIds: user.roles.map(role => role.id)
    })
    setIsModalVisible(true)
  }

  const handleResetPassword = (user: User) => {
    setSelectedUser(user)
    passwordForm.resetFields()
    setIsPasswordModalVisible(true)
  }

  const handleForcePasswordChange = async (user: User) => {
    try {
      const response = await fetch(`/api/users/${user.id}/force-password-change`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ force: true })
      })
      const result = await response.json()

      if (result.success) {
        message.success(`已设置用户 ${user.fullName} 强制修改密码`)
      } else {
        message.error(result.error || '设置强制修改密码失败')
      }
    } catch (error) {
      console.error('设置强制修改密码失败:', error)
      message.error('设置强制修改密码失败')
    }
  }

  const handleGeneratePassword = async () => {
    try {
      const response = await fetch('/api/users/generate-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ length: 12 })
      })
      const result = await response.json()

      if (result.success) {
        passwordForm.setFieldsValue({
          newPassword: result.data,
          confirmPassword: result.data
        })
        message.success('已生成安全密码')
      } else {
        message.error(result.error || '生成密码失败')
      }
    } catch (error) {
      console.error('生成密码失败:', error)
      message.error('生成密码失败')
    }
  }

  const handleDeleteUser = async (userId: string) => {
    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      })
      const result = await response.json()
      
      if (result.success) {
        message.success('用户删除成功')
        loadUsers(pagination.current, pagination.pageSize)
      } else {
        message.error(result.error || '删除用户失败')
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      message.error('删除用户失败')
    }
  }

  const handleCreateUser = () => {
    setEditingUser(null)
    form.resetFields()
    setIsModalVisible(true)
  }

  // 过滤后的用户数据
  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchText || 
      user.username.toLowerCase().includes(searchText.toLowerCase()) ||
      user.fullName.toLowerCase().includes(searchText.toLowerCase()) ||
      (user.email && user.email.toLowerCase().includes(searchText.toLowerCase()))
    
    const matchesStatus = !filterStatus || user.status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  return (
    <ProtectedRoute requiredPermission="admin:users:read">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold">用户管理</h1>
            <div className="flex items-center space-x-2">
              <SettingOutlined className="text-gray-500" />
              <Switch
                checked={simpleMode}
                onChange={setSimpleMode}
                checkedChildren="简化模式"
                unCheckedChildren="完整模式"
                size="default"
              />
            </div>
          </div>
          <PermissionGuard permission="admin:users:create">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateUser}
            >
              新建用户
            </Button>
          </PermissionGuard>
        </div>

        {/* 简化模式提示 */}
        {simpleMode && (
          <Alert
            message="简化模式已启用"
            description="当前使用极简用户管理模式，专为小企业设计。只显示核心功能，隐藏复杂的高级选项。"
            type="info"
            showIcon
            closable
            className="mb-4"
          />
        )}

        {/* 统计卡片 - 仅在完整模式下显示 */}
        {!simpleMode && (
          <Row gutter={16} className="mb-6">
            <Col span={6}>
              <Card>
                <Statistic
                  title="总用户数"
                  value={pagination.total}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="正常用户"
                  value={users.filter(u => u.status === 'active').length}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="禁用用户"
                  value={users.filter(u => u.status === 'inactive').length}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="锁定用户"
                  value={users.filter(u => u.status === 'locked').length}
                  valueStyle={{ color: '#d46b08' }}
                />
              </Card>
            </Col>
          </Row>
        )}

        {/* 搜索和过滤 */}
        <Card className="mb-4">
          {simpleMode ? (
            // 简化模式：只显示基本搜索
            <Row gutter={16} align="middle">
              <Col span={12}>
                <Input
                  placeholder="搜索用户名或姓名"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
              <Col span={6}>
                <Select
                  placeholder="筛选状态"
                  value={filterStatus}
                  onChange={setFilterStatus}
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value="active">正常</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Col>
              <Col span={6}>
                <div className="text-gray-500 text-sm">
                  共 {pagination.total} 个用户
                </div>
              </Col>
            </Row>
          ) : (
            // 完整模式：显示所有搜索和批量操作功能
            <Row gutter={16} align="middle">
              <Col span={8}>
                <Input
                  placeholder="搜索用户名、姓名或邮箱"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  allowClear
                />
              </Col>
              <Col span={6}>
                <Select
                  placeholder="筛选状态"
                  value={filterStatus}
                  onChange={setFilterStatus}
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value="active">正常</Option>
                  <Option value="inactive">禁用</Option>
                  <Option value="locked">锁定</Option>
                </Select>
              </Col>
              <Col span={10}>
                <Space>
                  {selectedRowKeys.length > 0 && (
                    <>
                      <span>已选择 {selectedRowKeys.length} 个用户</span>
                      <PermissionGuard permission="admin:users:update">
                        <Button
                          type="primary"
                          size="small"
                          onClick={() => setIsBatchStatusModalVisible(true)}
                        >
                          批量状态管理
                        </Button>
                      </PermissionGuard>
                    </>
                  )}
                </Space>
              </Col>
            </Row>
          )}
        </Card>

        {/* 用户列表表格 */}
        <Card>
          <Table
            columns={simpleMode ? simpleColumns : columns}
            dataSource={filteredUsers}
            rowKey="id"
            loading={loading}
            rowSelection={simpleMode ? undefined : {
              selectedRowKeys,
              onChange: setSelectedRowKeys,
              getCheckboxProps: (record) => ({
                disabled: record.username === 'admin' // 禁止选择系统管理员
              })
            }}
            pagination={{
              ...pagination,
              showSizeChanger: !simpleMode,
              showQuickJumper: !simpleMode,
              showTotal: (total, range) => simpleMode
                ? `共 ${total} 条`
                : `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                loadUsers(page, pageSize)
                setSelectedRowKeys([]) // 切换页面时清空选择
              }
            }}
            scroll={{ x: 1200 }}
          />
        </Card>

        {/* 用户创建/编辑模态框 */}
        <Modal
          title={editingUser ? '编辑用户' : '新建用户'}
          open={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          footer={null}
          width={simpleMode ? 500 : 600}
        >
          {simpleMode && (
            <Alert
              message="简化模式"
              description="只显示基本用户信息字段，适合快速创建和编辑用户。"
              type="info"
              showIcon
              className="mb-4"
            />
          )}
          <Form
            form={form}
            layout="vertical"
            onFinish={handleModalOk}
          >
            {simpleMode ? (
              // 简化模式表单
              <>
                <Form.Item
                  name="username"
                  label="用户名"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 3, message: '用户名至少3个字符' }
                  ]}
                >
                  <Input
                    placeholder="请输入用户名"
                    disabled={!!editingUser}
                  />
                </Form.Item>

                <Form.Item
                  name="fullName"
                  label="姓名"
                  rules={[{ required: true, message: '请输入姓名' }]}
                >
                  <Input placeholder="请输入姓名" />
                </Form.Item>

                <Form.Item
                  name="email"
                  label="邮箱（可选）"
                  rules={[{ type: 'email', message: '请输入有效的邮箱地址' }]}
                >
                  <Input placeholder="请输入邮箱" />
                </Form.Item>

                {!editingUser && (
                  <Form.Item
                    name="password"
                    label="初始密码"
                    rules={[
                      { required: true, message: '请输入初始密码' },
                      { min: 6, message: '密码至少6个字符' }
                    ]}
                  >
                    <Input.Password placeholder="请输入初始密码" />
                  </Form.Item>
                )}

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="roleIds"
                      label="角色"
                      rules={[{ required: true, message: '请选择角色' }]}
                    >
                      <Select placeholder="请选择角色">
                        <Option value="admin">管理员</Option>
                        <Option value="user">员工</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="status"
                      label="状态"
                      rules={[{ required: true, message: '请选择状态' }]}
                    >
                      <Select placeholder="请选择状态">
                        <Option value="active">正常</Option>
                        <Option value="inactive">禁用</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            ) : (
              // 完整模式表单
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="username"
                      label="用户名"
                      rules={[
                        { required: true, message: '请输入用户名' },
                        { min: 3, message: '用户名至少3个字符' },
                        { max: 50, message: '用户名最多50个字符' }
                      ]}
                    >
                      <Input
                        placeholder="请输入用户名"
                        disabled={!!editingUser}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="fullName"
                      label="姓名"
                      rules={[
                        { required: true, message: '请输入姓名' },
                        { max: 100, message: '姓名最多100个字符' }
                      ]}
                    >
                      <Input placeholder="请输入姓名" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="email"
                      label="邮箱"
                      rules={[
                        { type: 'email', message: '请输入有效的邮箱地址' }
                      ]}
                    >
                      <Input placeholder="请输入邮箱" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="phone"
                      label="电话"
                      rules={[
                        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                      ]}
                    >
                      <Input placeholder="请输入电话" />
                    </Form.Item>
                  </Col>
                </Row>

                {!editingUser && (
                  <Form.Item
                    name="password"
                    label="初始密码"
                    rules={[
                      { required: true, message: '请输入初始密码' },
                      { min: 8, message: '密码至少8个字符' },
                      {
                        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                        message: '密码必须包含大小写字母、数字和特殊字符'
                      }
                    ]}
                  >
                    <Input.Password placeholder="请输入初始密码" />
                  </Form.Item>
                )}

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="status"
                      label="状态"
                      rules={[{ required: true, message: '请选择状态' }]}
                    >
                      <Select placeholder="请选择状态">
                        <Option value="active">正常</Option>
                        <Option value="inactive">禁用</Option>
                        <Option value="locked">锁定</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="roleIds"
                      label="角色"
                      rules={[{ required: true, message: '请选择至少一个角色' }]}
                    >
                      <Select
                        mode="multiple"
                        placeholder="请选择角色"
                        allowClear
                      >
                        <Option value="admin">系统管理员</Option>
                        <Option value="manager">管理员</Option>
                        <Option value="user">普通用户</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            <Form.Item className="mb-0 text-right">
              <Space>
                <Button onClick={() => setIsModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editingUser ? '更新' : '创建'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 用户详情模态框 */}
        <Modal
          title="用户详情"
          open={isDetailModalVisible}
          onCancel={() => setIsDetailModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
              关闭
            </Button>
          ]}
          width={600}
        >
          {selectedUser && (
            <Descriptions column={2} bordered>
              <Descriptions.Item label="用户名">
                {selectedUser.username}
              </Descriptions.Item>
              <Descriptions.Item label="姓名">
                {selectedUser.fullName}
              </Descriptions.Item>
              <Descriptions.Item label="邮箱">
                {selectedUser.email || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="电话">
                {selectedUser.phone || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {renderStatusTag(selectedUser.status)}
              </Descriptions.Item>
              <Descriptions.Item label="角色">
                {renderRoleTags(selectedUser.roles)}
              </Descriptions.Item>
              <Descriptions.Item label="最后登录">
                {selectedUser.lastLoginAt ? new Date(selectedUser.lastLoginAt).toLocaleString() : '从未登录'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedUser.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间" span={2}>
                {new Date(selectedUser.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          )}
        </Modal>

        {/* 密码重置模态框 */}
        <Modal
          title="重置密码"
          open={isPasswordModalVisible}
          onCancel={() => setIsPasswordModalVisible(false)}
          footer={null}
          width={400}
        >
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handlePasswordReset}
          >
            <Form.Item
              name="newPassword"
              label={
                <Space>
                  新密码
                  <Button
                    type="link"
                    size="small"
                    onClick={handleGeneratePassword}
                  >
                    生成安全密码
                  </Button>
                </Space>
              }
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 8, message: '密码至少8个字符' },
                {
                  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                  message: '密码必须包含大小写字母、数字和特殊字符'
                }
              ]}
            >
              <Input.Password placeholder="请输入新密码" />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  }
                })
              ]}
            >
              <Input.Password placeholder="请再次输入密码" />
            </Form.Item>

            <Form.Item className="mb-0 text-right">
              <Space>
                <Button onClick={() => setIsPasswordModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  重置密码
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 批量状态管理模态框 */}
        <Modal
          title="批量状态管理"
          open={isBatchStatusModalVisible}
          onCancel={() => setIsBatchStatusModalVisible(false)}
          footer={null}
          width={400}
        >
          <Form
            form={batchStatusForm}
            layout="vertical"
            onFinish={handleBatchStatusUpdate}
          >
            <div className="mb-4">
              <p>将对以下 {selectedRowKeys.length} 个用户进行状态更新：</p>
              <div className="max-h-32 overflow-y-auto bg-gray-50 p-2 rounded">
                {selectedRowKeys.map(userId => {
                  const user = users.find(u => u.id === userId)
                  return user ? (
                    <div key={userId} className="text-sm">
                      {user.fullName} ({user.username})
                    </div>
                  ) : null
                })}
              </div>
            </div>

            <Form.Item
              name="status"
              label="新状态"
              rules={[{ required: true, message: '请选择新状态' }]}
            >
              <Select placeholder="请选择新状态">
                <Option value="active">正常</Option>
                <Option value="inactive">禁用</Option>
                <Option value="locked">锁定</Option>
              </Select>
            </Form.Item>

            <Form.Item className="mb-0 text-right">
              <Space>
                <Button onClick={() => setIsBatchStatusModalVisible(false)}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  确认更新
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </ProtectedRoute>
  )

  // 表单提交处理
  async function handleModalOk() {
    try {
      const values = await form.validateFields()
      setLoading(true)

      if (editingUser) {
        // 更新用户
        const response = await fetch(`/api/users/${editingUser.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(values)
        })
        const result = await response.json()

        if (result.success) {
          message.success('用户更新成功')
          setIsModalVisible(false)
          loadUsers(pagination.current, pagination.pageSize)
        } else {
          message.error(result.error || '更新用户失败')
        }
      } else {
        // 创建用户
        const response = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(values)
        })
        const result = await response.json()

        if (result.success) {
          message.success('用户创建成功')
          setIsModalVisible(false)
          loadUsers(pagination.current, pagination.pageSize)
        } else {
          message.error(result.error || '创建用户失败')
        }
      }
    } catch (error) {
      console.error('操作失败:', error)
      message.error('操作失败')
    } finally {
      setLoading(false)
    }
  }

  // 密码重置处理
  async function handlePasswordReset() {
    if (!selectedUser) return

    try {
      const values = await passwordForm.validateFields()
      setLoading(true)

      const response = await fetch(`/api/users/${selectedUser.id}/password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newPassword: values.newPassword })
      })
      const result = await response.json()

      if (result.success) {
        message.success('密码重置成功')
        setIsPasswordModalVisible(false)
      } else {
        message.error(result.error || '密码重置失败')
      }
    } catch (error) {
      console.error('密码重置失败:', error)
      message.error('密码重置失败')
    } finally {
      setLoading(false)
    }
  }

  // 批量状态更新处理
  async function handleBatchStatusUpdate() {
    try {
      const values = await batchStatusForm.validateFields()
      setLoading(true)

      const response = await fetch('/api/users/batch-status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedRowKeys,
          status: values.status
        })
      })
      const result = await response.json()

      if (result.success) {
        message.success(result.message || '批量状态更新成功')
        setIsBatchStatusModalVisible(false)
        setSelectedRowKeys([])
        loadUsers(pagination.current, pagination.pageSize)
      } else {
        message.error(result.error || '批量状态更新失败')
      }
    } catch (error) {
      console.error('批量状态更新失败:', error)
      message.error('批量状态更新失败')
    } finally {
      setLoading(false)
    }
  }
}

export default UsersManagementPage
