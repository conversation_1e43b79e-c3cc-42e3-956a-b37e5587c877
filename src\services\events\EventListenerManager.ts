/**
 * 事件监听器管理器
 * P5-3: 事件通信错误处理 - 统一的事件监听器生命周期管理
 */

// EventErrorHandler模块不存在，创建简单替代实现
import {
  ListenerRegistrationConfig,
  EventPublishConfig,
  ListenerHealth,
  EventCommunicationMetrics,
  EventCommunicationDebugInfo,
  EventErrorContext
} from './types'

/**
 * 简单的错误处理器实现（替代不存在的EventErrorHandler）
 */
class SimpleEventErrorHandler {
  private errors: Array<{ error: Error; context: any; timestamp: number }> = []

  async handleError(error: Error, context: any): Promise<void> {
    console.error('[EventListenerManager] 错误:', error.message, context)
    this.errors.push({ error, context, timestamp: Date.now() })

    // 保持最近100个错误
    if (this.errors.length > 100) {
      this.errors = this.errors.slice(-100)
    }
  }

  getErrorStatistics() {
    return {
      errorsByType: this.errors.reduce((acc, { error }) => {
        const type = error.constructor.name
        acc[type] = (acc[type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }
  }

  getAlerts() {
    return []
  }

  destroy() {
    this.errors = []
  }
}

/**
 * 监听器实例接口
 */
interface ListenerInstance {
  id: string
  config: ListenerRegistrationConfig
  registeredAt: number
  lastActivity: number
  eventCount: number
  errorCount: number
  averageResponseTime: number
  isActive: boolean
  cleanup?: () => void
}

/**
 * 事件发布记录
 */
interface EventPublishRecord {
  id: string
  type: string
  timestamp: number
  success: boolean
  duration: number
  targetListeners: string[]
  error?: Error
}

/**
 * 事件监听器管理器
 */
export class EventListenerManager {
  private listeners: Map<string, ListenerInstance> = new Map()
  private eventHistory: EventPublishRecord[] = []
  private errorHandler: SimpleEventErrorHandler
  private metrics: EventCommunicationMetrics
  private isDestroyed = false

  constructor(errorHandler?: SimpleEventErrorHandler) {
    this.errorHandler = errorHandler || new SimpleEventErrorHandler()
    
    this.metrics = {
      totalEvents: 0,
      successfulEvents: 0,
      failedEvents: 0,
      errorRate: 0,
      averageResponseTime: 0,
      activeListeners: 0,
      deadListeners: 0,
      memoryUsage: 0,
      networkUsage: 0
    }

    // 启动监控
    this.startMonitoring()
  }

  /**
   * 注册事件监听器
   */
  registerListener(config: ListenerRegistrationConfig): string {
    if (this.isDestroyed) {
      throw new Error('EventListenerManager has been destroyed')
    }

    // 验证配置
    this.validateListenerConfig(config)

    // 检查是否已存在同ID的监听器
    if (this.listeners.has(config.id)) {
      console.warn(`[EventListenerManager] 监听器 ${config.id} 已存在，将被替换`)
      this.unregisterListener(config.id)
    }

    // 创建监听器实例
    const instance: ListenerInstance = {
      id: config.id,
      config,
      registeredAt: Date.now(),
      lastActivity: Date.now(),
      eventCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      isActive: true
    }

    // 包装回调函数以添加错误处理和监控
    const wrappedCallback = this.wrapListenerCallback(instance)
    instance.config.callback = wrappedCallback

    // 注册监听器
    this.listeners.set(config.id, instance)

    // 更新指标
    this.updateMetrics()

    console.log(`[EventListenerManager] 注册监听器: ${config.id}`)
    return config.id
  }

  /**
   * 注销事件监听器
   */
  unregisterListener(listenerId: string): boolean {
    const instance = this.listeners.get(listenerId)
    if (!instance) {
      return false
    }

    // 执行清理函数
    if (instance.cleanup) {
      try {
        instance.cleanup()
      } catch (error) {
        console.error(`[EventListenerManager] 清理监听器 ${listenerId} 时出错:`, error)
      }
    }

    // 标记为非活跃
    instance.isActive = false

    // 从映射中移除
    this.listeners.delete(listenerId)

    // 更新指标
    this.updateMetrics()

    console.log(`[EventListenerManager] 注销监听器: ${listenerId}`)
    return true
  }

  /**
   * 发布事件
   */
  async publishEvent(config: EventPublishConfig): Promise<boolean> {
    if (this.isDestroyed) {
      throw new Error('EventListenerManager has been destroyed')
    }

    const startTime = Date.now()
    const eventId = this.generateEventId()
    let success = true
    let error: Error | undefined

    try {
      // 获取目标监听器
      const targetListeners = this.getTargetListeners(config)
      
      if (targetListeners.length === 0) {
        console.warn(`[EventListenerManager] 没有找到事件 ${config.type} 的监听器`)
        return false
      }

      // 发布事件到所有目标监听器
      const publishPromises = targetListeners.map(listener => 
        this.publishToListener(listener, config)
      )

      // 等待所有发布完成
      const results = await Promise.allSettled(publishPromises)
      
      // 检查是否有失败的发布
      const failures = results.filter(r => r.status === 'rejected')
      if (failures.length > 0) {
        success = false
        error = new Error(`${failures.length} 个监听器发布失败`)
      }

      // 记录事件发布
      this.recordEventPublish({
        id: eventId,
        type: config.type,
        timestamp: startTime,
        success,
        duration: Date.now() - startTime,
        targetListeners: targetListeners.map(l => l.id),
        error
      })

      return success

    } catch (publishError) {
      error = publishError as Error
      success = false

      // 记录发布错误
      this.recordEventPublish({
        id: eventId,
        type: config.type,
        timestamp: startTime,
        success: false,
        duration: Date.now() - startTime,
        targetListeners: [],
        error
      })

      // 使用错误处理器处理错误
      await this.errorHandler.handleError(error, {
        eventType: config.type,
        component: 'EventListenerManager'
      })

      return false
    }
  }

  /**
   * 获取目标监听器
   */
  private getTargetListeners(config: EventPublishConfig): ListenerInstance[] {
    const allListeners = Array.from(this.listeners.values())
    
    // 如果指定了目标监听器
    if (config.targetListeners && config.targetListeners.length > 0) {
      return allListeners.filter(listener => 
        config.targetListeners!.includes(listener.id) &&
        listener.isActive &&
        listener.config.enabled
      )
    }

    // 否则返回所有监听该事件类型的监听器
    return allListeners.filter(listener =>
      listener.config.eventTypes.includes(config.type) &&
      listener.isActive &&
      listener.config.enabled
    )
  }

  /**
   * 发布事件到单个监听器
   */
  private async publishToListener(
    listener: ListenerInstance,
    config: EventPublishConfig
  ): Promise<void> {
    const startTime = Date.now()

    try {
      // 检查超时设置
      const timeout = config.options?.timeout || listener.config.timeout || 5000

      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Listener timeout')), timeout)
      })

      // 执行监听器回调
      const callbackPromise = Promise.resolve(
        listener.config.callback(config.data)
      )

      // 等待回调完成或超时
      await Promise.race([callbackPromise, timeoutPromise])

      // 更新监听器统计
      this.updateListenerStats(listener, true, Date.now() - startTime)

    } catch (error) {
      // 更新监听器统计
      this.updateListenerStats(listener, false, Date.now() - startTime)

      // 使用错误处理器处理错误
      await this.errorHandler.handleError(error as Error, {
        eventType: config.type,
        listenerId: listener.id,
        component: 'EventListenerManager'
      })

      throw error
    }
  }

  /**
   * 包装监听器回调函数
   */
  private wrapListenerCallback(instance: ListenerInstance) {
    const originalCallback = instance.config.callback

    return async (data: any) => {
      const startTime = Date.now()

      try {
        // 更新最后活动时间
        instance.lastActivity = Date.now()

        // 执行原始回调
        const result = await originalCallback(data)

        // 更新统计
        this.updateListenerStats(instance, true, Date.now() - startTime)

        return result

      } catch (error) {
        // 更新统计
        this.updateListenerStats(instance, false, Date.now() - startTime)

        // 创建错误上下文
        const context: EventErrorContext = {
          listenerId: instance.id,
          component: 'EventListener',
          metadata: {
            eventTypes: instance.config.eventTypes,
            priority: instance.config.priority
          }
        }

        // 使用错误处理器处理错误
        await this.errorHandler.handleError(error as Error, context)

        // 重新抛出错误
        throw error
      }
    }
  }

  /**
   * 更新监听器统计
   */
  private updateListenerStats(
    listener: ListenerInstance,
    success: boolean,
    responseTime: number
  ): void {
    listener.eventCount++
    listener.lastActivity = Date.now()

    if (success) {
      // 更新平均响应时间
      listener.averageResponseTime = 
        (listener.averageResponseTime * (listener.eventCount - 1) + responseTime) / listener.eventCount
    } else {
      listener.errorCount++
    }
  }

  /**
   * 验证监听器配置
   */
  private validateListenerConfig(config: ListenerRegistrationConfig): void {
    if (!config.id) {
      throw new Error('监听器ID不能为空')
    }

    if (!config.eventTypes || config.eventTypes.length === 0) {
      throw new Error('监听器必须指定至少一个事件类型')
    }

    if (typeof config.callback !== 'function') {
      throw new Error('监听器回调必须是函数')
    }

    if (config.priority < 0 || config.priority > 10) {
      throw new Error('监听器优先级必须在0-10之间')
    }

    if (config.timeout < 0) {
      throw new Error('监听器超时时间不能为负数')
    }
  }

  /**
   * 记录事件发布
   */
  private recordEventPublish(record: EventPublishRecord): void {
    this.eventHistory.push(record)

    // 保持历史记录在合理范围内
    if (this.eventHistory.length > 1000) {
      this.eventHistory = this.eventHistory.slice(-500)
    }

    // 更新指标
    this.updateMetrics()
  }

  /**
   * 更新指标
   */
  private updateMetrics(): void {
    const now = Date.now()
    const recentEvents = this.eventHistory.filter(
      e => now - e.timestamp < 300000 // 最近5分钟
    )

    this.metrics.totalEvents = recentEvents.length
    this.metrics.successfulEvents = recentEvents.filter(e => e.success).length
    this.metrics.failedEvents = recentEvents.filter(e => !e.success).length
    this.metrics.errorRate = this.metrics.totalEvents > 0 ? 
      this.metrics.failedEvents / this.metrics.totalEvents : 0

    this.metrics.averageResponseTime = recentEvents.length > 0 ?
      recentEvents.reduce((sum, e) => sum + e.duration, 0) / recentEvents.length : 0

    this.metrics.activeListeners = Array.from(this.listeners.values())
      .filter(l => l.isActive).length

    this.metrics.deadListeners = Array.from(this.listeners.values())
      .filter(l => !l.isActive || (now - l.lastActivity > 300000)).length

    // 估算内存使用
    this.metrics.memoryUsage = this.estimateMemoryUsage()
  }

  /**
   * 估算内存使用
   */
  private estimateMemoryUsage(): number {
    const listenerCount = this.listeners.size
    const historyCount = this.eventHistory.length
    
    // 粗略估算：每个监听器约1KB，每个历史记录约0.5KB
    const estimatedBytes = listenerCount * 1024 + historyCount * 512
    
    // 返回MB
    return estimatedBytes / (1024 * 1024)
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 启动监控
   */
  private startMonitoring(): void {
    const monitoringInterval = setInterval(() => {
      if (this.isDestroyed) {
        clearInterval(monitoringInterval)
        return
      }

      this.performHealthCheck()
      this.cleanupInactiveListeners()
      this.updateMetrics()
    }, 30000) // 每30秒检查一次
  }

  /**
   * 执行健康检查
   */
  private performHealthCheck(): void {
    const now = Date.now()
    const inactiveThreshold = 300000 // 5分钟

    this.listeners.forEach((listener, id) => {
      const timeSinceLastActivity = now - listener.lastActivity

      // 标记长时间无活动的监听器
      if (timeSinceLastActivity > inactiveThreshold && listener.isActive) {
        console.warn(`[EventListenerManager] 监听器 ${id} 长时间无活动 (${Math.round(timeSinceLastActivity / 1000)}秒)`)
        
        // 可以选择自动注销或标记为非活跃
        // listener.isActive = false
      }
    })
  }

  /**
   * 清理非活跃监听器
   */
  private cleanupInactiveListeners(): void {
    const now = Date.now()
    const cleanupThreshold = 3600000 // 1小时

    const toRemove: string[] = []

    this.listeners.forEach((listener, id) => {
      if (!listener.isActive || (now - listener.lastActivity > cleanupThreshold)) {
        toRemove.push(id)
      }
    })

    toRemove.forEach(id => {
      console.log(`[EventListenerManager] 清理非活跃监听器: ${id}`)
      this.unregisterListener(id)
    })
  }

  /**
   * 获取监听器健康状态
   */
  getListenerHealthStates(): ListenerHealth[] {
    return Array.from(this.listeners.values()).map(listener => {
      const totalEvents = listener.eventCount
      const successRate = totalEvents > 0 ? 
        (totalEvents - listener.errorCount) / totalEvents : 1

      const healthScore = Math.round(
        successRate * 60 + // 成功率权重60%
        Math.max(0, 30 - (listener.averageResponseTime / 100) * 30) + // 响应时间权重30%
        (listener.isActive ? 10 : 0) // 活跃状态权重10%
      )

      let status: 'healthy' | 'warning' | 'error' | 'dead'
      if (!listener.isActive) {
        status = 'dead'
      } else if (healthScore > 80) {
        status = 'healthy'
      } else if (healthScore > 60) {
        status = 'warning'
      } else {
        status = 'error'
      }

      return {
        listenerId: listener.id,
        status,
        lastActivity: listener.lastActivity,
        errorCount: listener.errorCount,
        successCount: listener.eventCount - listener.errorCount,
        averageResponseTime: listener.averageResponseTime,
        memoryUsage: 0, // 暂时设为0，实际实现中可以计算
        healthScore
      }
    })
  }

  /**
   * 获取通信指标
   */
  getMetrics(): EventCommunicationMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取调试信息
   */
  getDebugInfo(): EventCommunicationDebugInfo {
    const healthStates = this.getListenerHealthStates()
    const recentEvents = this.eventHistory.slice(-20)

    return {
      listeners: Array.from(this.listeners.values()).map(listener => ({
        id: listener.id,
        eventTypes: listener.config.eventTypes,
        status: listener.isActive ? 'active' : 'inactive',
        health: healthStates.find(h => h.listenerId === listener.id)!
      })),
      recentEvents: recentEvents.map(event => ({
        type: event.type,
        timestamp: event.timestamp,
        success: event.success,
        duration: event.duration
      })),
      errorStats: this.errorHandler.getErrorStatistics().errorsByType,
      metrics: this.metrics,
      alerts: this.errorHandler.getAlerts()
    }
  }

  /**
   * 获取监听器列表
   */
  getListeners(): Array<{
    id: string
    eventTypes: string[]
    isActive: boolean
    registeredAt: number
    eventCount: number
    errorCount: number
  }> {
    return Array.from(this.listeners.values()).map(listener => ({
      id: listener.id,
      eventTypes: listener.config.eventTypes,
      isActive: listener.isActive,
      registeredAt: listener.registeredAt,
      eventCount: listener.eventCount,
      errorCount: listener.errorCount
    }))
  }

  /**
   * 重置监听器统计
   */
  resetListenerStats(listenerId: string): boolean {
    const listener = this.listeners.get(listenerId)
    if (!listener) return false

    listener.eventCount = 0
    listener.errorCount = 0
    listener.averageResponseTime = 0
    listener.lastActivity = Date.now()

    return true
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.isDestroyed) return

    // 注销所有监听器
    const listenerIds = Array.from(this.listeners.keys())
    listenerIds.forEach(id => this.unregisterListener(id))

    // 清理状态
    this.listeners.clear()
    this.eventHistory = []

    // 销毁错误处理器
    this.errorHandler.destroy()

    this.isDestroyed = true
    console.log('[EventListenerManager] 事件监听器管理器已销毁')
  }
}

export default EventListenerManager
