# ERP管理系统前端

这是一个基于Next.js + TypeScript + Ant Design构建的现代化企业资源规划(ERP)系统前端框架。

## 技术栈

- **前端框架**: Next.js 14 (App Router)
- **开发语言**: TypeScript
- **UI组件库**: Ant Design 5.x
- **样式方案**: Tailwind CSS + Ant Design
- **状态管理**: Zustand
- **构建工具**: Next.js内置构建系统

## 核心功能模块

### 1. 仪表板 (Dashboard)
- 业务数据概览
- 快速操作入口
- 实时统计信息

### 2. 销售管理 (Sales Management)
- 销售订单管理
- 客户信息管理
- 销售业绩统计

### 3. 采购管理 (Procurement Management)
- 采购订单管理
- 供应商信息管理
- 采购成本分析

### 4. 仓库管理 (Warehouse Management)
- 库存管理
- 出入库记录
- 库存预警

### 5. 生产管理 (Production Management)
- 生产计划管理
- 生产订单跟踪
- 设备利用率统计

### 6. 财务管理 (Finance Management)
- 财务收支记录
- 成本核算
- 财务报表

### 7. 用户认证和权限管理 (Authentication & Authorization)
- 用户登录/登出
- 基础权限控制（admin/employee）
- 用户管理（支持简化模式）
- JWT Token管理
- 会话安全控制

## 🎯 **简化模式特性**

系统支持简化模式，专为小于20人的企业设计：

- **二级权限**: 简化为管理员(admin)和员工(employee)两种角色
- **简化界面**: 用户管理界面支持简化/完整模式切换
- **快速部署**: 极简配置，2-3天即可完成部署
- **低维护成本**: 减少复杂功能，专注核心业务需求
- **向后兼容**: 与完整RBAC权限系统完全兼容

## 项目结构

```
src/
├── app/                    # Next.js App Router页面
│   ├── dashboard/         # 仪表板页面
│   ├── sales/            # 销售管理页面
│   ├── procurement/      # 采购管理页面
│   ├── warehouse/        # 仓库管理页面
│   ├── production/       # 生产管理页面
│   ├── finance/          # 财务管理页面
│   ├── layout.tsx        # 根布局
│   ├── page.tsx          # 首页
│   └── globals.css       # 全局样式
├── components/            # 可复用组件
│   ├── layout/           # 布局组件
│   └── providers/        # 上下文提供者
├── store/                # 状态管理
│   ├── modules/          # 模块化store
│   └── useAppStore.ts    # 全局store
└── types/                # TypeScript类型定义
```

## 开发指南

### 环境要求

- Node.js 18.0+
- npm 或 yarn 或 pnpm

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm run start
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

## 设计特性

### 响应式设计
- 支持桌面端和移动端
- 使用Ant Design的栅格系统
- 自适应布局组件

### 模块化架构
- 每个业务模块独立开发
- 可插拔的组件设计
- 清晰的代码组织结构

### 类型安全
- 完整的TypeScript类型定义
- 严格的类型检查
- 良好的开发体验

### 状态管理
- 使用Zustand进行状态管理
- 模块化的store设计
- 支持开发工具调试

## 后端集成准备

项目已为后端API集成做好准备：

1. **API接口层**: 可在`src/api/`目录下添加API调用逻辑
2. **数据类型**: 在`src/types/`中定义了完整的数据模型
3. **状态管理**: Store中预留了异步数据处理的位置
4. **错误处理**: 可集成统一的错误处理机制

### 推荐的后端技术栈
- Java Spring Boot
- .NET Core
- Node.js Express
- Python Django/FastAPI

## 部署

### Vercel部署
```bash
npm run build
# 部署到Vercel
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: [<EMAIL>]
- 项目地址: [GitHub Repository URL]
