# ERP系统用户认证和权限管理系统PRD - 极简版（小企业适用）

**文档版本**: v2.0
**创建日期**: 2025-08-01
**产品经理**: AI Assistant
**技术负责人**: 开发团队
**项目代号**: ERP-Auth-System-Simple
**开发周期**: 2-3天
**适用规模**: 小于20人的企业

---

## 📋 **1. 项目概述**

### 1.1 项目目标

为小于20人的企业提供简单、实用的用户认证和基础权限管理功能，确保系统安全的同时保持极低的开发和维护成本。

### 1.2 核心价值

- **简单易用**: 最小化学习成本，快速上手
- **快速交付**: 2-3天即可完成开发部署
- **低维护成本**: 极简架构，易于维护
- **基础安全**: 满足小企业基本安全需求

### 1.3 功能范围

**包含功能**:
- 用户登录/登出
- 基础角色管理（管理员/员工）
- 简单权限控制
- 基础会话管理

**不包含功能**:
- 复杂的审计日志系统
- 安全策略配置界面
- 异常检测机制
- 安全报告和仪表板
- 性能优化（小规模无需求）
- 复杂的权限树结构

---

## 🎯 **2. 功能需求详述**

### 2.1 用户认证系统

#### 2.1.1 基础认证功能

**用户故事**:
> 作为企业员工，我希望能够安全地登录系统，并根据我的角色访问相应的功能模块。

**核心功能**:
- 用户登录/登出
- 密码验证
- 会话管理
- 基础安全控制

#### 2.1.2 数据库结构

```sql
-- 用户表
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'employee') DEFAULT 'employee',
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 会话表（可选，可使用JWT替代）
CREATE TABLE user_sessions (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  token VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 2.1.3 认证服务实现

```typescript
// src/services/auth/SimpleAuthService.ts
export class SimpleAuthService {
  // 用户登录
  static async login(username: string, password: string): Promise<LoginResult> {
    const user = await dataAccessManager.users.findByUsername(username)

    if (!user || user.status !== 'active') {
      throw new Error('用户不存在或已被禁用')
    }

    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      throw new Error('用户名或密码错误')
    }

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.id, username: user.username, role: user.role },
      process.env.JWT_SECRET!,
      { expiresIn: '8h' }
    )

    return {
      user: {
        id: user.id,
        username: user.username,
        role: user.role
      },
      token
    }
  }

  // 验证token
  static async verifyToken(token: string): Promise<User | null> {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
      const user = await dataAccessManager.users.findById(decoded.userId)

      if (!user || user.status !== 'active') {
        return null
      }

      return {
        id: user.id,
        username: user.username,
        role: user.role
      }
    } catch (error) {
      return null
    }
  }

  // 权限检查
  static hasPermission(userRole: string, requiredRole: 'admin' | 'employee'): boolean {
    if (requiredRole === 'admin') {
      return userRole === 'admin'
    }
    return ['admin', 'employee'].includes(userRole)
  }
}
```

### 2.2 权限管理系统

#### 2.2.1 角色定义

**角色类型**:
```typescript
type UserRole = 'admin' | 'employee'

interface RolePermissions {
  admin: {
    description: '系统管理员'
    permissions: [
      '用户管理',
      '系统配置',
      '数据查看',
      '数据编辑',
      '数据删除'
    ]
  }
  employee: {
    description: '普通员工'
    permissions: [
      '数据查看',
      '数据编辑'
    ]
  }
}
```

#### 2.2.2 权限控制组件

```typescript
// src/components/auth/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode
  requireAdmin?: boolean
  fallback?: React.ReactNode
}

export function ProtectedRoute({
  children,
  requireAdmin = false,
  fallback = <div>无权限访问</div>
}: ProtectedRouteProps) {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return <div>加载中...</div>
  }

  if (!user) {
    return <LoginPage />
  }

  if (requireAdmin && user.role !== 'admin') {
    return fallback
  }

  return <>{children}</>
}

// 使用示例
export default function UserManagePage() {
  return (
    <ProtectedRoute requireAdmin>
      <div>
        <h1>用户管理</h1>
        {/* 用户管理内容 */}
      </div>
    </ProtectedRoute>
  )
}
```

#### 2.2.3 基础安全配置

```typescript
// src/config/security.ts
export const SECURITY_CONFIG = {
  // 密码策略（硬编码，适合小企业）
  password: {
    minLength: 6,
    requireNumbers: false,
    requireSpecialChars: false
  },

  // 会话配置
  session: {
    duration: 8 * 60 * 60, // 8小时（秒）
    maxLoginAttempts: 5,
    lockoutDuration: 30 * 60 // 30分钟（秒）
  }
}
```

### 2.3 用户管理功能

#### 2.3.1 用户管理界面

```typescript
// src/app/admin/users/page.tsx
export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      const userList = await userService.getAllUsers()
      setUsers(userList)
    } catch (error) {
      console.error('加载用户列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute requireAdmin>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">用户管理</h1>
          <Button type="primary" onClick={() => setShowAddModal(true)}>
            添加用户
          </Button>
        </div>

        <Table
          loading={loading}
          dataSource={users}
          columns={[
            {
              title: '用户名',
              dataIndex: 'username',
              key: 'username'
            },
            {
              title: '角色',
              dataIndex: 'role',
              key: 'role',
              render: (role: string) => (
                <Tag color={role === 'admin' ? 'red' : 'blue'}>
                  {role === 'admin' ? '管理员' : '员工'}
                </Tag>
              )
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              render: (status: string) => (
                <Tag color={status === 'active' ? 'green' : 'red'}>
                  {status === 'active' ? '正常' : '禁用'}
                </Tag>
              )
            },
            {
              title: '创建时间',
              dataIndex: 'createdAt',
              key: 'createdAt',
              render: (date: string) => new Date(date).toLocaleDateString()
            },
            {
              title: '操作',
              key: 'actions',
              render: (_, record) => (
                <Space>
                  <Button size="small" onClick={() => editUser(record)}>
                    编辑
                  </Button>
                  <Button
                    size="small"
                    danger
                    onClick={() => toggleUserStatus(record)}
                  >
                    {record.status === 'active' ? '禁用' : '启用'}
                  </Button>
                </Space>
              )
            }
          ]}
        />
      </div>
    </ProtectedRoute>
  )
}
```

### 2.4 登录页面实现

#### 2.4.1 登录界面

```typescript
// src/app/login/page.tsx
export default function LoginPage() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { login } = useAuth()

  const handleLogin = async (values: { username: string; password: string }) => {
    setLoading(true)
    try {
      await login(values.username, values.password)
      message.success('登录成功')
      router.push('/dashboard')
    } catch (error) {
      message.error(error.message || '登录失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            登录系统
          </h2>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          layout="vertical"
          size="large"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="w-full"
            >
              登录
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  )
}
```

### 2.5 认证状态管理

#### 2.5.1 Auth Context实现

```typescript
// src/contexts/AuthContext.tsx
interface AuthContextType {
  user: User | null
  login: (username: string, password: string) => Promise<void>
  logout: () => void
  isLoading: boolean
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查本地存储的token
    const token = localStorage.getItem('auth_token')
    if (token) {
      verifyAndSetUser(token)
    } else {
      setIsLoading(false)
    }
  }, [])

  const verifyAndSetUser = async (token: string) => {
    try {
      const user = await SimpleAuthService.verifyToken(token)
      if (user) {
        setUser(user)
      } else {
        localStorage.removeItem('auth_token')
      }
    } catch (error) {
      localStorage.removeItem('auth_token')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (username: string, password: string) => {
    const result = await SimpleAuthService.login(username, password)
    localStorage.setItem('auth_token', result.token)
    setUser(result.user)
  }

  const logout = () => {
    localStorage.removeItem('auth_token')
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

---

## 📅 **3. 开发计划**

### 3.1 第一天：核心认证功能

**开发任务**:
- [ ] 数据库表结构创建
- [ ] 用户认证服务实现
- [ ] JWT token生成和验证
- [ ] 登录页面开发

**验收标准**:
- 用户可以正常登录/登出
- JWT token正确生成和验证
- 登录状态正确维护

### 3.2 第二天：权限控制和用户管理

**开发任务**:
- [ ] 权限控制组件开发
- [ ] 用户管理界面实现
- [ ] 角色权限验证
- [ ] 基础安全配置

**验收标准**:
- 管理员可以管理用户
- 权限控制正常工作
- 角色区分功能正常

### 3.3 第三天：完善和测试

**开发任务**:
- [ ] 错误处理完善
- [ ] 用户体验优化
- [ ] 基础测试用例
- [ ] 部署配置

**验收标准**:
- 系统稳定运行
- 错误处理完善
- 用户体验良好

---

## ✅ **4. 验收标准**

### 4.1 功能测试

**测试用例1: 用户认证**
```
测试步骤:
1. 使用正确的用户名密码登录
2. 使用错误的用户名密码登录
3. 验证登录状态维护
4. 测试登出功能

预期结果:
- 正确凭据可以成功登录
- 错误凭据登录失败并提示
- 登录状态正确维护
- 登出功能正常

验收标准:
✅ 登录功能正常工作
✅ 错误处理完善
✅ 会话管理正确
```

**测试用例2: 权限控制**
```
测试步骤:
1. 管理员登录访问用户管理页面
2. 普通员工登录访问用户管理页面
3. 验证页面权限控制

预期结果:
- 管理员可以访问所有功能
- 普通员工无法访问管理功能
- 权限提示信息清晰

验收标准:
✅ 权限控制正确
✅ 角色区分明确
✅ 用户体验良好
```

### 4.2 性能指标（小企业标准）

| 功能模块 | 目标指标 | 验收标准 |
|----------|----------|----------|
| 登录响应 | < 2秒 | ✅ 快速响应 |
| 页面加载 | < 3秒 | ✅ 用户体验良好 |
| 并发用户 | 20人 | ✅ 满足小企业需求 |
| 系统稳定性 | 99%+ | ✅ 稳定运行 |

---

## 📦 **5. 交付物**

### 5.1 核心功能交付物

- 用户认证系统（登录/登出）
- 基础权限控制组件
- 用户管理界面
- 角色权限验证

### 5.2 技术交付物

- 数据库表结构和初始数据
- 认证服务和API接口
- 前端组件和页面
- 基础配置文件

### 5.3 文档交付物

- 系统使用说明
- 部署配置指南
- 基础维护手册

---

## 📋 **6. 技术要求**

### 6.1 环境要求

- Node.js 18+
- MySQL 8.0+
- React 18+
- TypeScript 5+

### 6.2 依赖包

```json
{
  "dependencies": {
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "mysql2": "^3.6.0",
    "antd": "^5.0.0"
  }
}
```

### 6.3 安全配置

```typescript
// 环境变量配置
JWT_SECRET=your-secret-key-here
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your-password
DB_NAME=erp_system
```

---

**极简版文档结束**

本文档为小企业ERP系统用户认证和权限管理系统的极简版需求规格说明，专注于核心功能实现，确保快速交付和低维护成本，完全满足小于20人企业的实际需求。
