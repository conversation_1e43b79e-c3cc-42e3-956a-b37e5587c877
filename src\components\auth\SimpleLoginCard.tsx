/**
 * 简化登录卡片组件
 * 
 * 根据PRD文档要求，提供极简的登录界面
 * 专为小于20人的企业设计，简单易用
 */

'use client'

import React, { useState } from 'react'
import { Card, Form, Input, Button, Alert, Space, Tag } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons'
import { SECURITY_CONFIG } from '@/config/security'

/**
 * 简化登录卡片属性接口
 */
export interface SimpleLoginCardProps {
  /** 登录成功回调 */
  onSuccess?: (user: any) => void
  /** 登录失败回调 */
  onError?: (error: string) => void
  /** 加载状态 */
  loading?: boolean
  /** 是否显示帮助信息 */
  showHelp?: boolean
}

/**
 * 简化登录表单数据接口
 */
interface SimpleLoginFormData {
  username: string
  password: string
}

/**
 * 简化登录卡片组件
 */
export const SimpleLoginCard: React.FC<SimpleLoginCardProps> = ({
  onSuccess,
  onError,
  loading: externalLoading = false,
  showHelp = true
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  /**
   * 处理登录提交
   */
  const handleLogin = async (values: SimpleLoginFormData) => {
    try {
      setLoading(true)
      setError('')

      // 调用登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          username: values.username,
          password: values.password,
          rememberMe: false // 简化版不提供记住我功能
        }),
      })

      const result = await response.json()

      if (result.status === 'success') {
        console.log('✅ [SimpleLoginCard] 登录成功')
        onSuccess?.(result.data.user)
      } else {
        const errorMessage = result.message || '登录失败，请重试'
        setError(errorMessage)
        onError?.(errorMessage)
      }
    } catch (error) {
      console.error('❌ [SimpleLoginCard] 登录请求失败:', error)
      const errorMessage = '网络错误，请检查网络连接后重试'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const isLoading = loading || externalLoading

  return (
    <Card
      title={
        <Space>
          <LoginOutlined />
          <span>系统登录</span>
          <Tag color="blue" size="small">极简版</Tag>
        </Space>
      }
      className="w-full max-w-md shadow-lg"
      bodyStyle={{ padding: '24px' }}
    >
      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={() => setError('')}
          className="mb-4"
        />
      )}

      {/* 简化说明 */}
      {showHelp && (
        <Alert
          message="ERP系统极简版"
          description={
            <div className="text-sm">
              <p>专为小企业设计的简化登录系统</p>
              <p>支持管理员和员工两种角色</p>
              <p>密码要求：最少{SECURITY_CONFIG.password.minLength}位字符</p>
            </div>
          }
          type="info"
          showIcon
          className="mb-4"
        />
      )}

      {/* 登录表单 */}
      <Form
        form={form}
        name="simpleLoginForm"
        layout="vertical"
        size="large"
        onFinish={handleLogin}
        disabled={isLoading}
      >
        {/* 用户名输入 */}
        <Form.Item
          name="username"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, message: '用户名至少3个字符' },
            { max: 50, message: '用户名不能超过50个字符' }
          ]}
        >
          <Input
            prefix={<UserOutlined className="text-gray-400" />}
            placeholder="请输入用户名"
            autoComplete="username"
          />
        </Form.Item>

        {/* 密码输入 */}
        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: SECURITY_CONFIG.password.minLength, message: `密码至少${SECURITY_CONFIG.password.minLength}个字符` }
          ]}
        >
          <Input.Password
            prefix={<LockOutlined className="text-gray-400" />}
            placeholder="请输入密码"
            autoComplete="current-password"
          />
        </Form.Item>

        {/* 登录按钮 */}
        <Form.Item className="mb-0">
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            className="w-full"
            size="large"
          >
            {isLoading ? '登录中...' : '登录'}
          </Button>
        </Form.Item>
      </Form>

      {/* 开发环境测试账户 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded">
          <div className="text-gray-700 text-sm">
            <div className="font-medium mb-2">测试账户：</div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>管理员:</span>
                <Space>
                  <Tag color="red">admin</Tag>
                  <Tag>admin123</Tag>
                </Space>
              </div>
              <div className="flex justify-between">
                <span>员工:</span>
                <Space>
                  <Tag color="blue">employee</Tag>
                  <Tag>employee123</Tag>
                </Space>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 系统信息 */}
      <div className="mt-4 text-center text-xs text-gray-500">
        <div>ERP管理系统 - 极简版</div>
        <div>适用于小于20人的企业</div>
      </div>
    </Card>
  )
}

/**
 * 简化登录页面布局组件
 */
export const SimpleLoginLayout: React.FC<{
  children: React.ReactNode
  title?: string
  subtitle?: string
}> = ({
  children,
  title = "ERP管理系统",
  subtitle = "企业资源规划管理系统 - 极简版"
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-md">
        {/* 系统标题 */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-2xl font-bold">ERP</span>
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {title}
          </h1>
          
          <p className="text-gray-600 text-sm">
            {subtitle}
          </p>
        </div>

        {/* 登录内容 */}
        {children}

        {/* 版权信息 */}
        <div className="text-center mt-8 text-gray-400 text-xs">
          © 2025 ERP管理系统. 保留所有权利.
        </div>
      </div>
    </div>
  )
}

export default SimpleLoginCard
