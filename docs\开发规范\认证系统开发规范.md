# 认证系统开发规范

**版本**: 2.0  
**更新日期**: 2025年8月1日  
**适用范围**: ERP系统认证相关开发

---

## 🎯 **核心原则**

### 1. 架构合规性
- **必须**: 所有认证操作通过DataAccessManager进行
- **禁止**: 直接导入和使用Token管理器类
- **必须**: 使用统一的JWT配置
- **禁止**: 重复定义JWT配置

### 2. 代码复用
- **必须**: 使用现有的认证服务和工具
- **禁止**: 创建重复的Token管理功能
- **必须**: 遵循DRY原则
- **禁止**: 复制粘贴认证相关代码

---

## 📋 **开发规范**

### 1. API路由开发

#### ✅ 正确示例
```typescript
// 文件: src/app/api/auth/example/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { AuthErrorHandler, AuthErrorCode } from '@/services/auth/AuthErrorHandler'

export async function POST(request: NextRequest) {
  try {
    // 使用DataAccessManager进行Token操作
    const tokenValidation = await dataAccessManager.tokenManagement.verifyAccessToken(token)
    
    if (!tokenValidation.isValid) {
      return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.TOKEN_INVALID)
    }
    
    // 业务逻辑...
    
  } catch (error) {
    AuthErrorHandler.logError(AuthErrorCode.INTERNAL_SERVER_ERROR, 'API异常', error)
    return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.INTERNAL_SERVER_ERROR)
  }
}
```

#### ❌ 错误示例
```typescript
// ❌ 直接导入Token管理器
import { SimpleTokenManager } from '@/utils/auth/SimpleTokenManager'

// ❌ 自定义错误响应
return NextResponse.json({ error: '错误' }, { status: 401 })

// ❌ 重复的JWT配置
const JWT_SECRET = process.env.JWT_ACCESS_SECRET || 'default'
```

### 2. 中间件开发

#### ✅ 正确示例
```typescript
// 文件: middleware.ts
import { TokenValidator } from '@/services/auth/TokenValidator'

export async function middleware(request: NextRequest) {
  // 使用TokenValidator进行轻量级验证
  const tokenInfo = TokenValidator.parseJWTToken(accessToken)
  
  if (!tokenInfo.valid) {
    return createUnauthorizedResponse(tokenInfo.error)
  }
  
  // 权限检查
  if (!TokenValidator.checkPagePermission(userPermissions, pathname)) {
    return createForbiddenResponse()
  }
}
```

#### ❌ 错误示例
```typescript
// ❌ 自定义JWT解析
function parseJWTToken(token: string) {
  // 重复实现JWT解析逻辑
}

// ❌ 重复的权限检查逻辑
function checkPermission(permissions: string[], path: string) {
  // 重复实现权限检查
}
```

### 3. 组件开发

#### ✅ 正确示例
```typescript
// 文件: src/components/auth/CustomPermissionGuard.tsx
import { PermissionGuard } from '@/components/auth/PermissionGuard'

const CustomComponent = () => {
  return (
    <PermissionGuard permission="user:read">
      <div>需要权限的内容</div>
    </PermissionGuard>
  )
}
```

#### ❌ 错误示例
```typescript
// ❌ 重复实现权限守卫逻辑
const CustomGuard = ({ children, permission }) => {
  // 重复的权限检查逻辑
}
```

---

## 🔧 **工具使用规范**

### 1. Token操作

```typescript
// ✅ 生成Token
const accessToken = await dataAccessManager.tokenManagement.generateAccessToken({
  userId: user.id,
  username: user.username,
  roles: user.roles.map(role => role.code),
  permissions: user.permissions
})

// ✅ 验证Token
const tokenValidation = await dataAccessManager.tokenManagement.verifyAccessToken(token)

// ✅ 检查Token是否需要刷新
const shouldRefresh = await dataAccessManager.tokenManagement.shouldRefreshToken(token, 5)
```

### 2. 错误处理

```typescript
// ✅ 创建标准错误响应
return AuthErrorHandler.createNextErrorResponse(
  AuthErrorCode.TOKEN_INVALID,
  '自定义错误信息'
)

// ✅ 记录错误日志
AuthErrorHandler.logError(
  AuthErrorCode.INTERNAL_SERVER_ERROR,
  '操作上下文',
  errorDetails
)

// ✅ 处理Token验证错误
const errorResponse = AuthErrorHandler.handleTokenValidationError(
  tokenInfo,
  '登录验证'
)
```

### 3. 配置使用

```typescript
// ✅ 使用统一配置
import { JWT_CONFIG, validateJWTConfig } from '@/config/jwt.config'

// 验证配置
validateJWTConfig()

// 使用配置
const secret = JWT_CONFIG.accessToken.secret
const expiresIn = JWT_CONFIG.accessToken.expiresIn
```

---

## 🧪 **测试规范**

### 1. 单元测试

```typescript
// 文件: src/__tests__/auth/token-management.test.ts
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'

describe('Token管理测试', () => {
  test('应该能生成有效的Access Token', async () => {
    const payload = { userId: 'test', username: 'testuser' }
    const token = await dataAccessManager.tokenManagement.generateAccessToken(payload)
    
    expect(token).toBeDefined()
    expect(typeof token).toBe('string')
  })
  
  test('应该能验证有效的Token', async () => {
    const payload = { userId: 'test', username: 'testuser' }
    const token = await dataAccessManager.tokenManagement.generateAccessToken(payload)
    const validation = await dataAccessManager.tokenManagement.verifyAccessToken(token)
    
    expect(validation.isValid).toBe(true)
    expect(validation.payload.userId).toBe('test')
  })
})
```

### 2. 集成测试

```typescript
// 文件: src/__tests__/integration/auth-flow.test.ts
describe('认证流程集成测试', () => {
  test('完整的登录流程', async () => {
    // 1. 登录请求
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ username: 'test', password: 'password' })
    
    expect(loginResponse.status).toBe(200)
    expect(loginResponse.body.data.accessToken).toBeDefined()
    
    // 2. 使用Token访问受保护资源
    const profileResponse = await request(app)
      .get('/api/auth/profile')
      .set('Cookie', loginResponse.headers['set-cookie'])
    
    expect(profileResponse.status).toBe(200)
  })
})
```

---

## 📊 **代码质量标准**

### 1. 架构合规性检查

```bash
# 运行架构合规性检查
npm run check:architecture

# 运行架构合规性测试
npm test -- --testPathPattern=architecture
```

### 2. 代码覆盖率要求

- **Token管理**: ≥ 90%
- **错误处理**: ≥ 85%
- **API路由**: ≥ 80%
- **组件**: ≥ 75%

### 3. 性能要求

- **Token生成**: < 50ms
- **Token验证**: < 20ms (缓存命中 < 5ms)
- **API响应**: < 200ms
- **缓存命中率**: > 80%

---

## 🔍 **代码审查清单**

### 认证相关PR必检项

- [ ] **架构合规性**: 是否通过DataAccessManager访问Token服务
- [ ] **重复代码**: 是否复用现有的认证组件和服务
- [ ] **错误处理**: 是否使用AuthErrorHandler统一处理错误
- [ ] **配置使用**: 是否使用统一的JWT配置
- [ ] **测试覆盖**: 是否包含充分的单元测试和集成测试
- [ ] **性能影响**: 是否考虑缓存和性能优化
- [ ] **安全性**: 是否正确处理敏感信息
- [ ] **文档更新**: 是否更新相关文档

### 代码质量检查

- [ ] **TypeScript类型**: 所有参数和返回值都有正确的类型定义
- [ ] **错误处理**: 所有异步操作都有适当的错误处理
- [ ] **日志记录**: 关键操作都有适当的日志记录
- [ ] **注释文档**: 复杂逻辑都有清晰的注释说明

---

## 🚨 **常见错误和解决方案**

### 1. 架构违规错误

**错误**: 直接导入Token管理器
```typescript
// ❌ 错误
import { SimpleTokenManager } from '@/utils/auth/SimpleTokenManager'
```

**解决方案**:
```typescript
// ✅ 正确
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
```

### 2. 重复代码错误

**错误**: 重复实现JWT验证逻辑
```typescript
// ❌ 错误
function verifyToken(token: string) {
  // 重复的验证逻辑
}
```

**解决方案**:
```typescript
// ✅ 正确
const validation = await dataAccessManager.tokenManagement.verifyAccessToken(token)
```

### 3. 错误处理不一致

**错误**: 自定义错误响应格式
```typescript
// ❌ 错误
return NextResponse.json({ error: '错误信息' }, { status: 401 })
```

**解决方案**:
```typescript
// ✅ 正确
return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.TOKEN_INVALID)
```

---

## 📚 **参考资源**

- [认证系统架构设计文档](./认证系统架构设计.md)
- [Token管理架构迁移指南](../架构优化/Token管理架构迁移指南.md)
- [架构合规性审查报告](../架构审查/用户认证和权限管理系统架构合规性审查报告.md)
- [API文档](../API文档/)

---

**规范维护**: 架构团队  
**执行监督**: 代码审查团队  
**更新频率**: 根据架构演进需要
