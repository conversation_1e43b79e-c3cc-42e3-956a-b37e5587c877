"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_scheduling_SchedulingStateManager_ts";
exports.ids = ["_ssr_src_services_scheduling_SchedulingStateManager_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/scheduling/SchedulingStateManager.ts":
/*!***********************************************************!*\
  !*** ./src/services/scheduling/SchedulingStateManager.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SchedulingStateManager: () => (/* binding */ SchedulingStateManager)\n/* harmony export */ });\n/* harmony import */ var _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/dataAccess/DataAccessManager */ \"(ssr)/./src/services/dataAccess/DataAccessManager.ts\");\n/**\n * 排程状态管理服务\n * 负责管理排程过程中的虚拟状态计算和状态变更记录\n * 注意：实际状态应用已统一到 SameMoldPrioritySchedulingService.applySchedulingResults\n */ \n/**\n * 排程状态管理器\n */ class SchedulingStateManager {\n    static{\n        this.changeHistory = [];\n    }\n    /**\n   * 创建工位状态快照（使用dataAccessManager）\n   */ static async createWorkstationSnapshot(description) {\n        try {\n            // 使用dataAccessManager获取当前工位状态\n            const response = await _services_dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.workstations.getWorkstations();\n            if (response.status !== \"success\" || !response.data?.items) {\n                throw new Error(\"获取工位数据失败\");\n            }\n            const workstations = response.data.items;\n            // 记录状态变更\n            const changeRecord = {\n                id: `snapshot_${Date.now()}`,\n                timestamp: new Date().toISOString(),\n                operation: \"snapshot\",\n                description: description || \"排程前工位状态快照\",\n                affectedWorkstations: workstations.map((ws)=>ws.id),\n                affectedWorkOrders: []\n            };\n            this.changeHistory.push(changeRecord);\n            console.log(`📸 [SchedulingStateManager] 创建工位状态快照: ${workstations.length} 个工位`);\n            return workstations;\n        } catch (error) {\n            console.error(\"❌ [SchedulingStateManager] 创建工位状态快照失败:\", error);\n            throw error;\n        }\n    }\n    /**\n   * 创建虚拟工位状态（用于计算阶段）\n   */ static createVirtualWorkstationStates(originalWorkstations) {\n        const virtualWorkstations = originalWorkstations.map((ws)=>({\n                ...JSON.parse(JSON.stringify(ws)),\n                isVirtual: true,\n                originalState: {\n                    ...ws\n                }\n            }));\n        return virtualWorkstations;\n    }\n    /**\n   * 计算虚拟工位状态变更（不影响实际状态）\n   */ static calculateVirtualStateChanges(virtualWorkstations, results) {\n        // 按工位分组排程结果\n        const resultsByWorkstation = results.reduce((groups, result)=>{\n            const key = result.workstationCode;\n            if (!groups[key]) {\n                groups[key] = [];\n            }\n            groups[key].push(result);\n            return groups;\n        }, {});\n        // 更新虚拟工位状态\n        const updatedVirtualWorkstations = virtualWorkstations.map((vws)=>{\n            const workstationResults = resultsByWorkstation[vws.code] || [];\n            if (workstationResults.length === 0) {\n                return vws // 无变更\n                ;\n            }\n            // 按结束时间排序，取最后一个作为最终状态\n            const sortedResults = workstationResults.sort((a, b)=>new Date(a.plannedEndTime).getTime() - new Date(b.plannedEndTime).getTime());\n            const lastResult = sortedResults[sortedResults.length - 1];\n            // 更新虚拟工位状态\n            const updatedVws = {\n                ...vws,\n                lastEndTime: lastResult.plannedEndTime,\n                currentMoldNumber: lastResult.formingMoldNumber,\n                currentBatchNumber: lastResult.batchNumber,\n                batchNumberQueue: [\n                    ...vws.batchNumberQueue || [],\n                    ...workstationResults.map((r)=>r.batchNumber)\n                ]\n            };\n            console.log(`🔄 [SchedulingStateManager] 虚拟工位状态更新: ${vws.code}`);\n            return updatedVws;\n        });\n        // 记录虚拟计算操作\n        const changeRecord = {\n            id: `virtual_calc_${Date.now()}`,\n            timestamp: new Date().toISOString(),\n            operation: \"virtual_calculation\",\n            description: \"虚拟工位状态计算完成\",\n            affectedWorkstations: [\n                ...new Set(results.map((r)=>r.workstationCode))\n            ],\n            affectedWorkOrders: results.map((r)=>r.workOrderId)\n        };\n        this.changeHistory.push(changeRecord);\n        return updatedVirtualWorkstations;\n    }\n    /**\n   * @deprecated 此方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults\n   * 实际状态应用已统一到 SameMoldPrioritySchedulingService.applySchedulingResults 方法\n   */ static async applySchedulingToActualState(results) {\n        console.warn(\"⚠️ [SchedulingStateManager] applySchedulingToActualState 方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults\");\n        throw new Error(\"此方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults\");\n    }\n    /**\n   * @deprecated 此方法已废弃，回滚功能需要通过具体的业务逻辑实现\n   * 排程回滚应该通过重新计算和应用来实现，而不是依赖快照恢复\n   */ static rollbackStateChanges(snapshot) {\n        console.warn(\"⚠️ [SchedulingStateManager] rollbackStateChanges 方法已废弃\");\n        console.warn(\"排程回滚应该通过重新计算和应用来实现，而不是依赖快照恢复\");\n        // 记录回滚尝试\n        const changeRecord = {\n            id: `rollback_attempt_${Date.now()}`,\n            timestamp: new Date().toISOString(),\n            operation: \"virtual_calculation\",\n            description: \"尝试回滚操作（已废弃）\",\n            affectedWorkstations: [],\n            affectedWorkOrders: []\n        };\n        this.changeHistory.push(changeRecord);\n    }\n    /**\n   * @deprecated 此方法已废弃，工位状态更新逻辑已统一到 SameMoldPrioritySchedulingService\n   */ static calculateWorkstationUpdates(results) {\n        console.warn(\"⚠️ [SchedulingStateManager] calculateWorkstationUpdates 方法已废弃\");\n        return [];\n    }\n    /**\n   * 获取状态变更历史\n   */ static getChangeHistory() {\n        return [\n            ...this.changeHistory\n        ];\n    }\n    /**\n   * 清理状态变更历史\n   */ static clearChangeHistory() {\n        this.changeHistory = [];\n    }\n    /**\n   * 获取当前排程状态信息\n   */ static getCurrentSchedulingState() {\n        return {\n            changeHistoryCount: this.changeHistory.length,\n            lastChangeTime: this.changeHistory.length > 0 ? this.changeHistory[this.changeHistory.length - 1].timestamp : null,\n            operations: this.changeHistory.map((record)=>({\n                    id: record.id,\n                    operation: record.operation,\n                    description: record.description,\n                    timestamp: record.timestamp\n                }))\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/scheduling/SchedulingStateManager.ts\n");

/***/ })

};
;