"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_services_mrpService_ts"],{

/***/ "(app-pages-browser)/./src/services/mrpService.ts":
/*!************************************!*\
  !*** ./src/services/mrpService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mrpService: function() { return /* binding */ mrpService; }\n/* harmony export */ });\n/* harmony import */ var _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataAccess/DataAccessManager */ \"(app-pages-browser)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var _BusinessIdGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BusinessIdGenerator */ \"(app-pages-browser)/./src/services/BusinessIdGenerator.ts\");\n/**\n * MRP（物料需求计划）服务\n * 完整版本，支持共享模具处理功能\n */ \n\n/**\n * MRP服务类\n */ class MRPService {\n    static getInstance() {\n        if (!MRPService.instance) {\n            MRPService.instance = new MRPService();\n        }\n        return MRPService.instance;\n    }\n    /**\n   * 执行MRP\n   */ async executeMRP(request) {\n        const { salesOrder, executedBy, executionDate } = request;\n        console.log(\"\\uD83D\\uDE80 [MRPService] 开始执行MRP\", {\n            orderId: salesOrder.id,\n            orderNumber: salesOrder.orderNumber,\n            executedBy\n        });\n        try {\n            // 1. 库存检查\n            const inventoryCheckResults = await this.checkInventory(salesOrder);\n            // 2. 分析共享模具情况\n            const sharedMoldAnalysis = await this.analyzeSharedMolds(salesOrder, inventoryCheckResults);\n            // 🔧 修复：生成MRP执行ID，用于生产订单创建\n            const mrpExecutionId = \"mrp-\".concat(Date.now());\n            // 3. 生成生产订单（包含共享模具处理）\n            const generatedProductionOrders = await this.generateProductionOrdersWithSharedMold(salesOrder, inventoryCheckResults, sharedMoldAnalysis, mrpExecutionId, executedBy);\n            // 4. 计算统计信息\n            const totalShortageValue = inventoryCheckResults.reduce((sum, item)=>sum + item.shortageQuantity * 100, 0);\n            // 5. 统计共享模具订单和传统订单数量\n            const sharedMoldOrders = generatedProductionOrders.filter((order)=>order.isSharedMold).length;\n            const traditionalOrders = generatedProductionOrders.filter((order)=>!order.isSharedMold).length;\n            // 6. 构建MRP结果\n            const mrpResult = {\n                id: mrpExecutionId,\n                salesOrderId: salesOrder.id,\n                salesOrderNumber: salesOrder.orderNumber,\n                executionDate,\n                status: generatedProductionOrders.length > 0 ? \"success\" : \"partial_shortage\",\n                totalProductionOrders: generatedProductionOrders.length,\n                sharedMoldOrders,\n                traditionalOrders,\n                inventoryCheckResults,\n                generatedProductionOrders,\n                totalShortageValue,\n                executedBy,\n                executedAt: new Date().toISOString()\n            };\n            console.log(\"✅ [MRPService] MRP执行完成\", {\n                resultId: mrpResult.id,\n                totalOrders: mrpResult.totalProductionOrders,\n                sharedMoldOrders: mrpResult.sharedMoldOrders,\n                traditionalOrders: mrpResult.traditionalOrders,\n                status: mrpResult.status\n            });\n            return mrpResult;\n        } catch (error) {\n            console.error(\"❌ [MRPService] MRP执行失败:\", error);\n            // 返回失败结果\n            return {\n                id: \"mrp-error-\".concat(Date.now()),\n                salesOrderId: salesOrder.id,\n                salesOrderNumber: salesOrder.orderNumber,\n                executionDate,\n                status: \"failed\",\n                totalProductionOrders: 0,\n                sharedMoldOrders: 0,\n                traditionalOrders: 0,\n                inventoryCheckResults: [],\n                generatedProductionOrders: [],\n                totalShortageValue: 0,\n                executedBy,\n                executedAt: new Date().toISOString()\n            };\n        }\n    }\n    /**\n   * 检查库存\n   */ async checkInventory(salesOrder) {\n        const results = [];\n        for (const item of salesOrder.items){\n            try {\n                // 获取产品库存信息 - 修复：使用正确的API调用方式\n                const inventoryResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getByProductCode(item.productCode);\n                let availableQuantity = 0;\n                if (inventoryResponse.status === \"success\" && inventoryResponse.data) {\n                    // 使用currentStock字段作为可用数量\n                    availableQuantity = inventoryResponse.data.currentStock || 0;\n                }\n                const requiredQuantity = item.quantity;\n                const shortageQuantity = Math.max(0, requiredQuantity - availableQuantity);\n                results.push({\n                    productCode: item.productCode,\n                    productName: item.productName,\n                    requiredQuantity,\n                    availableQuantity,\n                    shortageQuantity,\n                    isSufficient: shortageQuantity === 0\n                });\n            } catch (error) {\n                console.error(\"❌ [MRPService] 库存检查失败 - 产品: \".concat(item.productCode), error);\n                // 如果库存检查失败，假设需要全部生产\n                results.push({\n                    productCode: item.productCode,\n                    productName: item.productName,\n                    requiredQuantity: item.quantity,\n                    availableQuantity: 0,\n                    shortageQuantity: item.quantity,\n                    isSufficient: false\n                });\n            }\n        }\n        return results;\n    }\n    /**\n   * 分析共享模具情况\n   */ async analyzeSharedMolds(salesOrder, inventoryCheckResults) {\n        console.log(\"\\uD83D\\uDD0D [MRPService] 开始分析共享模具情况\");\n        const sharedMoldGroups = [];\n        const moldProductMap = new Map();\n        // 1. 按模具编号分组产品\n        for (const checkResult of inventoryCheckResults){\n            if (checkResult.shortageQuantity > 0) {\n                try {\n                    // 获取产品主数据中的成型模具编号\n                    const productResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(checkResult.productCode);\n                    if (productResponse.status === \"success\" && productResponse.data) {\n                        const moldNumber = productResponse.data.formingMold || \"\";\n                        if (moldNumber) {\n                            const productRequirement = {\n                                productCode: checkResult.productCode,\n                                productName: checkResult.productName,\n                                productModelCode: productResponse.data.modelCode,\n                                requiredQuantity: checkResult.requiredQuantity,\n                                deliveryDate: salesOrder.deliveryDate,\n                                sourceOrderId: salesOrder.id,\n                                sourceOrderNumber: salesOrder.orderNumber,\n                                customerName: salesOrder.customerName,\n                                urgencyLevel: \"medium\"\n                            };\n                            if (!moldProductMap.has(moldNumber)) {\n                                moldProductMap.set(moldNumber, []);\n                            }\n                            moldProductMap.get(moldNumber).push(productRequirement);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"❌ 获取产品 \".concat(checkResult.productCode, \" 主数据失败:\"), error);\n                }\n            }\n        }\n        // 2. 识别共享模具（多个产品使用同一模具）\n        for (const [moldNumber, products] of moldProductMap.entries()){\n            if (products.length > 1) {\n                console.log(\"\\uD83D\\uDD27 发现共享模具: \".concat(moldNumber, \", 包含 \").concat(products.length, \" 个产品\"));\n                const maxRequiredQuantity = Math.max(...products.map((p)=>p.requiredQuantity));\n                // 计算最大缺货量\n                const maxShortageQuantity = Math.max(...products.map((p)=>{\n                    const checkResult = inventoryCheckResults.find((r)=>r.productCode === p.productCode);\n                    return (checkResult === null || checkResult === void 0 ? void 0 : checkResult.shortageQuantity) || 0;\n                }));\n                const sharedMoldGroup = {\n                    moldNumber,\n                    deliveryDate: salesOrder.deliveryDate,\n                    products,\n                    isFirstOccurrence: true,\n                    maxRequiredQuantity,\n                    maxShortageQuantity\n                };\n                sharedMoldGroups.push(sharedMoldGroup);\n            }\n        }\n        console.log(\"✅ [MRPService] 共享模具分析完成，发现 \".concat(sharedMoldGroups.length, \" 个共享模具组\"));\n        return sharedMoldGroups;\n    }\n    /**\n   * 生成生产订单（包含共享模具处理）\n   * 🔧 修复：添加MRP执行信息参数，用于新的createFromMRP方法\n   */ async generateProductionOrdersWithSharedMold(salesOrder, inventoryCheckResults, sharedMoldGroups, mrpExecutionId, executedBy) {\n        const productionOrders = [];\n        const processedProducts = new Set();\n        // 1. 处理共享模具订单\n        for (const moldGroup of sharedMoldGroups){\n            const sharedMoldOrder = await this.generateSharedMoldOrder(moldGroup, inventoryCheckResults, salesOrder, mrpExecutionId, executedBy);\n            if (sharedMoldOrder) {\n                productionOrders.push(sharedMoldOrder);\n                // 标记已处理的产品\n                moldGroup.products.forEach((p)=>processedProducts.add(p.productCode));\n            }\n        }\n        // 2. 处理传统订单（未被共享模具处理的产品）\n        for (const checkResult of inventoryCheckResults){\n            if (checkResult.shortageQuantity > 0 && !processedProducts.has(checkResult.productCode)) {\n                const traditionalOrder = await this.generateTraditionalOrder(checkResult, salesOrder, mrpExecutionId, executedBy);\n                if (traditionalOrder) {\n                    productionOrders.push(traditionalOrder);\n                }\n            }\n        }\n        return productionOrders;\n    }\n    /**\n   * 生成传统生产订单（原有逻辑）\n   * 🔧 修复：添加MRP执行信息参数，用于新的createFromMRP方法\n   */ async generateTraditionalOrder(checkResult, salesOrder, mrpExecutionId, executedBy) {\n        try {\n            // 获取产品主数据中的成型模具编号\n            let formingMoldNumber = \"\";\n            const productResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(checkResult.productCode);\n            if (productResponse.status === \"success\" && productResponse.data) {\n                formingMoldNumber = productResponse.data.formingMold || \"\";\n                console.log(\"✅ 获取产品 \".concat(checkResult.productCode, \" 的成型模具编号: \").concat(formingMoldNumber));\n            } else {\n                console.warn(\"⚠️ 未找到产品 \".concat(checkResult.productCode, \" 的主数据，模具编号将为空\"));\n            }\n            // 获取客户信息和信用等级\n            let customerCreditLevel = undefined;\n            const customerResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.customers.getById(salesOrder.customerId);\n            if (customerResponse.status === \"success\" && customerResponse.data) {\n                customerCreditLevel = customerResponse.data.customerLevel;\n                console.log(\"✅ 获取客户 \".concat(salesOrder.customerName, \" 的信用等级: \").concat(customerCreditLevel));\n            } else {\n                console.warn(\"⚠️ 未找到客户 \".concat(salesOrder.customerId, \" 的主数据，信用等级将为空\"));\n            }\n            // 🔧 简化：基于销售订单号生成生产订单ID，固定格式\n            const orderNumber = _BusinessIdGenerator__WEBPACK_IMPORTED_MODULE_1__.BusinessIdGenerator.generateProductionOrderId(salesOrder.orderNumber);\n            const productionOrder = {\n                id: \"po-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                orderNumber,\n                // 产品信息\n                productName: checkResult.productName,\n                productCode: checkResult.productCode,\n                // 共享模具相关字段\n                formingMoldNumber: formingMoldNumber,\n                isSharedMold: false,\n                productItems: [],\n                // 数量字段\n                plannedQuantity: checkResult.shortageQuantity,\n                producedQuantity: 0,\n                // 时间字段\n                startDate: new Date().toISOString().split(\"T\")[0],\n                endDate: salesOrder.deliveryDate,\n                deliveryDate: salesOrder.deliveryDate,\n                // 状态字段\n                status: \"in_plan\",\n                workstation: \"默认工位\",\n                // 客户信息\n                customerName: salesOrder.customerName,\n                customerId: salesOrder.customerId,\n                customerCreditLevel: customerCreditLevel,\n                prioritySource: \"auto\",\n                // 订单追溯\n                salesOrderNumber: salesOrder.orderNumber,\n                sourceOrderNumbers: [\n                    salesOrder.orderNumber\n                ],\n                sourceOrderIds: [\n                    salesOrder.id\n                ],\n                // 其他字段\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // 🔧 修复：使用新的createFromMRP方法，添加MRP执行信息\n            const createResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.productionOrders.createFromMRP({\n                ...productionOrder,\n                mrpExecutionId,\n                mrpExecutedBy: executedBy,\n                mrpExecutedAt: new Date().toISOString()\n            });\n            if (createResponse.status !== \"success\") {\n                throw new Error(\"生产订单创建失败: \".concat(createResponse.message));\n            }\n            console.log(\"✅ [MRPService] 传统生产订单已保存: \".concat(productionOrder.orderNumber, \", MRP执行ID: \").concat(mrpExecutionId));\n            return productionOrder;\n        } catch (error) {\n            console.error(\"❌ [MRPService] 生成传统订单失败 - 产品: \".concat(checkResult.productCode), error);\n            return null;\n        }\n    }\n    /**\n   * 生成共享模具生产订单\n   * 🔧 修复：添加MRP执行信息参数，用于新的createFromMRP方法\n   */ async generateSharedMoldOrder(moldGroup, inventoryCheckResults, salesOrder, mrpExecutionId, executedBy) {\n        try {\n            console.log(\"\\uD83D\\uDD27 [MRPService] 开始生成共享模具订单: \".concat(moldGroup.moldNumber));\n            // 获取客户信息和信用等级\n            let customerCreditLevel = undefined;\n            const customerResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.customers.getById(salesOrder.customerId);\n            if (customerResponse.status === \"success\" && customerResponse.data) {\n                customerCreditLevel = customerResponse.data.customerLevel;\n            }\n            // 🔧 简化：基于销售订单号生成生产订单ID，固定格式\n            const orderNumber = _BusinessIdGenerator__WEBPACK_IMPORTED_MODULE_1__.BusinessIdGenerator.generateProductionOrderId(salesOrder.orderNumber);\n            // 构建产品明细列表\n            const productItems = moldGroup.products.map((product)=>({\n                    id: \"item-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                    productCode: product.productCode,\n                    productName: product.productName,\n                    plannedQuantity: product.requiredQuantity,\n                    producedQuantity: 0,\n                    requiredQuantity: product.requiredQuantity,\n                    sourceOrderItems: [\n                        salesOrder.id\n                    ] // 简化处理\n                }));\n            var _moldGroup_maxShortageQuantity;\n            // 使用最大缺货量作为计划数量（如果没有则使用最大需求量）\n            const plannedQuantity = (_moldGroup_maxShortageQuantity = moldGroup.maxShortageQuantity) !== null && _moldGroup_maxShortageQuantity !== void 0 ? _moldGroup_maxShortageQuantity : moldGroup.maxRequiredQuantity;\n            // 主产品信息（使用第一个产品作为主产品）\n            const primaryProduct = moldGroup.products[0];\n            const sharedMoldOrder = {\n                id: \"po-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                orderNumber,\n                // 产品信息（共享模具订单的特殊处理）\n                productName: \"\".concat(moldGroup.moldNumber, \" 共享模具生产\"),\n                productCode: primaryProduct.productCode,\n                // 共享模具相关字段\n                formingMoldNumber: moldGroup.moldNumber,\n                isSharedMold: true,\n                moldGroup: moldGroup.moldNumber,\n                productItems,\n                // 数量字段\n                plannedQuantity,\n                producedQuantity: 0,\n                // 时间字段\n                startDate: new Date().toISOString().split(\"T\")[0],\n                endDate: moldGroup.deliveryDate,\n                deliveryDate: moldGroup.deliveryDate,\n                // 状态字段\n                status: \"in_plan\",\n                workstation: \"默认工位\",\n                // 客户信息\n                customerName: salesOrder.customerName,\n                customerId: salesOrder.customerId,\n                customerCreditLevel: customerCreditLevel,\n                prioritySource: \"auto\",\n                // 订单追溯\n                salesOrderNumber: salesOrder.orderNumber,\n                sourceOrderNumbers: [\n                    ...new Set(moldGroup.products.map((p)=>p.sourceOrderNumber))\n                ],\n                sourceOrderIds: [\n                    ...new Set(moldGroup.products.map((p)=>p.sourceOrderId))\n                ],\n                // 其他字段\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // 🔧 修复：使用新的createFromMRP方法，添加MRP执行信息\n            const createResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.productionOrders.createFromMRP({\n                ...sharedMoldOrder,\n                mrpExecutionId,\n                mrpExecutedBy: executedBy,\n                mrpExecutedAt: new Date().toISOString()\n            });\n            if (createResponse.status !== \"success\") {\n                throw new Error(\"共享模具生产订单创建失败: \".concat(createResponse.message));\n            }\n            console.log(\"✅ [MRPService] 共享模具生产订单已保存: \".concat(sharedMoldOrder.orderNumber, \", 生产数量: \").concat(plannedQuantity, \", MRP执行ID: \").concat(mrpExecutionId));\n            return sharedMoldOrder;\n        } catch (error) {\n            console.error(\"❌ [MRPService] 生成共享模具订单失败 - 模具: \".concat(moldGroup.moldNumber), error);\n            return null;\n        }\n    }\n}\n// 导出单例实例\nconst mrpService = MRPService.getInstance();\n/* harmony default export */ __webpack_exports__[\"default\"] = (mrpService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/mrpService.ts\n"));

/***/ })

}]);