/**
 * 认证提供者组件
 * 
 * 为整个应用提供认证上下文和状态管理
 * 处理自动登录、Token刷新等全局认证逻辑
 */

'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { Spin, message } from 'antd'
import { AuthContext, User } from '@/types/auth'

// 创建认证上下文
const AuthContextProvider = createContext<AuthContext | null>(null)

/**
 * 认证提供者组件Props
 */
interface AuthProviderProps {
  children: React.ReactNode
}

/**
 * 认证提供者组件
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const pathname = usePathname()
  const router = useRouter()

  /**
   * 公开路径列表
   */
  const publicPaths = ['/login', '/forgot-password', '/403']

  /**
   * 检查是否为公开路径
   */
  const isPublicPath = (path: string): boolean => {
    return publicPaths.some(publicPath => path.startsWith(publicPath))
  }

  /**
   * 获取用户信息
   */
  const fetchUserProfile = async (): Promise<boolean> => {
    try {
      console.log('🔍 [AuthProvider] 开始获取用户信息')

      const response = await fetch('/api/auth/profile', {
        method: 'GET',
        credentials: 'include',
      })

      console.log('🔍 [AuthProvider] Profile API响应状态:', response.status)

      if (!response.ok) {
        console.log('❌ [AuthProvider] Profile API请求失败:', response.status, response.statusText)
        setUser(null)
        setIsAuthenticated(false)
        return false
      }

      const result = await response.json()
      console.log('🔍 [AuthProvider] Profile API响应结果:', result.status, result.message)

      if (result.status === 'success') {
        console.log('✅ [AuthProvider] 用户信息获取成功:', result.data.username)
        setUser(result.data)
        setIsAuthenticated(true)
        return true
      } else {
        console.log('❌ [AuthProvider] 用户信息获取失败:', result.message)
        setUser(null)
        setIsAuthenticated(false)
        return false
      }
    } catch (error) {
      console.error('❌ [AuthProvider] 获取用户信息异常:', error)
      setUser(null)
      setIsAuthenticated(false)
      return false
    }
  }

  /**
   * 自动刷新Token
   */
  const autoRefreshToken = async (): Promise<boolean> => {
    try {
      console.log('🔄 [AuthProvider] 开始刷新Token')

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      })

      console.log('🔍 [AuthProvider] Refresh API响应状态:', response.status)

      if (!response.ok) {
        console.log('❌ [AuthProvider] Refresh API请求失败:', response.status, response.statusText)
        return false
      }

      const result = await response.json()
      console.log('🔍 [AuthProvider] Refresh API响应结果:', result.status, result.message)

      if (result.status === 'success') {
        console.log('✅ [AuthProvider] Token自动刷新成功')
        return true
      } else {
        console.log('❌ [AuthProvider] Token自动刷新失败:', result.message)
        return false
      }
    } catch (error) {
      console.error('❌ [AuthProvider] Token自动刷新异常:', error)
      return false
    }
  }

  /**
   * 处理认证失败
   */
  const handleAuthFailure = () => {
    setUser(null)
    setIsAuthenticated(false)
    
    if (!isPublicPath(pathname)) {
      router.push('/login')
    }
  }

  /**
   * 初始化认证状态
   */
  useEffect(() => {
    const initAuth = async () => {
      console.log('🔄 [AuthProvider] 初始化认证状态, 路径:', pathname)
      setIsLoading(true)

      // 如果是公开路径，直接完成加载
      if (isPublicPath(pathname)) {
        console.log('📖 [AuthProvider] 公开路径，跳过认证检查')
        setIsLoading(false)
        return
      }

      // 如果已经认证且用户信息存在，跳过重新初始化
      if (isAuthenticated && user) {
        console.log('✅ [AuthProvider] 用户已认证，跳过重新初始化')
        setIsLoading(false)
        return
      }

      // 尝试获取用户信息
      const success = await fetchUserProfile()
      console.log('🔍 [AuthProvider] 用户信息获取结果:', success)
      
      if (!success) {
        // 获取用户信息失败，尝试刷新Token
        console.log('🔄 [AuthProvider] 尝试刷新Token')
        const refreshSuccess = await autoRefreshToken()
        
        if (refreshSuccess) {
          // Token刷新成功，重新获取用户信息
          console.log('✅ [AuthProvider] Token刷新成功，重新获取用户信息')
          await fetchUserProfile()
        } else {
          // Token刷新失败，跳转到登录页
          console.log('❌ [AuthProvider] Token刷新失败，准备跳转登录页')
          handleAuthFailure()
        }
      }

      setIsLoading(false)
    }

    initAuth()
  }, [pathname]) // 移除router依赖，避免不必要的重新初始化

  /**
   * 监听Token刷新需求
   */
  useEffect(() => {
    const handleTokenRefreshNeeded = async () => {
      if (isAuthenticated) {
        await autoRefreshToken()
      }
    }

    // 监听来自中间件的Token刷新提示
    const handleResponse = (event: Event) => {
      const response = (event as any).detail?.response
      if (response?.headers?.get('X-Token-Refresh-Needed') === 'true') {
        handleTokenRefreshNeeded()
      }
    }

    window.addEventListener('fetch-response', handleResponse)
    
    return () => {
      window.removeEventListener('fetch-response', handleResponse)
    }
  }, [isAuthenticated])

  /**
   * 定时刷新Token
   */
  useEffect(() => {
    if (!isAuthenticated) {
      return
    }

    // 每50分钟自动刷新Token
    const interval = setInterval(async () => {
      if (isAuthenticated) {
        const success = await autoRefreshToken()
        if (!success) {
          handleAuthFailure()
        }
      }
    }, 50 * 60 * 1000) // 50分钟

    return () => clearInterval(interval)
  }, [isAuthenticated])

  /**
   * 认证上下文值
   */
  const contextValue: AuthContext = {
    user,
    isAuthenticated,
    isLoading,
    login: async (credentials) => {
      // 登录逻辑在useAuth Hook中实现
      throw new Error('请使用useAuth Hook进行登录')
    },
    logout: async () => {
      // 登出逻辑在useAuth Hook中实现
      throw new Error('请使用useAuth Hook进行登出')
    },
    refreshToken: async () => {
      // Token刷新逻辑在useAuth Hook中实现
      throw new Error('请使用useAuth Hook进行Token刷新')
    },
    checkPermission: (permission: string) => {
      if (!user || !isAuthenticated) {
        return false
      }

      // 系统管理员拥有所有权限
      if (user.roles.some(role => role.code === 'admin')) {
        return true
      }

      // 检查用户是否拥有指定权限
      const userPermissions = user.roles.flatMap(role => 
        role.permissions.map(perm => perm.code)
      )

      return userPermissions.includes(permission)
    },
    checkRole: (role: string) => {
      if (!user || !isAuthenticated) {
        return false
      }

      return user.roles.some(userRole => userRole.code === role)
    }
  }

  // 加载中状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" tip="正在加载..." />
      </div>
    )
  }

  return (
    <AuthContextProvider.Provider value={contextValue}>
      {children}
    </AuthContextProvider.Provider>
  )
}

/**
 * 使用认证上下文的Hook
 */
export const useAuthContext = (): AuthContext => {
  const context = useContext(AuthContextProvider)
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  return context
}
