/**
 * 简化权限控制Hook
 * 
 * 根据PRD要求，提供简单易用的权限检查功能
 * 专为admin/employee两级权限设计
 */

import { useMemo } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useSimpleAuth } from '@/contexts/SimpleAuthContext'
import { SimplePermissionManager, SimplePermissionLevel } from '@/utils/auth/SimplePermissionManager'

/**
 * 简化权限检查结果接口
 */
export interface SimplePermissionHookResult {
  /** 用户权限级别 */
  userLevel: SimplePermissionLevel | null
  /** 是否为管理员 */
  isAdmin: boolean
  /** 是否为员工 */
  isEmployee: boolean
  /** 检查是否有指定权限 */
  hasPermission: (permission: string) => boolean
  /** 检查是否有指定角色 */
  hasRole: (role: SimplePermissionLevel) => boolean
  /** 获取用户可访问的功能列表 */
  getAccessibleFeatures: () => string[]
  /** 获取权限级别描述 */
  getLevelDescription: () => string
}

/**
 * 简化权限控制Hook
 * 
 * 提供简单易用的权限检查功能，专为PRD的极简版需求设计
 * 
 * @returns 权限检查结果和相关方法
 */
export const useSimplePermission = (): SimplePermissionHookResult => {
  // 优先使用简化认证上下文，如果不可用则回退到复杂认证
  let user, isAuthenticated

  try {
    const simpleAuth = useSimpleAuth()
    user = simpleAuth.user ? {
      id: simpleAuth.user.id,
      username: simpleAuth.user.username,
      roles: [{ code: simpleAuth.user.role, name: simpleAuth.user.role, permissions: [] }]
    } : null
    isAuthenticated = simpleAuth.isAuthenticated
  } catch {
    // 回退到复杂认证系统
    const complexAuth = useAuth()
    user = complexAuth.user
    isAuthenticated = complexAuth.isAuthenticated
  }

  // 计算用户权限级别
  const userLevel = useMemo(() => {
    if (!isAuthenticated || !user) {
      return null
    }
    return SimplePermissionManager.getUserPermissionLevel(user)
  }, [isAuthenticated, user])

  // 计算是否为管理员
  const isAdmin = useMemo(() => {
    return userLevel === 'admin'
  }, [userLevel])

  // 计算是否为员工
  const isEmployee = useMemo(() => {
    return userLevel === 'employee'
  }, [userLevel])

  // 权限检查函数
  const hasPermission = useMemo(() => {
    return (permission: string): boolean => {
      if (!isAuthenticated || !user) {
        return false
      }
      return SimplePermissionManager.checkPermission(user, permission).hasPermission
    }
  }, [isAuthenticated, user])

  // 角色检查函数
  const hasRole = useMemo(() => {
    return (role: SimplePermissionLevel): boolean => {
      if (!userLevel) {
        return false
      }
      return SimplePermissionManager.comparePermissionLevels(userLevel, role)
    }
  }, [userLevel])

  // 获取可访问功能列表
  const getAccessibleFeatures = useMemo(() => {
    return (): string[] => {
      if (!isAuthenticated || !user) {
        return []
      }
      return SimplePermissionManager.getUserAccessibleFeatures(user)
    }
  }, [isAuthenticated, user])

  // 获取权限级别描述
  const getLevelDescription = useMemo(() => {
    return (): string => {
      if (!userLevel) {
        return '未登录'
      }
      return SimplePermissionManager.getPermissionLevelDescription(userLevel)
    }
  }, [userLevel])

  return {
    userLevel,
    isAdmin,
    isEmployee,
    hasPermission,
    hasRole,
    getAccessibleFeatures,
    getLevelDescription
  }
}

/**
 * 简化的权限检查Hook（只返回检查函数）
 * 
 * 适用于只需要权限检查功能的场景
 * 
 * @returns 权限检查函数
 */
export const usePermissionCheck = () => {
  const { hasPermission } = useSimplePermission()
  return hasPermission
}

/**
 * 简化的角色检查Hook（只返回角色信息）
 * 
 * 适用于只需要角色信息的场景
 * 
 * @returns 角色信息
 */
export const useUserRole = () => {
  const { userLevel, isAdmin, isEmployee, getLevelDescription } = useSimplePermission()
  
  return {
    level: userLevel,
    isAdmin,
    isEmployee,
    description: getLevelDescription()
  }
}

/**
 * 管理员权限检查Hook
 * 
 * 专门用于检查管理员权限的简化Hook
 * 
 * @returns 是否为管理员
 */
export const useAdminCheck = (): boolean => {
  const { isAdmin } = useSimplePermission()
  return isAdmin
}

/**
 * 员工权限检查Hook
 * 
 * 专门用于检查员工权限的简化Hook
 * 
 * @returns 是否为员工（包括管理员）
 */
export const useEmployeeCheck = (): boolean => {
  const { isAdmin, isEmployee } = useSimplePermission()
  return isAdmin || isEmployee
}

/**
 * 功能访问检查Hook
 * 
 * 检查用户是否可以访问指定功能
 * 
 * @param feature 功能名称
 * @returns 是否可以访问
 */
export const useFeatureAccess = (feature: string): boolean => {
  const { getAccessibleFeatures } = useSimplePermission()
  
  return useMemo(() => {
    const accessibleFeatures = getAccessibleFeatures()
    return accessibleFeatures.includes(feature)
  }, [feature, getAccessibleFeatures])
}

/**
 * 批量权限检查Hook
 * 
 * 一次性检查多个权限
 * 
 * @param permissions 权限列表
 * @returns 权限检查结果映射
 */
export const useBatchPermissionCheck = (permissions: string[]): Record<string, boolean> => {
  const { hasPermission } = useSimplePermission()
  
  return useMemo(() => {
    const result: Record<string, boolean> = {}
    permissions.forEach(permission => {
      result[permission] = hasPermission(permission)
    })
    return result
  }, [permissions, hasPermission])
}

/**
 * 权限级别比较Hook
 * 
 * 比较当前用户权限级别与要求的权限级别
 * 
 * @param requiredLevel 要求的权限级别
 * @returns 是否满足权限要求
 */
export const usePermissionLevelCheck = (requiredLevel: SimplePermissionLevel): boolean => {
  const { hasRole } = useSimplePermission()
  return hasRole(requiredLevel)
}

export default useSimplePermission
