/**
 * 简单认证服务
 * 
 * 根据PRD文档要求实现的极简版认证服务
 * 为小企业提供简单易用的认证功能，专注于admin/employee两级权限
 * 
 * 注意：这是现有复杂认证系统的简化接口，底层仍使用DataAccessManager
 */

import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { SECURITY_CONFIG, validatePassword } from '@/config/security'
import { User, LoginRequest, LoginResult } from '@/types/auth'

/**
 * 简化的用户类型（PRD要求）
 */
export interface SimpleUser {
  id: string
  username: string
  role: 'admin' | 'employee'
}

/**
 * 简化的登录结果（PRD要求）
 */
export interface SimpleLoginResult {
  user: SimpleUser
  token: string
}

/**
 * 简单认证服务类
 * 
 * 提供PRD文档中要求的简化认证接口
 * 底层使用现有的完整认证系统，但对外提供简单的API
 */
export class SimpleAuthService {
  
  /**
   * 用户登录
   * 
   * @param username 用户名
   * @param password 密码
   * @returns 登录结果，包含用户信息和token
   */
  static async login(username: string, password: string): Promise<SimpleLoginResult> {
    try {
      console.log('🔐 [SimpleAuthService] 开始处理登录请求:', username)

      // 调用底层认证服务
      const loginRequest: LoginRequest = {
        username,
        password,
        rememberMe: false
      }

      const result = await dataAccessManager.auth.login(loginRequest)

      if (result.status !== 'success' || !result.data) {
        throw new Error(result.message || '用户名或密码错误')
      }

      const { user, sessionId } = result.data

      // 检查用户状态
      if (user.status !== 'active') {
        throw new Error('用户不存在或已被禁用')
      }

      // 生成JWT token
      const accessToken = await dataAccessManager.tokenManagement.generateAccessToken({
        userId: user.id,
        username: user.username,
        roles: user.roles.map(role => role.code),
        permissions: user.roles.flatMap(role => role.permissions.map(perm => perm.code)),
        sessionId: sessionId!
      })

      // 简化用户信息，只返回PRD要求的字段
      const simpleUser: SimpleUser = {
        id: user.id,
        username: user.username,
        role: this.mapUserRole(user.roles)
      }

      console.log('✅ [SimpleAuthService] 登录成功:', simpleUser.username)

      return {
        user: simpleUser,
        token: accessToken
      }

    } catch (error) {
      console.error('❌ [SimpleAuthService] 登录失败:', error)
      throw error
    }
  }

  /**
   * 验证token
   * 
   * @param token JWT token
   * @returns 用户信息，如果token无效则返回null
   */
  static async verifyToken(token: string): Promise<SimpleUser | null> {
    try {
      console.log('🔍 [SimpleAuthService] 开始验证token')

      // 使用底层token管理服务验证
      const tokenValidation = await dataAccessManager.tokenManagement.verifyAccessToken(token)

      if (!tokenValidation.isValid || !tokenValidation.payload) {
        console.log('❌ [SimpleAuthService] Token验证失败')
        return null
      }

      // 获取用户信息
      const userResult = await dataAccessManager.auth.getUserById(tokenValidation.payload.userId)

      if (userResult.status !== 'success' || !userResult.data) {
        console.log('❌ [SimpleAuthService] 用户不存在')
        return null
      }

      const user = userResult.data

      // 检查用户状态
      if (user.status !== 'active') {
        console.log('❌ [SimpleAuthService] 用户已被禁用')
        return null
      }

      // 返回简化的用户信息
      const simpleUser: SimpleUser = {
        id: user.id,
        username: user.username,
        role: this.mapUserRole(user.roles)
      }

      console.log('✅ [SimpleAuthService] Token验证成功:', simpleUser.username)
      return simpleUser

    } catch (error) {
      console.error('❌ [SimpleAuthService] Token验证异常:', error)
      return null
    }
  }

  /**
   * 权限检查
   * 
   * @param userRole 用户角色
   * @param requiredRole 需要的角色
   * @returns 是否有权限
   */
  static hasPermission(userRole: string, requiredRole: 'admin' | 'employee'): boolean {
    console.log('🔍 [SimpleAuthService] 检查权限:', { userRole, requiredRole })

    // PRD要求的简单权限逻辑
    if (requiredRole === 'admin') {
      return userRole === 'admin'
    }
    
    // employee权限：admin和employee都可以访问
    return ['admin', 'employee'].includes(userRole)
  }

  /**
   * 将复杂的角色映射为简单的admin/employee角色
   *
   * 支持多种角色代码的识别，确保与现有RBAC系统的兼容性
   *
   * @param roles 用户角色列表
   * @returns 简化的角色
   */
  private static mapUserRole(roles: User['roles']): 'admin' | 'employee' {
    console.log('🔍 [SimpleAuthService] 开始角色映射:', roles.map(r => ({ code: r.code, name: r.name })))

    // 检查是否为空角色列表
    if (!roles || roles.length === 0) {
      console.log('⚠️ [SimpleAuthService] 用户无角色，默认为employee')
      return 'employee'
    }

    // 管理员角色代码列表（支持多种命名方式）
    const adminRoleCodes = [
      'admin',
      'system_admin',
      'administrator',
      'super_admin',
      'root',
      'manager'
    ]

    // 管理员角色名称关键词（支持中英文）
    const adminRoleNameKeywords = [
      '管理员',
      '系统管理员',
      '超级管理员',
      'admin',
      'administrator',
      'manager',
      'supervisor'
    ]

    // 检查角色代码
    const hasAdminRoleByCode = roles.some(role =>
      adminRoleCodes.includes(role.code.toLowerCase())
    )

    // 检查角色名称
    const hasAdminRoleByName = roles.some(role =>
      adminRoleNameKeywords.some(keyword =>
        role.name.toLowerCase().includes(keyword.toLowerCase())
      )
    )

    // 检查权限（如果角色包含管理权限，也视为管理员）
    const hasAdminPermissions = roles.some(role =>
      role.permissions && role.permissions.some(perm =>
        perm.code.includes('admin') ||
        perm.code.includes('system') ||
        perm.code.includes('manage')
      )
    )

    const isAdmin = hasAdminRoleByCode || hasAdminRoleByName || hasAdminPermissions

    console.log('🔍 [SimpleAuthService] 角色映射结果:', {
      hasAdminRoleByCode,
      hasAdminRoleByName,
      hasAdminPermissions,
      finalRole: isAdmin ? 'admin' : 'employee'
    })

    return isAdmin ? 'admin' : 'employee'
  }

  /**
   * 验证密码强度（根据PRD的简化要求）
   * 
   * @param password 密码
   * @returns 验证结果
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean
    errors: string[]
  } {
    return validatePassword(password)
  }

  /**
   * 获取安全配置信息
   * 
   * @returns 安全配置
   */
  static getSecurityConfig() {
    return {
      passwordPolicy: SECURITY_CONFIG.password,
      sessionDuration: SECURITY_CONFIG.session.duration,
      maxLoginAttempts: SECURITY_CONFIG.session.maxLoginAttempts,
      lockoutDuration: SECURITY_CONFIG.session.lockoutDuration
    }
  }

  /**
   * 检查用户是否为管理员
   * 
   * @param user 用户信息
   * @returns 是否为管理员
   */
  static isAdmin(user: SimpleUser): boolean {
    return user.role === 'admin'
  }

  /**
   * 检查用户是否为员工
   *
   * @param user 用户信息
   * @returns 是否为员工
   */
  static isEmployee(user: SimpleUser): boolean {
    return user.role === 'employee'
  }

  /**
   * 获取角色的中文显示名称
   *
   * @param role 角色代码
   * @returns 中文显示名称
   */
  static getRoleDisplayName(role: 'admin' | 'employee'): string {
    const roleNames = {
      admin: '管理员',
      employee: '员工'
    }
    return roleNames[role]
  }

  /**
   * 获取用户的权限级别描述
   *
   * @param user 用户信息
   * @returns 权限级别描述
   */
  static getUserPermissionDescription(user: SimpleUser): string {
    if (user.role === 'admin') {
      return '拥有系统所有权限，可以管理用户、配置系统和访问所有功能'
    } else {
      return '可以访问业务功能，包括数据查看、编辑和基本操作'
    }
  }

  /**
   * 验证角色转换的正确性
   *
   * @param originalRoles 原始角色列表
   * @param mappedRole 映射后的角色
   * @returns 验证结果
   */
  static validateRoleMapping(originalRoles: User['roles'], mappedRole: 'admin' | 'employee'): {
    isValid: boolean
    warnings: string[]
  } {
    const warnings: string[] = []

    // 检查是否有角色丢失
    if (!originalRoles || originalRoles.length === 0) {
      warnings.push('用户没有分配任何角色')
    }

    // 检查管理员权限是否正确映射
    const hasHighLevelPermissions = originalRoles.some(role =>
      role.permissions && role.permissions.some(perm =>
        perm.code.includes('delete') ||
        perm.code.includes('admin') ||
        perm.code.includes('system')
      )
    )

    if (hasHighLevelPermissions && mappedRole === 'employee') {
      warnings.push('用户拥有高级权限但被映射为员工角色，可能存在权限降级')
    }

    // 检查是否有未识别的角色
    const recognizedRoles = ['admin', 'system_admin', 'administrator', 'user', 'employee', 'manager']
    const unrecognizedRoles = originalRoles.filter(role =>
      !recognizedRoles.includes(role.code.toLowerCase())
    )

    if (unrecognizedRoles.length > 0) {
      warnings.push(`存在未识别的角色: ${unrecognizedRoles.map(r => r.code).join(', ')}`)
    }

    return {
      isValid: warnings.length === 0,
      warnings
    }
  }

  /**
   * 创建简化用户对象
   *
   * @param fullUser 完整用户对象
   * @returns 简化用户对象
   */
  static createSimpleUser(fullUser: User): SimpleUser {
    return {
      id: fullUser.id,
      username: fullUser.username,
      role: this.mapUserRole(fullUser.roles)
    }
  }
}

/**
 * 导出简化的认证服务实例
 * 提供与PRD文档完全一致的API接口
 */
export default SimpleAuthService
