/**
 * 认证和权限管理系统集成测试
 * 
 * 测试PRD要求的完整认证流程
 * 包括登录、权限验证、页面访问控制等端到端功能
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { message } from 'antd'
import { SimpleAuthProvider, useSimpleAuth } from '@/contexts/SimpleAuthContext'
import { SimpleAuthService } from '@/services/auth/SimpleAuthService'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { AdminOnly, EmployeeAndAbove } from '@/components/auth/SimplePermissionComponents'

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn()
}))

jest.mock('antd', () => ({
  message: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

jest.mock('@/services/auth/SimpleAuthService', () => ({
  SimpleAuthService: {
    login: jest.fn(),
    verifyToken: jest.fn(),
    hasPermission: jest.fn()
  }
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 完整的应用组件，模拟真实使用场景
const TestApp: React.FC = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    isAdmin,
    isEmployee
  } = useSimpleAuth()

  if (isLoading) {
    return <div data-testid="loading">Loading...</div>
  }

  if (!isAuthenticated) {
    return (
      <div data-testid="login-page">
        <h1>登录页面</h1>
        <button 
          data-testid="admin-login-btn"
          onClick={() => login('admin', 'admin123')}
        >
          管理员登录
        </button>
        <button 
          data-testid="employee-login-btn"
          onClick={() => login('employee', 'employee123')}
        >
          员工登录
        </button>
      </div>
    )
  }

  return (
    <div data-testid="main-app">
      <header data-testid="app-header">
        <span data-testid="user-info">
          欢迎, {user?.username} ({user?.role})
        </span>
        <button data-testid="logout-btn" onClick={logout}>
          退出
        </button>
      </header>

      <nav data-testid="navigation">
        <div data-testid="nav-dashboard">仪表盘</div>
        
        <EmployeeAndAbove>
          <div data-testid="nav-orders">订单管理</div>
          <div data-testid="nav-production">生产管理</div>
        </EmployeeAndAbove>

        <AdminOnly>
          <div data-testid="nav-users">用户管理</div>
          <div data-testid="nav-system">系统设置</div>
        </AdminOnly>
      </nav>

      <main data-testid="main-content">
        {/* 仪表盘 - 所有用户都可以访问 */}
        <div data-testid="dashboard-content">
          <h2>仪表盘</h2>
          <p>欢迎使用ERP系统</p>
        </div>

        {/* 员工功能区域 */}
        <EmployeeAndAbove>
          <div data-testid="employee-features">
            <h3>员工功能</h3>
            <div data-testid="orders-section">订单管理</div>
            <div data-testid="production-section">生产管理</div>
          </div>
        </EmployeeAndAbove>

        {/* 管理员功能区域 */}
        <AdminOnly>
          <div data-testid="admin-features">
            <h3>管理员功能</h3>
            <div data-testid="users-section">用户管理</div>
            <div data-testid="system-section">系统设置</div>
          </div>
        </AdminOnly>

        {/* 受保护的页面组件 */}
        <ProtectedRoute requiredPermission="admin:users:create">
          <div data-testid="protected-admin-content">
            管理员专用内容
          </div>
        </ProtectedRoute>

        <ProtectedRoute requiredPermission="data:view">
          <div data-testid="protected-employee-content">
            员工可访问内容
          </div>
        </ProtectedRoute>
      </main>
    </div>
  )
}

describe('认证和权限管理系统集成测试', () => {
  const mockPush = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    })
    
    require('next/navigation').usePathname.mockReturnValue('/dashboard')
    
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
  })

  describe('完整的管理员登录流程', () => {
    it('应该完成管理员登录并显示所有功能', async () => {
      const mockAdminUser = {
        id: 'admin-1',
        username: 'admin',
        role: 'admin' as const
      }

      const mockLoginResult = {
        user: mockAdminUser,
        token: 'admin-token'
      }

      ;(SimpleAuthService.login as jest.Mock).mockResolvedValue(mockLoginResult)
      ;(SimpleAuthService.hasPermission as jest.Mock).mockImplementation((role, permission) => {
        return role === 'admin' // 管理员拥有所有权限
      })

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      // 初始状态：显示登录页面
      await waitFor(() => {
        expect(screen.getByTestId('login-page')).toBeInTheDocument()
      })

      // 点击管理员登录
      fireEvent.click(screen.getByTestId('admin-login-btn'))

      // 等待登录完成
      await waitFor(() => {
        expect(screen.getByTestId('main-app')).toBeInTheDocument()
      })

      // 验证用户信息显示
      expect(screen.getByTestId('user-info')).toHaveTextContent('欢迎, admin (admin)')

      // 验证导航菜单 - 管理员应该看到所有菜单
      expect(screen.getByTestId('nav-dashboard')).toBeInTheDocument()
      expect(screen.getByTestId('nav-orders')).toBeInTheDocument()
      expect(screen.getByTestId('nav-production')).toBeInTheDocument()
      expect(screen.getByTestId('nav-users')).toBeInTheDocument()
      expect(screen.getByTestId('nav-system')).toBeInTheDocument()

      // 验证功能区域 - 管理员应该看到所有功能
      expect(screen.getByTestId('employee-features')).toBeInTheDocument()
      expect(screen.getByTestId('admin-features')).toBeInTheDocument()

      // 验证受保护内容 - 管理员应该看到所有受保护内容
      expect(screen.getByTestId('protected-admin-content')).toBeInTheDocument()
      expect(screen.getByTestId('protected-employee-content')).toBeInTheDocument()

      // 验证登录调用
      expect(SimpleAuthService.login).toHaveBeenCalledWith('admin', 'admin123')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'admin-token')
      expect(message.success).toHaveBeenCalledWith('登录成功')
    })
  })

  describe('完整的员工登录流程', () => {
    it('应该完成员工登录并只显示员工功能', async () => {
      const mockEmployeeUser = {
        id: 'employee-1',
        username: 'employee',
        role: 'employee' as const
      }

      const mockLoginResult = {
        user: mockEmployeeUser,
        token: 'employee-token'
      }

      ;(SimpleAuthService.login as jest.Mock).mockResolvedValue(mockLoginResult)
      ;(SimpleAuthService.hasPermission as jest.Mock).mockImplementation((role, permission) => {
        // 员工只有基础权限
        if (role === 'employee') {
          return !permission.startsWith('admin:')
        }
        return false
      })

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      // 初始状态：显示登录页面
      await waitFor(() => {
        expect(screen.getByTestId('login-page')).toBeInTheDocument()
      })

      // 点击员工登录
      fireEvent.click(screen.getByTestId('employee-login-btn'))

      // 等待登录完成
      await waitFor(() => {
        expect(screen.getByTestId('main-app')).toBeInTheDocument()
      })

      // 验证用户信息显示
      expect(screen.getByTestId('user-info')).toHaveTextContent('欢迎, employee (employee)')

      // 验证导航菜单 - 员工应该看到基础菜单
      expect(screen.getByTestId('nav-dashboard')).toBeInTheDocument()
      expect(screen.getByTestId('nav-orders')).toBeInTheDocument()
      expect(screen.getByTestId('nav-production')).toBeInTheDocument()

      // 员工不应该看到管理员菜单
      expect(screen.queryByTestId('nav-users')).not.toBeInTheDocument()
      expect(screen.queryByTestId('nav-system')).not.toBeInTheDocument()

      // 验证功能区域 - 员工应该看到员工功能，但不应该看到管理员功能
      expect(screen.getByTestId('employee-features')).toBeInTheDocument()
      expect(screen.queryByTestId('admin-features')).not.toBeInTheDocument()

      // 验证受保护内容 - 员工应该看到员工内容，但不应该看到管理员内容
      expect(screen.queryByTestId('protected-admin-content')).not.toBeInTheDocument()
      expect(screen.getByTestId('protected-employee-content')).toBeInTheDocument()
    })
  })

  describe('登出流程', () => {
    it('应该正确处理登出并返回登录页面', async () => {
      const mockUser = {
        id: 'user-1',
        username: 'testuser',
        role: 'admin' as const
      }

      // 先设置已登录状态
      localStorageMock.getItem.mockReturnValue('valid-token')
      ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      // 等待登录状态加载
      await waitFor(() => {
        expect(screen.getByTestId('main-app')).toBeInTheDocument()
      })

      // 点击登出按钮
      fireEvent.click(screen.getByTestId('logout-btn'))

      // 等待登出完成
      await waitFor(() => {
        expect(screen.getByTestId('login-page')).toBeInTheDocument()
      })

      // 验证登出操作
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(message.success).toHaveBeenCalledWith('已安全退出')
      expect(mockPush).toHaveBeenCalledWith('/login')
    })
  })

  describe('自动登录流程', () => {
    it('应该在有有效token时自动登录', async () => {
      const mockUser = {
        id: 'user-1',
        username: 'autouser',
        role: 'employee' as const
      }

      localStorageMock.getItem.mockReturnValue('valid-token')
      ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      // 应该自动登录并显示主应用
      await waitFor(() => {
        expect(screen.getByTestId('main-app')).toBeInTheDocument()
      })

      expect(screen.getByTestId('user-info')).toHaveTextContent('欢迎, autouser (employee)')
      expect(SimpleAuthService.verifyToken).toHaveBeenCalledWith('valid-token')
    })

    it('应该在token无效时跳转到登录页', async () => {
      localStorageMock.getItem.mockReturnValue('invalid-token')
      ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(null)

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/login')
      })

      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
    })
  })

  describe('权限边界测试', () => {
    it('应该正确处理权限边界情况', async () => {
      const mockUser = {
        id: 'user-1',
        username: 'testuser',
        role: 'employee' as const
      }

      localStorageMock.getItem.mockReturnValue('valid-token')
      ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)
      
      // 模拟员工权限：有基础权限，无管理员权限
      ;(SimpleAuthService.hasPermission as jest.Mock).mockImplementation((role, permission) => {
        if (role === 'employee') {
          return ['data:view', 'data:edit', 'orders:create'].includes(permission)
        }
        return false
      })

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('main-app')).toBeInTheDocument()
      })

      // 员工应该能看到员工功能
      expect(screen.getByTestId('employee-features')).toBeInTheDocument()
      expect(screen.getByTestId('protected-employee-content')).toBeInTheDocument()

      // 员工不应该能看到管理员功能
      expect(screen.queryByTestId('admin-features')).not.toBeInTheDocument()
      expect(screen.queryByTestId('protected-admin-content')).not.toBeInTheDocument()
    })
  })

  describe('错误处理', () => {
    it('应该正确处理登录失败', async () => {
      const loginError = new Error('用户名或密码错误')
      ;(SimpleAuthService.login as jest.Mock).mockRejectedValue(loginError)

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('login-page')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByTestId('admin-login-btn'))

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('用户名或密码错误')
      })

      // 应该仍然在登录页面
      expect(screen.getByTestId('login-page')).toBeInTheDocument()
    })

    it('应该正确处理网络错误', async () => {
      const networkError = new Error('Network Error')
      ;(SimpleAuthService.login as jest.Mock).mockRejectedValue(networkError)

      render(
        <SimpleAuthProvider>
          <TestApp />
        </SimpleAuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('login-page')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByTestId('employee-login-btn'))

      await waitFor(() => {
        expect(message.error).toHaveBeenCalledWith('Network Error')
      })
    })
  })
})
