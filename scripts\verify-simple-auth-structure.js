#!/usr/bin/env node

/**
 * 数据库结构验证脚本
 * 
 * 验证数据库结构是否符合PRD文档《03-用户认证和权限管理系统.md》的要求
 * 确保极简版认证系统的数据结构正确
 */

const mysql = require('mysql2/promise')
const path = require('path')

// 数据库连接配置
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'erp_system',
  charset: 'utf8mb4'
}

/**
 * 验证数据库结构
 */
async function verifyDatabaseStructure() {
  let connection = null
  
  try {
    console.log('🔍 开始验证数据库结构...')
    console.log(`📊 连接数据库: ${DB_CONFIG.host}/${DB_CONFIG.database}`)
    
    // 连接数据库
    connection = await mysql.createConnection(DB_CONFIG)
    
    // 验证必需的表
    await verifyRequiredTables(connection)
    
    // 验证用户表结构
    await verifyUsersTable(connection)
    
    // 验证角色和权限表
    await verifyRolesAndPermissions(connection)
    
    // 验证基础数据
    await verifyBasicData(connection)
    
    console.log('✅ 数据库结构验证完成！')
    console.log('🎉 符合PRD文档要求的极简版认证系统结构')
    
  } catch (error) {
    console.error('❌ 数据库结构验证失败:', error.message)
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

/**
 * 验证必需的表是否存在
 */
async function verifyRequiredTables(connection) {
  console.log('📋 验证必需的表...')
  
  const requiredTables = [
    'users',
    'user_passwords', 
    'roles',
    'permissions',
    'role_permissions',
    'user_roles'
  ]
  
  const [tables] = await connection.execute('SHOW TABLES')
  const existingTables = tables.map(row => Object.values(row)[0])
  
  for (const table of requiredTables) {
    if (existingTables.includes(table)) {
      console.log(`  ✅ ${table} 表存在`)
    } else {
      throw new Error(`缺少必需的表: ${table}`)
    }
  }
}

/**
 * 验证用户表结构
 */
async function verifyUsersTable(connection) {
  console.log('👤 验证用户表结构...')
  
  const [columns] = await connection.execute('DESCRIBE users')
  const columnNames = columns.map(col => col.Field)
  
  const requiredColumns = [
    'id',
    'username', 
    'email',
    'full_name',
    'status',
    'created_at',
    'updated_at'
  ]
  
  for (const column of requiredColumns) {
    if (columnNames.includes(column)) {
      console.log(`  ✅ users.${column} 字段存在`)
    } else {
      throw new Error(`users表缺少必需字段: ${column}`)
    }
  }
  
  // 验证状态枚举值
  const statusColumn = columns.find(col => col.Field === 'status')
  if (statusColumn && statusColumn.Type.includes('active') && statusColumn.Type.includes('inactive')) {
    console.log('  ✅ status字段枚举值正确')
  } else {
    console.warn('  ⚠️ status字段枚举值可能不完整')
  }
}

/**
 * 验证角色和权限表
 */
async function verifyRolesAndPermissions(connection) {
  console.log('🔐 验证角色和权限表...')
  
  // 验证角色表
  const [roleColumns] = await connection.execute('DESCRIBE roles')
  const roleColumnNames = roleColumns.map(col => col.Field)
  
  const requiredRoleColumns = ['id', 'code', 'name', 'description']
  for (const column of requiredRoleColumns) {
    if (roleColumnNames.includes(column)) {
      console.log(`  ✅ roles.${column} 字段存在`)
    } else {
      throw new Error(`roles表缺少必需字段: ${column}`)
    }
  }
  
  // 验证权限表
  const [permColumns] = await connection.execute('DESCRIBE permissions')
  const permColumnNames = permColumns.map(col => col.Field)
  
  const requiredPermColumns = ['id', 'code', 'name', 'type', 'resource', 'action']
  for (const column of requiredPermColumns) {
    if (permColumnNames.includes(column)) {
      console.log(`  ✅ permissions.${column} 字段存在`)
    } else {
      throw new Error(`permissions表缺少必需字段: ${column}`)
    }
  }
}

/**
 * 验证基础数据
 */
async function verifyBasicData(connection) {
  console.log('📊 验证基础数据...')
  
  // 验证基础角色
  const [roles] = await connection.execute('SELECT code FROM roles WHERE code IN (?, ?)', ['admin', 'employee'])
  
  if (roles.length >= 2) {
    console.log('  ✅ admin和employee角色存在')
  } else {
    console.warn('  ⚠️ 缺少基础角色，请运行初始化脚本')
  }
  
  // 验证默认管理员用户
  const [adminUsers] = await connection.execute('SELECT username FROM users WHERE username = ?', ['admin'])
  
  if (adminUsers.length > 0) {
    console.log('  ✅ 默认管理员用户存在')
  } else {
    console.warn('  ⚠️ 缺少默认管理员用户，请运行初始化脚本')
  }
  
  // 验证用户角色关联
  const [userRoles] = await connection.execute(`
    SELECT COUNT(*) as count 
    FROM user_roles ur 
    JOIN users u ON ur.user_id = u.id 
    JOIN roles r ON ur.role_id = r.id 
    WHERE u.username = 'admin' AND r.code = 'admin'
  `)
  
  if (userRoles[0].count > 0) {
    console.log('  ✅ 管理员用户角色关联正确')
  } else {
    console.warn('  ⚠️ 管理员用户角色关联缺失')
  }
}

/**
 * 生成数据库结构报告
 */
async function generateStructureReport(connection) {
  console.log('\n📋 数据库结构报告:')
  console.log('=' .repeat(50))
  
  // 统计用户数量
  const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users')
  console.log(`👤 用户总数: ${userCount[0].count}`)
  
  // 统计角色数量
  const [roleCount] = await connection.execute('SELECT COUNT(*) as count FROM roles')
  console.log(`🔐 角色总数: ${roleCount[0].count}`)
  
  // 统计权限数量
  const [permCount] = await connection.execute('SELECT COUNT(*) as count FROM permissions')
  console.log(`🛡️ 权限总数: ${permCount[0].count}`)
  
  // 活跃用户数量
  const [activeUsers] = await connection.execute('SELECT COUNT(*) as count FROM users WHERE status = "active"')
  console.log(`✅ 活跃用户: ${activeUsers[0].count}`)
  
  console.log('=' .repeat(50))
}

// 主函数
async function main() {
  console.log('🚀 ERP系统数据库结构验证工具')
  console.log('📋 验证PRD文档要求的极简版认证系统结构')
  console.log('')
  
  await verifyDatabaseStructure()
}

// 运行验证
if (require.main === module) {
  main().catch(error => {
    console.error('💥 验证过程发生错误:', error)
    process.exit(1)
  })
}

module.exports = {
  verifyDatabaseStructure,
  DB_CONFIG
}
