/**
 * 简化权限控制组件集合
 * 
 * 根据PRD要求，提供简单易用的权限控制组件
 * 专为admin/employee两级权限设计
 */

import React from 'react'
import { Alert, Tag, Space } from 'antd'
import { LockOutlined, CrownOutlined, UserOutlined } from '@ant-design/icons'
import { useSimplePermission, useAdminCheck, useEmployeeCheck } from '@/hooks/useSimplePermission'

/**
 * 仅管理员可见组件
 */
export const AdminOnly: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ children, fallback = null }) => {
  const isAdmin = useAdminCheck()
  
  if (!isAdmin) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

/**
 * 员工及以上可见组件（包括管理员）
 */
export const EmployeeAndAbove: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ children, fallback = null }) => {
  const isEmployee = useEmployeeCheck()
  
  if (!isEmployee) {
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

/**
 * 权限级别显示组件
 */
export const PermissionLevelDisplay: React.FC<{
  showIcon?: boolean
  showDescription?: boolean
  size?: 'small' | 'default' | 'large'
}> = ({ 
  showIcon = true, 
  showDescription = false,
  size = 'default'
}) => {
  const { userLevel, isAdmin, getLevelDescription } = useSimplePermission()
  
  if (!userLevel) {
    return (
      <Tag color="gray" size={size}>
        {showIcon && <LockOutlined />}
        未登录
      </Tag>
    )
  }
  
  return (
    <Space>
      <Tag 
        color={isAdmin ? 'red' : 'blue'} 
        size={size}
        icon={showIcon ? (isAdmin ? <CrownOutlined /> : <UserOutlined />) : undefined}
      >
        {getLevelDescription()}
      </Tag>
      {showDescription && (
        <span className="text-sm text-gray-500">
          {isAdmin ? '拥有所有权限' : '基础操作权限'}
        </span>
      )}
    </Space>
  )
}

/**
 * 权限不足提示组件
 */
export const PermissionDenied: React.FC<{
  requiredLevel?: 'admin' | 'employee'
  message?: string
  showUpgrade?: boolean
}> = ({ 
  requiredLevel = 'employee',
  message,
  showUpgrade = false
}) => {
  const { userLevel, getLevelDescription } = useSimplePermission()
  
  const defaultMessage = message || `此功能需要${requiredLevel === 'admin' ? '管理员' : '员工'}权限`
  
  return (
    <Alert
      message="权限不足"
      description={
        <div className="space-y-2">
          <p>{defaultMessage}</p>
          <div className="flex items-center justify-between text-sm">
            <span>当前权限: {userLevel ? getLevelDescription() : '未登录'}</span>
            <span>需要权限: {requiredLevel === 'admin' ? '系统管理员' : '普通员工'}</span>
          </div>
          {showUpgrade && (
            <p className="text-xs text-gray-500">
              请联系系统管理员升级您的账户权限
            </p>
          )}
        </div>
      }
      type="warning"
      showIcon
      icon={<LockOutlined />}
    />
  )
}

/**
 * 简化的功能权限包装器
 */
export const FeatureWrapper: React.FC<{
  feature: string
  children: React.ReactNode
  fallback?: React.ReactNode
  showDenied?: boolean
}> = ({ 
  feature, 
  children, 
  fallback,
  showDenied = false
}) => {
  const { hasPermission } = useSimplePermission()
  
  if (!hasPermission(feature)) {
    if (showDenied) {
      return <PermissionDenied message={`此功能需要 ${feature} 权限`} />
    }
    return <>{fallback}</>
  }
  
  return <>{children}</>
}

/**
 * 角色标识组件
 */
export const RoleBadge: React.FC<{
  role?: 'admin' | 'employee'
  size?: 'small' | 'default' | 'large'
  showIcon?: boolean
}> = ({ 
  role,
  size = 'default',
  showIcon = true
}) => {
  const { userLevel } = useSimplePermission()
  const displayRole = role || userLevel
  
  if (!displayRole) {
    return null
  }
  
  const config = {
    admin: {
      color: 'red' as const,
      icon: <CrownOutlined />,
      text: '管理员'
    },
    employee: {
      color: 'blue' as const,
      icon: <UserOutlined />,
      text: '员工'
    }
  }
  
  const roleConfig = config[displayRole]
  
  return (
    <Tag 
      color={roleConfig.color} 
      size={size}
      icon={showIcon ? roleConfig.icon : undefined}
    >
      {roleConfig.text}
    </Tag>
  )
}

/**
 * 权限状态指示器
 */
export const PermissionIndicator: React.FC<{
  permission: string
  showText?: boolean
}> = ({ permission, showText = true }) => {
  const { hasPermission } = useSimplePermission()
  const hasAccess = hasPermission(permission)
  
  return (
    <Space>
      <div 
        className={`w-2 h-2 rounded-full ${
          hasAccess ? 'bg-green-500' : 'bg-red-500'
        }`}
      />
      {showText && (
        <span className={`text-sm ${
          hasAccess ? 'text-green-600' : 'text-red-600'
        }`}>
          {hasAccess ? '有权限' : '无权限'}
        </span>
      )}
    </Space>
  )
}

/**
 * 简化的权限检查按钮
 */
export const PermissionButton: React.FC<{
  permission: string
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  className?: string
}> = ({ 
  permission, 
  children, 
  onClick, 
  disabled = false,
  className = ''
}) => {
  const { hasPermission } = useSimplePermission()
  const hasAccess = hasPermission(permission)
  
  return (
    <button
      onClick={hasAccess ? onClick : undefined}
      disabled={disabled || !hasAccess}
      className={`
        ${hasAccess 
          ? 'bg-blue-600 hover:bg-blue-700 text-white' 
          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        }
        px-4 py-2 rounded transition-colors
        ${className}
      `}
      title={hasAccess ? undefined : `需要 ${permission} 权限`}
    >
      {children}
    </button>
  )
}

/**
 * 权限级别进度条
 */
export const PermissionLevelBar: React.FC = () => {
  const { userLevel, isAdmin } = useSimplePermission()
  
  if (!userLevel) {
    return null
  }
  
  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-gray-500">权限级别:</span>
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all ${
            isAdmin ? 'bg-red-500 w-full' : 'bg-blue-500 w-1/2'
          }`}
        />
      </div>
      <RoleBadge size="small" />
    </div>
  )
}

export {
  AdminOnly,
  EmployeeAndAbove,
  PermissionLevelDisplay,
  PermissionDenied,
  FeatureWrapper,
  RoleBadge,
  PermissionIndicator,
  PermissionButton,
  PermissionLevelBar
}
