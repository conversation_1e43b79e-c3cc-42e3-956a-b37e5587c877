/**
 * 认证架构合规性测试
 * 
 * 确保认证系统遵循项目架构规范
 * 防止架构违规和重复代码问题
 */

import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { TokenValidator } from '@/services/auth/TokenValidator'
import { AuthErrorHandler, AuthErrorCode } from '@/services/auth/AuthErrorHandler'
import { JWT_CONFIG } from '@/config/jwt.config'
import fs from 'fs'
import path from 'path'

describe('认证架构合规性测试', () => {
  describe('1. 架构一致性验证', () => {
    test('应该只有一个Token管理器实现', () => {
      const authDir = path.join(process.cwd(), 'src/utils/auth')
      
      if (fs.existsSync(authDir)) {
        const files = fs.readdirSync(authDir)
        const tokenManagerFiles = files.filter(file => 
          file.includes('TokenManager') && file.endsWith('.ts')
        )
        
        // 应该只有TokenManager.ts，不应该有SimpleTokenManager.ts或ServerTokenManager.ts
        expect(tokenManagerFiles).toEqual(['TokenManager.ts'])
      }
    })

    test('应该有统一的JWT配置', () => {
      expect(JWT_CONFIG).toBeDefined()
      expect(JWT_CONFIG.accessToken).toBeDefined()
      expect(JWT_CONFIG.refreshToken).toBeDefined()
      expect(JWT_CONFIG.accessToken.secret).toBeDefined()
      expect(JWT_CONFIG.refreshToken.secret).toBeDefined()
    })

    test('DataAccessManager应该有tokenManagement访问器', () => {
      expect(dataAccessManager.tokenManagement).toBeDefined()
      expect(typeof dataAccessManager.tokenManagement.generateAccessToken).toBe('function')
      expect(typeof dataAccessManager.tokenManagement.verifyAccessToken).toBe('function')
      expect(typeof dataAccessManager.tokenManagement.generateRefreshToken).toBe('function')
      expect(typeof dataAccessManager.tokenManagement.verifyRefreshToken).toBe('function')
    })
  })

  describe('2. 代码重复检查', () => {
    test('不应该存在重复的Token管理器类', () => {
      const srcDir = path.join(process.cwd(), 'src')
      const duplicateFiles = [
        'src/utils/auth/SimpleTokenManager.ts',
        'src/utils/auth/ServerTokenManager.ts'
      ]
      
      duplicateFiles.forEach(filePath => {
        const fullPath = path.join(process.cwd(), filePath)
        expect(fs.existsSync(fullPath)).toBe(false)
      })
    })

    test('不应该有重复的JWT配置定义', async () => {
      const srcDir = path.join(process.cwd(), 'src')
      const jwtConfigPattern = /const\s+JWT_CONFIG.*=.*{/g
      
      let configCount = 0
      
      function scanDirectory(dir: string) {
        const files = fs.readdirSync(dir)
        
        files.forEach(file => {
          const filePath = path.join(dir, file)
          const stat = fs.statSync(filePath)
          
          if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
            scanDirectory(filePath)
          } else if (file.endsWith('.ts') && !file.endsWith('.test.ts')) {
            const content = fs.readFileSync(filePath, 'utf-8')
            const matches = content.match(jwtConfigPattern)
            if (matches) {
              configCount += matches.length
            }
          }
        })
      }
      
      scanDirectory(srcDir)
      
      // 应该只有一个JWT配置定义（在jwt.config.ts中）
      expect(configCount).toBeLessThanOrEqual(1)
    })
  })

  describe('3. API路由架构合规性', () => {
    test('API路由不应该直接导入Token管理器', async () => {
      const apiDir = path.join(process.cwd(), 'src/app/api')
      
      if (!fs.existsSync(apiDir)) {
        return // 如果API目录不存在，跳过测试
      }
      
      const violations: string[] = []
      
      function scanApiFiles(dir: string) {
        const files = fs.readdirSync(dir)
        
        files.forEach(file => {
          const filePath = path.join(dir, file)
          const stat = fs.statSync(filePath)
          
          if (stat.isDirectory()) {
            scanApiFiles(filePath)
          } else if (file.endsWith('.ts')) {
            const content = fs.readFileSync(filePath, 'utf-8')
            
            // 检查是否直接导入Token管理器
            const directImports = [
              /import.*SimpleTokenManager/,
              /import.*ServerTokenManager/,
              /import.*TokenManager.*from.*utils\/auth/
            ]
            
            directImports.forEach(pattern => {
              if (pattern.test(content)) {
                violations.push(`${filePath}: 直接导入Token管理器`)
              }
            })
            
            // 检查是否使用dataAccessManager.tokenManagement
            if (content.includes('TokenManager.') && !content.includes('dataAccessManager.tokenManagement')) {
              violations.push(`${filePath}: 未使用dataAccessManager.tokenManagement`)
            }
          }
        })
      }
      
      scanApiFiles(apiDir)
      
      expect(violations).toEqual([])
    })
  })

  describe('4. 错误处理一致性', () => {
    test('AuthErrorHandler应该提供所有必要的错误类型', () => {
      expect(AuthErrorCode.TOKEN_NOT_FOUND).toBeDefined()
      expect(AuthErrorCode.TOKEN_INVALID).toBeDefined()
      expect(AuthErrorCode.TOKEN_EXPIRED).toBeDefined()
      expect(AuthErrorCode.INVALID_CREDENTIALS).toBeDefined()
      expect(AuthErrorCode.UNAUTHORIZED).toBeDefined()
      expect(AuthErrorCode.FORBIDDEN).toBeDefined()
    })

    test('AuthErrorHandler应该能创建标准错误响应', () => {
      const errorResponse = AuthErrorHandler.createErrorResponse(AuthErrorCode.TOKEN_INVALID)
      
      expect(errorResponse.status).toBe('error')
      expect(errorResponse.data).toBe(null)
      expect(errorResponse.code).toBe(AuthErrorCode.TOKEN_INVALID)
      expect(errorResponse.message).toBeDefined()
      expect(errorResponse.timestamp).toBeDefined()
      expect(errorResponse.requestId).toBeDefined()
    })
  })

  describe('5. Token验证服务', () => {
    test('TokenValidator应该提供统一的验证方法', () => {
      expect(typeof TokenValidator.parseJWTToken).toBe('function')
      expect(typeof TokenValidator.checkPagePermission).toBe('function')
      expect(typeof TokenValidator.isTokenNearExpiry).toBe('function')
      expect(typeof TokenValidator.generateRequestId).toBe('function')
    })

    test('TokenValidator应该正确解析有效的JWT格式', () => {
      // 创建一个模拟的JWT Token（仅用于格式测试）
      const mockPayload = { userId: 'test', exp: Math.floor(Date.now() / 1000) + 3600 }
      const mockToken = 'header.' + Buffer.from(JSON.stringify(mockPayload)).toString('base64') + '.signature'
      
      const result = TokenValidator.parseJWTToken(mockToken)
      
      expect(result.valid).toBe(true)
      expect(result.payload).toBeDefined()
      expect(result.payload.userId).toBe('test')
    })

    test('TokenValidator应该拒绝无效的Token格式', () => {
      const invalidTokens = [
        '',
        'invalid',
        'invalid.token',
        'invalid.token.format.extra'
      ]
      
      invalidTokens.forEach(token => {
        const result = TokenValidator.parseJWTToken(token)
        expect(result.valid).toBe(false)
      })
    })
  })

  describe('6. 性能和缓存', () => {
    test('Token操作应该通过DataAccessManager获得缓存支持', async () => {
      // 验证tokenManagement访问器存在
      expect(dataAccessManager.tokenManagement).toBeDefined()
      
      // 验证缓存统计功能可用
      const cacheStats = dataAccessManager.getCacheStatistics()
      expect(cacheStats).toBeDefined()
      expect(typeof cacheStats.enabled).toBe('boolean')
    })

    test('应该有性能监控支持', () => {
      expect(dataAccessManager.performance).toBeDefined()
      expect(typeof dataAccessManager.performance.getMetrics).toBe('function')
    })
  })

  describe('7. 配置安全性', () => {
    test('生产环境不应该使用默认密钥', () => {
      if (process.env.NODE_ENV === 'production') {
        expect(JWT_CONFIG.accessToken.secret).not.toContain('default')
        expect(JWT_CONFIG.refreshToken.secret).not.toContain('default')
      }
    })

    test('JWT配置应该有合理的过期时间', () => {
      expect(JWT_CONFIG.accessToken.expiresIn).toBeDefined()
      expect(JWT_CONFIG.refreshToken.expiresIn).toBeDefined()
      
      // Access Token应该相对较短（通常1小时）
      expect(JWT_CONFIG.accessToken.expiresIn).toMatch(/^[0-9]+[hm]$/)
      
      // Refresh Token应该相对较长（通常几天）
      expect(JWT_CONFIG.refreshToken.expiresIn).toMatch(/^[0-9]+[dh]$/)
    })
  })
})
