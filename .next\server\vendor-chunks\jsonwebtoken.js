/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(ssr)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(ssr)/./node_modules/jws/index.js\");\nmodule.exports = function(jwt, options) {\n    options = options || {};\n    var decoded = jws.decode(jwt, options);\n    if (!decoded) {\n        return null;\n    }\n    var payload = decoded.payload;\n    //try parse the payload\n    if (typeof payload === \"string\") {\n        try {\n            var obj = JSON.parse(payload);\n            if (obj !== null && typeof obj === \"object\") {\n                payload = obj;\n            }\n        } catch (e) {}\n    }\n    //return header if `complete` option is enabled.  header includes claims\n    //such as `kid` and `alg` used to select the key within a JWKS needed to\n    //verify the signature\n    if (options.complete === true) {\n        return {\n            header: decoded.header,\n            payload: payload,\n            signature: decoded.signature\n        };\n    }\n    return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n    decode: __webpack_require__(/*! ./decode */ \"(ssr)/./node_modules/jsonwebtoken/decode.js\"),\n    verify: __webpack_require__(/*! ./verify */ \"(ssr)/./node_modules/jsonwebtoken/verify.js\"),\n    sign: __webpack_require__(/*! ./sign */ \"(ssr)/./node_modules/jsonwebtoken/sign.js\"),\n    JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n    NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n    TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUc7SUFDZkMsUUFBUUMsbUJBQU9BLENBQUM7SUFDaEJDLFFBQVFELG1CQUFPQSxDQUFDO0lBQ2hCRSxNQUFNRixtQkFBT0EsQ0FBQztJQUNkRyxtQkFBbUJILG1CQUFPQSxDQUFDO0lBQzNCSSxnQkFBZ0JKLG1CQUFPQSxDQUFDO0lBQ3hCSyxtQkFBbUJMLG1CQUFPQSxDQUFDO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9pbmRleC5qcz85YmU4Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xuICBkZWNvZGU6IHJlcXVpcmUoJy4vZGVjb2RlJyksXG4gIHZlcmlmeTogcmVxdWlyZSgnLi92ZXJpZnknKSxcbiAgc2lnbjogcmVxdWlyZSgnLi9zaWduJyksXG4gIEpzb25XZWJUb2tlbkVycm9yOiByZXF1aXJlKCcuL2xpYi9Kc29uV2ViVG9rZW5FcnJvcicpLFxuICBOb3RCZWZvcmVFcnJvcjogcmVxdWlyZSgnLi9saWIvTm90QmVmb3JlRXJyb3InKSxcbiAgVG9rZW5FeHBpcmVkRXJyb3I6IHJlcXVpcmUoJy4vbGliL1Rva2VuRXhwaXJlZEVycm9yJyksXG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJkZWNvZGUiLCJyZXF1aXJlIiwidmVyaWZ5Iiwic2lnbiIsIkpzb25XZWJUb2tlbkVycm9yIiwiTm90QmVmb3JlRXJyb3IiLCJUb2tlbkV4cGlyZWRFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function(message, error) {\n    Error.call(this, message);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"JsonWebTokenError\";\n    this.message = message;\n    if (error) this.inner = error;\n};\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcnAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcz8xNmYyIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IGZ1bmN0aW9uIChtZXNzYWdlLCBlcnJvcikge1xuICBFcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICBpZihFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSkge1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG4gIHRoaXMubmFtZSA9ICdKc29uV2ViVG9rZW5FcnJvcic7XG4gIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gIGlmIChlcnJvcikgdGhpcy5pbm5lciA9IGVycm9yO1xufTtcblxuSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShFcnJvci5wcm90b3R5cGUpO1xuSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gSnNvbldlYlRva2VuRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gSnNvbldlYlRva2VuRXJyb3I7XG4iXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJtZXNzYWdlIiwiZXJyb3IiLCJFcnJvciIsImNhbGwiLCJjYXB0dXJlU3RhY2tUcmFjZSIsImNvbnN0cnVjdG9yIiwibmFtZSIsImlubmVyIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJtYXBwaW5ncyI6IkFBQUEsSUFBSUEsb0JBQW9CLFNBQVVDLE9BQU8sRUFBRUMsS0FBSztJQUM5Q0MsTUFBTUMsSUFBSSxDQUFDLElBQUksRUFBRUg7SUFDakIsSUFBR0UsTUFBTUUsaUJBQWlCLEVBQUU7UUFDMUJGLE1BQU1FLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNDLFdBQVc7SUFDaEQ7SUFDQSxJQUFJLENBQUNDLElBQUksR0FBRztJQUNaLElBQUksQ0FBQ04sT0FBTyxHQUFHQTtJQUNmLElBQUlDLE9BQU8sSUFBSSxDQUFDTSxLQUFLLEdBQUdOO0FBQzFCO0FBRUFGLGtCQUFrQlMsU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNSLE1BQU1NLFNBQVM7QUFDM0RULGtCQUFrQlMsU0FBUyxDQUFDSCxXQUFXLEdBQUdOO0FBRTFDWSxPQUFPQyxPQUFPLEdBQUdiIiwiZmlsZSI6Iihzc3IpLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvSnNvbldlYlRva2VuRXJyb3IuanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar NotBeforeError = function(message, date) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"NotBeforeError\";\n    this.date = date;\n};\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\nNotBeforeError.prototype.constructor = NotBeforeError;\nmodule.exports = NotBeforeError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLGlCQUFpQixTQUFVQyxPQUFPLEVBQUVDLElBQUk7SUFDMUNKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLElBQUksR0FBR0E7QUFDZDtBQUVBRixlQUFlSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXBFTCxlQUFlSyxTQUFTLENBQUNHLFdBQVcsR0FBR1I7QUFFdkNTLE9BQU9DLE9BQU8sR0FBR1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcnAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcz84NjY4Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIE5vdEJlZm9yZUVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGRhdGUpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ05vdEJlZm9yZUVycm9yJztcbiAgdGhpcy5kYXRlID0gZGF0ZTtcbn07XG5cbk5vdEJlZm9yZUVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gTm90QmVmb3JlRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gTm90QmVmb3JlRXJyb3I7Il0sIm5hbWVzIjpbIkpzb25XZWJUb2tlbkVycm9yIiwicmVxdWlyZSIsIk5vdEJlZm9yZUVycm9yIiwibWVzc2FnZSIsImRhdGUiLCJjYWxsIiwibmFtZSIsInByb3RvdHlwZSIsIk9iamVjdCIsImNyZWF0ZSIsImNvbnN0cnVjdG9yIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar TokenExpiredError = function(message, expiredAt) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"TokenExpiredError\";\n    this.expiredAt = expiredAt;\n};\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\nTokenExpiredError.prototype.constructor = TokenExpiredError;\nmodule.exports = TokenExpiredError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLG9CQUFvQixTQUFVQyxPQUFPLEVBQUVDLFNBQVM7SUFDbERKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLFNBQVMsR0FBR0E7QUFDbkI7QUFFQUYsa0JBQWtCSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXZFTCxrQkFBa0JLLFNBQVMsQ0FBQ0csV0FBVyxHQUFHUjtBQUUxQ1MsT0FBT0MsT0FBTyxHQUFHViIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL1Rva2VuRXhwaXJlZEVycm9yLmpzPzkwZWMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgVG9rZW5FeHBpcmVkRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZXhwaXJlZEF0KSB7XG4gIEpzb25XZWJUb2tlbkVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIHRoaXMubmFtZSA9ICdUb2tlbkV4cGlyZWRFcnJvcic7XG4gIHRoaXMuZXhwaXJlZEF0ID0gZXhwaXJlZEF0O1xufTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ub2tlbkV4cGlyZWRFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBUb2tlbkV4cGlyZWRFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBUb2tlbkV4cGlyZWRFcnJvcjsiXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJyZXF1aXJlIiwiVG9rZW5FeHBpcmVkRXJyb3IiLCJtZXNzYWdlIiwiZXhwaXJlZEF0IiwiY2FsbCIsIm5hbWUiLCJwcm90b3R5cGUiLCJPYmplY3QiLCJjcmVhdGUiLCJjb25zdHJ1Y3RvciIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=15.7.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUV2QkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL2FzeW1tZXRyaWNLZXlEZXRhaWxzU3VwcG9ydGVkLmpzPzczZDkiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE1LjcuMCcpO1xuIl0sIm5hbWVzIjpbInNlbXZlciIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwic2F0aXNmaWVzIiwicHJvY2VzcyIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \"^6.12.0 || >=8.0.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUVyQkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL3BzU3VwcG9ydGVkLmpzP2M4ZDQiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnXjYuMTIuMCB8fCA+PTguMC4wJyk7XG4iXSwibmFtZXMiOlsic2VtdmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzYXRpc2ZpZXMiLCJwcm9jZXNzIiwidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=16.9.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFNBQVNDLG1CQUFPQSxDQUFDO0FBRXZCQyxPQUFPQyxPQUFPLEdBQUdILE9BQU9JLFNBQVMsQ0FBQ0MsUUFBUUMsT0FBTyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvcnNhUHNzS2V5RGV0YWlsc1N1cHBvcnRlZC5qcz9mOTA4Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnPj0xNi45LjAnKTtcbiJdLCJuYW1lcyI6WyJzZW12ZXIiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInNhdGlzZmllcyIsInByb2Nlc3MiLCJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\nmodule.exports = function(time, iat) {\n    var timestamp = iat || Math.floor(Date.now() / 1000);\n    if (typeof time === \"string\") {\n        var milliseconds = ms(time);\n        if (typeof milliseconds === \"undefined\") {\n            return;\n        }\n        return Math.floor(timestamp + milliseconds / 1000);\n    } else if (typeof time === \"number\") {\n        return timestamp + time;\n    } else {\n        return;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxLQUFLQyxtQkFBT0EsQ0FBQztBQUVqQkMsT0FBT0MsT0FBTyxHQUFHLFNBQVVDLElBQUksRUFBRUMsR0FBRztJQUNsQyxJQUFJQyxZQUFZRCxPQUFPRSxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSztJQUUvQyxJQUFJLE9BQU9OLFNBQVMsVUFBVTtRQUM1QixJQUFJTyxlQUFlWCxHQUFHSTtRQUN0QixJQUFJLE9BQU9PLGlCQUFpQixhQUFhO1lBQ3ZDO1FBQ0Y7UUFDQSxPQUFPSixLQUFLQyxLQUFLLENBQUNGLFlBQVlLLGVBQWU7SUFDL0MsT0FBTyxJQUFJLE9BQU9QLFNBQVMsVUFBVTtRQUNuQyxPQUFPRSxZQUFZRjtJQUNyQixPQUFPO1FBQ0w7SUFDRjtBQUVGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvdGltZXNwYW4uanM/Y2VmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbXMgPSByZXF1aXJlKCdtcycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICh0aW1lLCBpYXQpIHtcbiAgdmFyIHRpbWVzdGFtcCA9IGlhdCB8fCBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcblxuICBpZiAodHlwZW9mIHRpbWUgPT09ICdzdHJpbmcnKSB7XG4gICAgdmFyIG1pbGxpc2Vjb25kcyA9IG1zKHRpbWUpO1xuICAgIGlmICh0eXBlb2YgbWlsbGlzZWNvbmRzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICByZXR1cm4gTWF0aC5mbG9vcih0aW1lc3RhbXAgKyBtaWxsaXNlY29uZHMgLyAxMDAwKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgdGltZSA9PT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4gdGltZXN0YW1wICsgdGltZTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm47XG4gIH1cblxufTsiXSwibmFtZXMiOlsibXMiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInRpbWUiLCJpYXQiLCJ0aW1lc3RhbXAiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93IiwibWlsbGlzZWNvbmRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\nconst allowedAlgorithmsForKeys = {\n    \"ec\": [\n        \"ES256\",\n        \"ES384\",\n        \"ES512\"\n    ],\n    \"rsa\": [\n        \"RS256\",\n        \"PS256\",\n        \"RS384\",\n        \"PS384\",\n        \"RS512\",\n        \"PS512\"\n    ],\n    \"rsa-pss\": [\n        \"PS256\",\n        \"PS384\",\n        \"PS512\"\n    ]\n};\nconst allowedCurves = {\n    ES256: \"prime256v1\",\n    ES384: \"secp384r1\",\n    ES512: \"secp521r1\"\n};\nmodule.exports = function(algorithm, key) {\n    if (!algorithm || !key) return;\n    const keyType = key.asymmetricKeyType;\n    if (!keyType) return;\n    const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n    if (!allowedAlgorithms) {\n        throw new Error(`Unknown key type \"${keyType}\".`);\n    }\n    if (!allowedAlgorithms.includes(algorithm)) {\n        throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(\", \")}.`);\n    }\n    /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */ /* istanbul ignore next */ if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n        switch(keyType){\n            case \"ec\":\n                const keyCurve = key.asymmetricKeyDetails.namedCurve;\n                const allowedCurve = allowedCurves[algorithm];\n                if (keyCurve !== allowedCurve) {\n                    throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n                }\n                break;\n            case \"rsa-pss\":\n                if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n                    const length = parseInt(algorithm.slice(-3), 10);\n                    const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                    if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                    if (saltLength !== undefined && saltLength > length >> 3) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                }\n                break;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(ssr)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(ssr)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(ssr)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(ssr)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(ssr)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(ssr)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(ssr)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(ssr)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(ssr)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst SUPPORTED_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\",\n    \"ES256\",\n    \"ES384\",\n    \"ES512\",\n    \"HS256\",\n    \"HS384\",\n    \"HS512\",\n    \"none\"\n];\nif (PS_SUPPORTED) {\n    SUPPORTED_ALGS.splice(3, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nconst sign_options_schema = {\n    expiresIn: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"expiresIn\" should be a number of seconds or string representing a timespan'\n    },\n    notBefore: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"notBefore\" should be a number of seconds or string representing a timespan'\n    },\n    audience: {\n        isValid: function(value) {\n            return isString(value) || Array.isArray(value);\n        },\n        message: '\"audience\" must be a string or array'\n    },\n    algorithm: {\n        isValid: includes.bind(null, SUPPORTED_ALGS),\n        message: '\"algorithm\" must be a valid string enum value'\n    },\n    header: {\n        isValid: isPlainObject,\n        message: '\"header\" must be an object'\n    },\n    encoding: {\n        isValid: isString,\n        message: '\"encoding\" must be a string'\n    },\n    issuer: {\n        isValid: isString,\n        message: '\"issuer\" must be a string'\n    },\n    subject: {\n        isValid: isString,\n        message: '\"subject\" must be a string'\n    },\n    jwtid: {\n        isValid: isString,\n        message: '\"jwtid\" must be a string'\n    },\n    noTimestamp: {\n        isValid: isBoolean,\n        message: '\"noTimestamp\" must be a boolean'\n    },\n    keyid: {\n        isValid: isString,\n        message: '\"keyid\" must be a string'\n    },\n    mutatePayload: {\n        isValid: isBoolean,\n        message: '\"mutatePayload\" must be a boolean'\n    },\n    allowInsecureKeySizes: {\n        isValid: isBoolean,\n        message: '\"allowInsecureKeySizes\" must be a boolean'\n    },\n    allowInvalidAsymmetricKeyTypes: {\n        isValid: isBoolean,\n        message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'\n    }\n};\nconst registered_claims_schema = {\n    iat: {\n        isValid: isNumber,\n        message: '\"iat\" should be a number of seconds'\n    },\n    exp: {\n        isValid: isNumber,\n        message: '\"exp\" should be a number of seconds'\n    },\n    nbf: {\n        isValid: isNumber,\n        message: '\"nbf\" should be a number of seconds'\n    }\n};\nfunction validate(schema, allowUnknown, object, parameterName) {\n    if (!isPlainObject(object)) {\n        throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n    }\n    Object.keys(object).forEach(function(key) {\n        const validator = schema[key];\n        if (!validator) {\n            if (!allowUnknown) {\n                throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n            }\n            return;\n        }\n        if (!validator.isValid(object[key])) {\n            throw new Error(validator.message);\n        }\n    });\n}\nfunction validateOptions(options) {\n    return validate(sign_options_schema, false, options, \"options\");\n}\nfunction validatePayload(payload) {\n    return validate(registered_claims_schema, true, payload, \"payload\");\n}\nconst options_to_payload = {\n    \"audience\": \"aud\",\n    \"issuer\": \"iss\",\n    \"subject\": \"sub\",\n    \"jwtid\": \"jti\"\n};\nconst options_for_objects = [\n    \"expiresIn\",\n    \"notBefore\",\n    \"noTimestamp\",\n    \"audience\",\n    \"issuer\",\n    \"subject\",\n    \"jwtid\"\n];\nmodule.exports = function(payload, secretOrPrivateKey, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else {\n        options = options || {};\n    }\n    const isObjectPayload = typeof payload === \"object\" && !Buffer.isBuffer(payload);\n    const header = Object.assign({\n        alg: options.algorithm || \"HS256\",\n        typ: isObjectPayload ? \"JWT\" : undefined,\n        kid: options.keyid\n    }, options.header);\n    function failure(err) {\n        if (callback) {\n            return callback(err);\n        }\n        throw err;\n    }\n    if (!secretOrPrivateKey && options.algorithm !== \"none\") {\n        return failure(new Error(\"secretOrPrivateKey must have a value\"));\n    }\n    if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n        try {\n            secretOrPrivateKey = createPrivateKey(secretOrPrivateKey);\n        } catch (_) {\n            try {\n                secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === \"string\" ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey);\n            } catch (_) {\n                return failure(new Error(\"secretOrPrivateKey is not valid key material\"));\n            }\n        }\n    }\n    if (header.alg.startsWith(\"HS\") && secretOrPrivateKey.type !== \"secret\") {\n        return failure(new Error(`secretOrPrivateKey must be a symmetric key when using ${header.alg}`));\n    } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n        if (secretOrPrivateKey.type !== \"private\") {\n            return failure(new Error(`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInsecureKeySizes && !header.alg.startsWith(\"ES\") && secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n        secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n            return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n        }\n    }\n    if (typeof payload === \"undefined\") {\n        return failure(new Error(\"payload is required\"));\n    } else if (isObjectPayload) {\n        try {\n            validatePayload(payload);\n        } catch (error) {\n            return failure(error);\n        }\n        if (!options.mutatePayload) {\n            payload = Object.assign({}, payload);\n        }\n    } else {\n        const invalid_options = options_for_objects.filter(function(opt) {\n            return typeof options[opt] !== \"undefined\";\n        });\n        if (invalid_options.length > 0) {\n            return failure(new Error(\"invalid \" + invalid_options.join(\",\") + \" option for \" + typeof payload + \" payload\"));\n        }\n    }\n    if (typeof payload.exp !== \"undefined\" && typeof options.expiresIn !== \"undefined\") {\n        return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n    }\n    if (typeof payload.nbf !== \"undefined\" && typeof options.notBefore !== \"undefined\") {\n        return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n    }\n    try {\n        validateOptions(options);\n    } catch (error) {\n        return failure(error);\n    }\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n        try {\n            validateAsymmetricKey(header.alg, secretOrPrivateKey);\n        } catch (error) {\n            return failure(error);\n        }\n    }\n    const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n    if (options.noTimestamp) {\n        delete payload.iat;\n    } else if (isObjectPayload) {\n        payload.iat = timestamp;\n    }\n    if (typeof options.notBefore !== \"undefined\") {\n        try {\n            payload.nbf = timespan(options.notBefore, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.nbf === \"undefined\") {\n            return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    if (typeof options.expiresIn !== \"undefined\" && typeof payload === \"object\") {\n        try {\n            payload.exp = timespan(options.expiresIn, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.exp === \"undefined\") {\n            return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    Object.keys(options_to_payload).forEach(function(key) {\n        const claim = options_to_payload[key];\n        if (typeof options[key] !== \"undefined\") {\n            if (typeof payload[claim] !== \"undefined\") {\n                return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n            }\n            payload[claim] = options[key];\n        }\n    });\n    const encoding = options.encoding || \"utf8\";\n    if (typeof callback === \"function\") {\n        callback = callback && once(callback);\n        jws.createSign({\n            header: header,\n            privateKey: secretOrPrivateKey,\n            payload: payload,\n            encoding: encoding\n        }).once(\"error\", callback).once(\"done\", function(signature) {\n            // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n            if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n                return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n            }\n            callback(null, signature);\n        });\n    } else {\n        let signature = jws.sign({\n            header: header,\n            payload: payload,\n            secret: secretOrPrivateKey,\n            encoding: encoding\n        });\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n            throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`);\n        }\n        return signature;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(ssr)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(ssr)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(ssr)/./node_modules/jws/index.js\");\nconst { KeyObject, createSecretKey, createPublicKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PUB_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst EC_KEY_ALGS = [\n    \"ES256\",\n    \"ES384\",\n    \"ES512\"\n];\nconst RSA_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst HS_ALGS = [\n    \"HS256\",\n    \"HS384\",\n    \"HS512\"\n];\nif (PS_SUPPORTED) {\n    PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n    RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nmodule.exports = function(jwtString, secretOrPublicKey, options, callback) {\n    if (typeof options === \"function\" && !callback) {\n        callback = options;\n        options = {};\n    }\n    if (!options) {\n        options = {};\n    }\n    //clone this object since we are going to mutate it.\n    options = Object.assign({}, options);\n    let done;\n    if (callback) {\n        done = callback;\n    } else {\n        done = function(err, data) {\n            if (err) throw err;\n            return data;\n        };\n    }\n    if (options.clockTimestamp && typeof options.clockTimestamp !== \"number\") {\n        return done(new JsonWebTokenError(\"clockTimestamp must be a number\"));\n    }\n    if (options.nonce !== undefined && (typeof options.nonce !== \"string\" || options.nonce.trim() === \"\")) {\n        return done(new JsonWebTokenError(\"nonce must be a non-empty string\"));\n    }\n    if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== \"boolean\") {\n        return done(new JsonWebTokenError(\"allowInvalidAsymmetricKeyTypes must be a boolean\"));\n    }\n    const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n    if (!jwtString) {\n        return done(new JsonWebTokenError(\"jwt must be provided\"));\n    }\n    if (typeof jwtString !== \"string\") {\n        return done(new JsonWebTokenError(\"jwt must be a string\"));\n    }\n    const parts = jwtString.split(\".\");\n    if (parts.length !== 3) {\n        return done(new JsonWebTokenError(\"jwt malformed\"));\n    }\n    let decodedToken;\n    try {\n        decodedToken = decode(jwtString, {\n            complete: true\n        });\n    } catch (err) {\n        return done(err);\n    }\n    if (!decodedToken) {\n        return done(new JsonWebTokenError(\"invalid token\"));\n    }\n    const header = decodedToken.header;\n    let getSecret;\n    if (typeof secretOrPublicKey === \"function\") {\n        if (!callback) {\n            return done(new JsonWebTokenError(\"verify must be called asynchronous if secret or public key is provided as a callback\"));\n        }\n        getSecret = secretOrPublicKey;\n    } else {\n        getSecret = function(header, secretCallback) {\n            return secretCallback(null, secretOrPublicKey);\n        };\n    }\n    return getSecret(header, function(err, secretOrPublicKey) {\n        if (err) {\n            return done(new JsonWebTokenError(\"error in secret or public key callback: \" + err.message));\n        }\n        const hasSignature = parts[2].trim() !== \"\";\n        if (!hasSignature && secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"jwt signature is required\"));\n        }\n        if (hasSignature && !secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"secret or public key must be provided\"));\n        }\n        if (!hasSignature && !options.algorithms) {\n            return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n        }\n        if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n            try {\n                secretOrPublicKey = createPublicKey(secretOrPublicKey);\n            } catch (_) {\n                try {\n                    secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === \"string\" ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n                } catch (_) {\n                    return done(new JsonWebTokenError(\"secretOrPublicKey is not valid key material\"));\n                }\n            }\n        }\n        if (!options.algorithms) {\n            if (secretOrPublicKey.type === \"secret\") {\n                options.algorithms = HS_ALGS;\n            } else if ([\n                \"rsa\",\n                \"rsa-pss\"\n            ].includes(secretOrPublicKey.asymmetricKeyType)) {\n                options.algorithms = RSA_KEY_ALGS;\n            } else if (secretOrPublicKey.asymmetricKeyType === \"ec\") {\n                options.algorithms = EC_KEY_ALGS;\n            } else {\n                options.algorithms = PUB_KEY_ALGS;\n            }\n        }\n        if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n            return done(new JsonWebTokenError(\"invalid algorithm\"));\n        }\n        if (header.alg.startsWith(\"HS\") && secretOrPublicKey.type !== \"secret\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be a symmetric key when using ${header.alg}`));\n        } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== \"public\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInvalidAsymmetricKeyTypes) {\n            try {\n                validateAsymmetricKey(header.alg, secretOrPublicKey);\n            } catch (e) {\n                return done(e);\n            }\n        }\n        let valid;\n        try {\n            valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n        } catch (e) {\n            return done(e);\n        }\n        if (!valid) {\n            return done(new JsonWebTokenError(\"invalid signature\"));\n        }\n        const payload = decodedToken.payload;\n        if (typeof payload.nbf !== \"undefined\" && !options.ignoreNotBefore) {\n            if (typeof payload.nbf !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid nbf value\"));\n            }\n            if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n                return done(new NotBeforeError(\"jwt not active\", new Date(payload.nbf * 1000)));\n            }\n        }\n        if (typeof payload.exp !== \"undefined\" && !options.ignoreExpiration) {\n            if (typeof payload.exp !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid exp value\"));\n            }\n            if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"jwt expired\", new Date(payload.exp * 1000)));\n            }\n        }\n        if (options.audience) {\n            const audiences = Array.isArray(options.audience) ? options.audience : [\n                options.audience\n            ];\n            const target = Array.isArray(payload.aud) ? payload.aud : [\n                payload.aud\n            ];\n            const match = target.some(function(targetAudience) {\n                return audiences.some(function(audience) {\n                    return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n                });\n            });\n            if (!match) {\n                return done(new JsonWebTokenError(\"jwt audience invalid. expected: \" + audiences.join(\" or \")));\n            }\n        }\n        if (options.issuer) {\n            const invalid_issuer = typeof options.issuer === \"string\" && payload.iss !== options.issuer || Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1;\n            if (invalid_issuer) {\n                return done(new JsonWebTokenError(\"jwt issuer invalid. expected: \" + options.issuer));\n            }\n        }\n        if (options.subject) {\n            if (payload.sub !== options.subject) {\n                return done(new JsonWebTokenError(\"jwt subject invalid. expected: \" + options.subject));\n            }\n        }\n        if (options.jwtid) {\n            if (payload.jti !== options.jwtid) {\n                return done(new JsonWebTokenError(\"jwt jwtid invalid. expected: \" + options.jwtid));\n            }\n        }\n        if (options.nonce) {\n            if (payload.nonce !== options.nonce) {\n                return done(new JsonWebTokenError(\"jwt nonce invalid. expected: \" + options.nonce));\n            }\n        }\n        if (options.maxAge) {\n            if (typeof payload.iat !== \"number\") {\n                return done(new JsonWebTokenError(\"iat required when maxAge is specified\"));\n            }\n            const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n            if (typeof maxAgeTimestamp === \"undefined\") {\n                return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n            }\n            if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"maxAge exceeded\", new Date(maxAgeTimestamp * 1000)));\n            }\n        }\n        if (options.complete === true) {\n            const signature = decodedToken.signature;\n            return done(null, {\n                header: header,\n                payload: payload,\n                signature: signature\n            });\n        }\n        return done(null, payload);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/verify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nmodule.exports = function(jwt, options) {\n    options = options || {};\n    var decoded = jws.decode(jwt, options);\n    if (!decoded) {\n        return null;\n    }\n    var payload = decoded.payload;\n    //try parse the payload\n    if (typeof payload === \"string\") {\n        try {\n            var obj = JSON.parse(payload);\n            if (obj !== null && typeof obj === \"object\") {\n                payload = obj;\n            }\n        } catch (e) {}\n    }\n    //return header if `complete` option is enabled.  header includes claims\n    //such as `kid` and `alg` used to select the key within a JWKS needed to\n    //verify the signature\n    if (options.complete === true) {\n        return {\n            header: decoded.header,\n            payload: payload,\n            signature: decoded.signature\n        };\n    }\n    return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n    decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n    verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n    sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n    JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n    NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n    TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBQSxPQUFPQyxPQUFPLEdBQUc7SUFDZkMsUUFBUUMsbUJBQU9BLENBQUM7SUFDaEJDLFFBQVFELG1CQUFPQSxDQUFDO0lBQ2hCRSxNQUFNRixtQkFBT0EsQ0FBQztJQUNkRyxtQkFBbUJILG1CQUFPQSxDQUFDO0lBQzNCSSxnQkFBZ0JKLG1CQUFPQSxDQUFDO0lBQ3hCSyxtQkFBbUJMLG1CQUFPQSxDQUFDO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9pbmRleC5qcz85YmU4Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xuICBkZWNvZGU6IHJlcXVpcmUoJy4vZGVjb2RlJyksXG4gIHZlcmlmeTogcmVxdWlyZSgnLi92ZXJpZnknKSxcbiAgc2lnbjogcmVxdWlyZSgnLi9zaWduJyksXG4gIEpzb25XZWJUb2tlbkVycm9yOiByZXF1aXJlKCcuL2xpYi9Kc29uV2ViVG9rZW5FcnJvcicpLFxuICBOb3RCZWZvcmVFcnJvcjogcmVxdWlyZSgnLi9saWIvTm90QmVmb3JlRXJyb3InKSxcbiAgVG9rZW5FeHBpcmVkRXJyb3I6IHJlcXVpcmUoJy4vbGliL1Rva2VuRXhwaXJlZEVycm9yJyksXG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJkZWNvZGUiLCJyZXF1aXJlIiwidmVyaWZ5Iiwic2lnbiIsIkpzb25XZWJUb2tlbkVycm9yIiwiTm90QmVmb3JlRXJyb3IiLCJUb2tlbkV4cGlyZWRFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function(message, error) {\n    Error.call(this, message);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"JsonWebTokenError\";\n    this.message = message;\n    if (error) this.inner = error;\n};\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcnAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcz8xNmYyIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IGZ1bmN0aW9uIChtZXNzYWdlLCBlcnJvcikge1xuICBFcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICBpZihFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSkge1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG4gIHRoaXMubmFtZSA9ICdKc29uV2ViVG9rZW5FcnJvcic7XG4gIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gIGlmIChlcnJvcikgdGhpcy5pbm5lciA9IGVycm9yO1xufTtcblxuSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShFcnJvci5wcm90b3R5cGUpO1xuSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gSnNvbldlYlRva2VuRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gSnNvbldlYlRva2VuRXJyb3I7XG4iXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJtZXNzYWdlIiwiZXJyb3IiLCJFcnJvciIsImNhbGwiLCJjYXB0dXJlU3RhY2tUcmFjZSIsImNvbnN0cnVjdG9yIiwibmFtZSIsImlubmVyIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJtYXBwaW5ncyI6IkFBQUEsSUFBSUEsb0JBQW9CLFNBQVVDLE9BQU8sRUFBRUMsS0FBSztJQUM5Q0MsTUFBTUMsSUFBSSxDQUFDLElBQUksRUFBRUg7SUFDakIsSUFBR0UsTUFBTUUsaUJBQWlCLEVBQUU7UUFDMUJGLE1BQU1FLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNDLFdBQVc7SUFDaEQ7SUFDQSxJQUFJLENBQUNDLElBQUksR0FBRztJQUNaLElBQUksQ0FBQ04sT0FBTyxHQUFHQTtJQUNmLElBQUlDLE9BQU8sSUFBSSxDQUFDTSxLQUFLLEdBQUdOO0FBQzFCO0FBRUFGLGtCQUFrQlMsU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNSLE1BQU1NLFNBQVM7QUFDM0RULGtCQUFrQlMsU0FBUyxDQUFDSCxXQUFXLEdBQUdOO0FBRTFDWSxPQUFPQyxPQUFPLEdBQUdiIiwiZmlsZSI6Iihyc2MpLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvSnNvbldlYlRva2VuRXJyb3IuanMiLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar NotBeforeError = function(message, date) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"NotBeforeError\";\n    this.date = date;\n};\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\nNotBeforeError.prototype.constructor = NotBeforeError;\nmodule.exports = NotBeforeError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLGlCQUFpQixTQUFVQyxPQUFPLEVBQUVDLElBQUk7SUFDMUNKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLElBQUksR0FBR0E7QUFDZDtBQUVBRixlQUFlSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXBFTCxlQUFlSyxTQUFTLENBQUNHLFdBQVcsR0FBR1I7QUFFdkNTLE9BQU9DLE9BQU8sR0FBR1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcnAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcz84NjY4Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIE5vdEJlZm9yZUVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGRhdGUpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ05vdEJlZm9yZUVycm9yJztcbiAgdGhpcy5kYXRlID0gZGF0ZTtcbn07XG5cbk5vdEJlZm9yZUVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gTm90QmVmb3JlRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gTm90QmVmb3JlRXJyb3I7Il0sIm5hbWVzIjpbIkpzb25XZWJUb2tlbkVycm9yIiwicmVxdWlyZSIsIk5vdEJlZm9yZUVycm9yIiwibWVzc2FnZSIsImRhdGUiLCJjYWxsIiwibmFtZSIsInByb3RvdHlwZSIsIk9iamVjdCIsImNyZWF0ZSIsImNvbnN0cnVjdG9yIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar TokenExpiredError = function(message, expiredAt) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"TokenExpiredError\";\n    this.expiredAt = expiredAt;\n};\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\nTokenExpiredError.prototype.constructor = TokenExpiredError;\nmodule.exports = TokenExpiredError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxvQkFBb0JDLG1CQUFPQSxDQUFDO0FBRWhDLElBQUlDLG9CQUFvQixTQUFVQyxPQUFPLEVBQUVDLFNBQVM7SUFDbERKLGtCQUFrQkssSUFBSSxDQUFDLElBQUksRUFBRUY7SUFDN0IsSUFBSSxDQUFDRyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNGLFNBQVMsR0FBR0E7QUFDbkI7QUFFQUYsa0JBQWtCSyxTQUFTLEdBQUdDLE9BQU9DLE1BQU0sQ0FBQ1Qsa0JBQWtCTyxTQUFTO0FBRXZFTCxrQkFBa0JLLFNBQVMsQ0FBQ0csV0FBVyxHQUFHUjtBQUUxQ1MsT0FBT0MsT0FBTyxHQUFHViIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL1Rva2VuRXhwaXJlZEVycm9yLmpzPzkwZWMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgVG9rZW5FeHBpcmVkRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZXhwaXJlZEF0KSB7XG4gIEpzb25XZWJUb2tlbkVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIHRoaXMubmFtZSA9ICdUb2tlbkV4cGlyZWRFcnJvcic7XG4gIHRoaXMuZXhwaXJlZEF0ID0gZXhwaXJlZEF0O1xufTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ub2tlbkV4cGlyZWRFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBUb2tlbkV4cGlyZWRFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBUb2tlbkV4cGlyZWRFcnJvcjsiXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJyZXF1aXJlIiwiVG9rZW5FeHBpcmVkRXJyb3IiLCJtZXNzYWdlIiwiZXhwaXJlZEF0IiwiY2FsbCIsIm5hbWUiLCJwcm90b3R5cGUiLCJPYmplY3QiLCJjcmVhdGUiLCJjb25zdHJ1Y3RvciIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=15.7.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUV2QkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL2FzeW1tZXRyaWNLZXlEZXRhaWxzU3VwcG9ydGVkLmpzPzczZDkiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE1LjcuMCcpO1xuIl0sIm5hbWVzIjpbInNlbXZlciIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwic2F0aXNmaWVzIiwicHJvY2VzcyIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \"^6.12.0 || >=8.0.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUVyQkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL3BzU3VwcG9ydGVkLmpzP2M4ZDQiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnXjYuMTIuMCB8fCA+PTguMC4wJyk7XG4iXSwibmFtZXMiOlsic2VtdmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzYXRpc2ZpZXMiLCJwcm9jZXNzIiwidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=16.9.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLE1BQU1BLFNBQVNDLG1CQUFPQSxDQUFDO0FBRXZCQyxPQUFPQyxPQUFPLEdBQUdILE9BQU9JLFNBQVMsQ0FBQ0MsUUFBUUMsT0FBTyxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvcnNhUHNzS2V5RGV0YWlsc1N1cHBvcnRlZC5qcz9mOTA4Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnPj0xNi45LjAnKTtcbiJdLCJuYW1lcyI6WyJzZW12ZXIiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInNhdGlzZmllcyIsInByb2Nlc3MiLCJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\nmodule.exports = function(time, iat) {\n    var timestamp = iat || Math.floor(Date.now() / 1000);\n    if (typeof time === \"string\") {\n        var milliseconds = ms(time);\n        if (typeof milliseconds === \"undefined\") {\n            return;\n        }\n        return Math.floor(timestamp + milliseconds / 1000);\n    } else if (typeof time === \"number\") {\n        return timestamp + time;\n    } else {\n        return;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxJQUFJQSxLQUFLQyxtQkFBT0EsQ0FBQztBQUVqQkMsT0FBT0MsT0FBTyxHQUFHLFNBQVVDLElBQUksRUFBRUMsR0FBRztJQUNsQyxJQUFJQyxZQUFZRCxPQUFPRSxLQUFLQyxLQUFLLENBQUNDLEtBQUtDLEdBQUcsS0FBSztJQUUvQyxJQUFJLE9BQU9OLFNBQVMsVUFBVTtRQUM1QixJQUFJTyxlQUFlWCxHQUFHSTtRQUN0QixJQUFJLE9BQU9PLGlCQUFpQixhQUFhO1lBQ3ZDO1FBQ0Y7UUFDQSxPQUFPSixLQUFLQyxLQUFLLENBQUNGLFlBQVlLLGVBQWU7SUFDL0MsT0FBTyxJQUFJLE9BQU9QLFNBQVMsVUFBVTtRQUNuQyxPQUFPRSxZQUFZRjtJQUNyQixPQUFPO1FBQ0w7SUFDRjtBQUVGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvdGltZXNwYW4uanM/Y2VmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbXMgPSByZXF1aXJlKCdtcycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICh0aW1lLCBpYXQpIHtcbiAgdmFyIHRpbWVzdGFtcCA9IGlhdCB8fCBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcblxuICBpZiAodHlwZW9mIHRpbWUgPT09ICdzdHJpbmcnKSB7XG4gICAgdmFyIG1pbGxpc2Vjb25kcyA9IG1zKHRpbWUpO1xuICAgIGlmICh0eXBlb2YgbWlsbGlzZWNvbmRzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICByZXR1cm4gTWF0aC5mbG9vcih0aW1lc3RhbXAgKyBtaWxsaXNlY29uZHMgLyAxMDAwKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgdGltZSA9PT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4gdGltZXN0YW1wICsgdGltZTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm47XG4gIH1cblxufTsiXSwibmFtZXMiOlsibXMiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInRpbWUiLCJpYXQiLCJ0aW1lc3RhbXAiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93IiwibWlsbGlzZWNvbmRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\nconst allowedAlgorithmsForKeys = {\n    \"ec\": [\n        \"ES256\",\n        \"ES384\",\n        \"ES512\"\n    ],\n    \"rsa\": [\n        \"RS256\",\n        \"PS256\",\n        \"RS384\",\n        \"PS384\",\n        \"RS512\",\n        \"PS512\"\n    ],\n    \"rsa-pss\": [\n        \"PS256\",\n        \"PS384\",\n        \"PS512\"\n    ]\n};\nconst allowedCurves = {\n    ES256: \"prime256v1\",\n    ES384: \"secp384r1\",\n    ES512: \"secp521r1\"\n};\nmodule.exports = function(algorithm, key) {\n    if (!algorithm || !key) return;\n    const keyType = key.asymmetricKeyType;\n    if (!keyType) return;\n    const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n    if (!allowedAlgorithms) {\n        throw new Error(`Unknown key type \"${keyType}\".`);\n    }\n    if (!allowedAlgorithms.includes(algorithm)) {\n        throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(\", \")}.`);\n    }\n    /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */ /* istanbul ignore next */ if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n        switch(keyType){\n            case \"ec\":\n                const keyCurve = key.asymmetricKeyDetails.namedCurve;\n                const allowedCurve = allowedCurves[algorithm];\n                if (keyCurve !== allowedCurve) {\n                    throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n                }\n                break;\n            case \"rsa-pss\":\n                if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n                    const length = parseInt(algorithm.slice(-3), 10);\n                    const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                    if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                    if (saltLength !== undefined && saltLength > length >> 3) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                }\n                break;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst SUPPORTED_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\",\n    \"ES256\",\n    \"ES384\",\n    \"ES512\",\n    \"HS256\",\n    \"HS384\",\n    \"HS512\",\n    \"none\"\n];\nif (PS_SUPPORTED) {\n    SUPPORTED_ALGS.splice(3, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nconst sign_options_schema = {\n    expiresIn: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"expiresIn\" should be a number of seconds or string representing a timespan'\n    },\n    notBefore: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"notBefore\" should be a number of seconds or string representing a timespan'\n    },\n    audience: {\n        isValid: function(value) {\n            return isString(value) || Array.isArray(value);\n        },\n        message: '\"audience\" must be a string or array'\n    },\n    algorithm: {\n        isValid: includes.bind(null, SUPPORTED_ALGS),\n        message: '\"algorithm\" must be a valid string enum value'\n    },\n    header: {\n        isValid: isPlainObject,\n        message: '\"header\" must be an object'\n    },\n    encoding: {\n        isValid: isString,\n        message: '\"encoding\" must be a string'\n    },\n    issuer: {\n        isValid: isString,\n        message: '\"issuer\" must be a string'\n    },\n    subject: {\n        isValid: isString,\n        message: '\"subject\" must be a string'\n    },\n    jwtid: {\n        isValid: isString,\n        message: '\"jwtid\" must be a string'\n    },\n    noTimestamp: {\n        isValid: isBoolean,\n        message: '\"noTimestamp\" must be a boolean'\n    },\n    keyid: {\n        isValid: isString,\n        message: '\"keyid\" must be a string'\n    },\n    mutatePayload: {\n        isValid: isBoolean,\n        message: '\"mutatePayload\" must be a boolean'\n    },\n    allowInsecureKeySizes: {\n        isValid: isBoolean,\n        message: '\"allowInsecureKeySizes\" must be a boolean'\n    },\n    allowInvalidAsymmetricKeyTypes: {\n        isValid: isBoolean,\n        message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'\n    }\n};\nconst registered_claims_schema = {\n    iat: {\n        isValid: isNumber,\n        message: '\"iat\" should be a number of seconds'\n    },\n    exp: {\n        isValid: isNumber,\n        message: '\"exp\" should be a number of seconds'\n    },\n    nbf: {\n        isValid: isNumber,\n        message: '\"nbf\" should be a number of seconds'\n    }\n};\nfunction validate(schema, allowUnknown, object, parameterName) {\n    if (!isPlainObject(object)) {\n        throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n    }\n    Object.keys(object).forEach(function(key) {\n        const validator = schema[key];\n        if (!validator) {\n            if (!allowUnknown) {\n                throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n            }\n            return;\n        }\n        if (!validator.isValid(object[key])) {\n            throw new Error(validator.message);\n        }\n    });\n}\nfunction validateOptions(options) {\n    return validate(sign_options_schema, false, options, \"options\");\n}\nfunction validatePayload(payload) {\n    return validate(registered_claims_schema, true, payload, \"payload\");\n}\nconst options_to_payload = {\n    \"audience\": \"aud\",\n    \"issuer\": \"iss\",\n    \"subject\": \"sub\",\n    \"jwtid\": \"jti\"\n};\nconst options_for_objects = [\n    \"expiresIn\",\n    \"notBefore\",\n    \"noTimestamp\",\n    \"audience\",\n    \"issuer\",\n    \"subject\",\n    \"jwtid\"\n];\nmodule.exports = function(payload, secretOrPrivateKey, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else {\n        options = options || {};\n    }\n    const isObjectPayload = typeof payload === \"object\" && !Buffer.isBuffer(payload);\n    const header = Object.assign({\n        alg: options.algorithm || \"HS256\",\n        typ: isObjectPayload ? \"JWT\" : undefined,\n        kid: options.keyid\n    }, options.header);\n    function failure(err) {\n        if (callback) {\n            return callback(err);\n        }\n        throw err;\n    }\n    if (!secretOrPrivateKey && options.algorithm !== \"none\") {\n        return failure(new Error(\"secretOrPrivateKey must have a value\"));\n    }\n    if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n        try {\n            secretOrPrivateKey = createPrivateKey(secretOrPrivateKey);\n        } catch (_) {\n            try {\n                secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === \"string\" ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey);\n            } catch (_) {\n                return failure(new Error(\"secretOrPrivateKey is not valid key material\"));\n            }\n        }\n    }\n    if (header.alg.startsWith(\"HS\") && secretOrPrivateKey.type !== \"secret\") {\n        return failure(new Error(`secretOrPrivateKey must be a symmetric key when using ${header.alg}`));\n    } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n        if (secretOrPrivateKey.type !== \"private\") {\n            return failure(new Error(`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInsecureKeySizes && !header.alg.startsWith(\"ES\") && secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n        secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n            return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n        }\n    }\n    if (typeof payload === \"undefined\") {\n        return failure(new Error(\"payload is required\"));\n    } else if (isObjectPayload) {\n        try {\n            validatePayload(payload);\n        } catch (error) {\n            return failure(error);\n        }\n        if (!options.mutatePayload) {\n            payload = Object.assign({}, payload);\n        }\n    } else {\n        const invalid_options = options_for_objects.filter(function(opt) {\n            return typeof options[opt] !== \"undefined\";\n        });\n        if (invalid_options.length > 0) {\n            return failure(new Error(\"invalid \" + invalid_options.join(\",\") + \" option for \" + typeof payload + \" payload\"));\n        }\n    }\n    if (typeof payload.exp !== \"undefined\" && typeof options.expiresIn !== \"undefined\") {\n        return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n    }\n    if (typeof payload.nbf !== \"undefined\" && typeof options.notBefore !== \"undefined\") {\n        return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n    }\n    try {\n        validateOptions(options);\n    } catch (error) {\n        return failure(error);\n    }\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n        try {\n            validateAsymmetricKey(header.alg, secretOrPrivateKey);\n        } catch (error) {\n            return failure(error);\n        }\n    }\n    const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n    if (options.noTimestamp) {\n        delete payload.iat;\n    } else if (isObjectPayload) {\n        payload.iat = timestamp;\n    }\n    if (typeof options.notBefore !== \"undefined\") {\n        try {\n            payload.nbf = timespan(options.notBefore, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.nbf === \"undefined\") {\n            return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    if (typeof options.expiresIn !== \"undefined\" && typeof payload === \"object\") {\n        try {\n            payload.exp = timespan(options.expiresIn, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.exp === \"undefined\") {\n            return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    Object.keys(options_to_payload).forEach(function(key) {\n        const claim = options_to_payload[key];\n        if (typeof options[key] !== \"undefined\") {\n            if (typeof payload[claim] !== \"undefined\") {\n                return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n            }\n            payload[claim] = options[key];\n        }\n    });\n    const encoding = options.encoding || \"utf8\";\n    if (typeof callback === \"function\") {\n        callback = callback && once(callback);\n        jws.createSign({\n            header: header,\n            privateKey: secretOrPrivateKey,\n            payload: payload,\n            encoding: encoding\n        }).once(\"error\", callback).once(\"done\", function(signature) {\n            // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n            if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n                return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n            }\n            callback(null, signature);\n        });\n    } else {\n        let signature = jws.sign({\n            header: header,\n            payload: payload,\n            secret: secretOrPrivateKey,\n            encoding: encoding\n        });\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n            throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`);\n        }\n        return signature;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst { KeyObject, createSecretKey, createPublicKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PUB_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst EC_KEY_ALGS = [\n    \"ES256\",\n    \"ES384\",\n    \"ES512\"\n];\nconst RSA_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst HS_ALGS = [\n    \"HS256\",\n    \"HS384\",\n    \"HS512\"\n];\nif (PS_SUPPORTED) {\n    PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n    RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nmodule.exports = function(jwtString, secretOrPublicKey, options, callback) {\n    if (typeof options === \"function\" && !callback) {\n        callback = options;\n        options = {};\n    }\n    if (!options) {\n        options = {};\n    }\n    //clone this object since we are going to mutate it.\n    options = Object.assign({}, options);\n    let done;\n    if (callback) {\n        done = callback;\n    } else {\n        done = function(err, data) {\n            if (err) throw err;\n            return data;\n        };\n    }\n    if (options.clockTimestamp && typeof options.clockTimestamp !== \"number\") {\n        return done(new JsonWebTokenError(\"clockTimestamp must be a number\"));\n    }\n    if (options.nonce !== undefined && (typeof options.nonce !== \"string\" || options.nonce.trim() === \"\")) {\n        return done(new JsonWebTokenError(\"nonce must be a non-empty string\"));\n    }\n    if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== \"boolean\") {\n        return done(new JsonWebTokenError(\"allowInvalidAsymmetricKeyTypes must be a boolean\"));\n    }\n    const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n    if (!jwtString) {\n        return done(new JsonWebTokenError(\"jwt must be provided\"));\n    }\n    if (typeof jwtString !== \"string\") {\n        return done(new JsonWebTokenError(\"jwt must be a string\"));\n    }\n    const parts = jwtString.split(\".\");\n    if (parts.length !== 3) {\n        return done(new JsonWebTokenError(\"jwt malformed\"));\n    }\n    let decodedToken;\n    try {\n        decodedToken = decode(jwtString, {\n            complete: true\n        });\n    } catch (err) {\n        return done(err);\n    }\n    if (!decodedToken) {\n        return done(new JsonWebTokenError(\"invalid token\"));\n    }\n    const header = decodedToken.header;\n    let getSecret;\n    if (typeof secretOrPublicKey === \"function\") {\n        if (!callback) {\n            return done(new JsonWebTokenError(\"verify must be called asynchronous if secret or public key is provided as a callback\"));\n        }\n        getSecret = secretOrPublicKey;\n    } else {\n        getSecret = function(header, secretCallback) {\n            return secretCallback(null, secretOrPublicKey);\n        };\n    }\n    return getSecret(header, function(err, secretOrPublicKey) {\n        if (err) {\n            return done(new JsonWebTokenError(\"error in secret or public key callback: \" + err.message));\n        }\n        const hasSignature = parts[2].trim() !== \"\";\n        if (!hasSignature && secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"jwt signature is required\"));\n        }\n        if (hasSignature && !secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"secret or public key must be provided\"));\n        }\n        if (!hasSignature && !options.algorithms) {\n            return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n        }\n        if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n            try {\n                secretOrPublicKey = createPublicKey(secretOrPublicKey);\n            } catch (_) {\n                try {\n                    secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === \"string\" ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n                } catch (_) {\n                    return done(new JsonWebTokenError(\"secretOrPublicKey is not valid key material\"));\n                }\n            }\n        }\n        if (!options.algorithms) {\n            if (secretOrPublicKey.type === \"secret\") {\n                options.algorithms = HS_ALGS;\n            } else if ([\n                \"rsa\",\n                \"rsa-pss\"\n            ].includes(secretOrPublicKey.asymmetricKeyType)) {\n                options.algorithms = RSA_KEY_ALGS;\n            } else if (secretOrPublicKey.asymmetricKeyType === \"ec\") {\n                options.algorithms = EC_KEY_ALGS;\n            } else {\n                options.algorithms = PUB_KEY_ALGS;\n            }\n        }\n        if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n            return done(new JsonWebTokenError(\"invalid algorithm\"));\n        }\n        if (header.alg.startsWith(\"HS\") && secretOrPublicKey.type !== \"secret\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be a symmetric key when using ${header.alg}`));\n        } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== \"public\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInvalidAsymmetricKeyTypes) {\n            try {\n                validateAsymmetricKey(header.alg, secretOrPublicKey);\n            } catch (e) {\n                return done(e);\n            }\n        }\n        let valid;\n        try {\n            valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n        } catch (e) {\n            return done(e);\n        }\n        if (!valid) {\n            return done(new JsonWebTokenError(\"invalid signature\"));\n        }\n        const payload = decodedToken.payload;\n        if (typeof payload.nbf !== \"undefined\" && !options.ignoreNotBefore) {\n            if (typeof payload.nbf !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid nbf value\"));\n            }\n            if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n                return done(new NotBeforeError(\"jwt not active\", new Date(payload.nbf * 1000)));\n            }\n        }\n        if (typeof payload.exp !== \"undefined\" && !options.ignoreExpiration) {\n            if (typeof payload.exp !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid exp value\"));\n            }\n            if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"jwt expired\", new Date(payload.exp * 1000)));\n            }\n        }\n        if (options.audience) {\n            const audiences = Array.isArray(options.audience) ? options.audience : [\n                options.audience\n            ];\n            const target = Array.isArray(payload.aud) ? payload.aud : [\n                payload.aud\n            ];\n            const match = target.some(function(targetAudience) {\n                return audiences.some(function(audience) {\n                    return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n                });\n            });\n            if (!match) {\n                return done(new JsonWebTokenError(\"jwt audience invalid. expected: \" + audiences.join(\" or \")));\n            }\n        }\n        if (options.issuer) {\n            const invalid_issuer = typeof options.issuer === \"string\" && payload.iss !== options.issuer || Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1;\n            if (invalid_issuer) {\n                return done(new JsonWebTokenError(\"jwt issuer invalid. expected: \" + options.issuer));\n            }\n        }\n        if (options.subject) {\n            if (payload.sub !== options.subject) {\n                return done(new JsonWebTokenError(\"jwt subject invalid. expected: \" + options.subject));\n            }\n        }\n        if (options.jwtid) {\n            if (payload.jti !== options.jwtid) {\n                return done(new JsonWebTokenError(\"jwt jwtid invalid. expected: \" + options.jwtid));\n            }\n        }\n        if (options.nonce) {\n            if (payload.nonce !== options.nonce) {\n                return done(new JsonWebTokenError(\"jwt nonce invalid. expected: \" + options.nonce));\n            }\n        }\n        if (options.maxAge) {\n            if (typeof payload.iat !== \"number\") {\n                return done(new JsonWebTokenError(\"iat required when maxAge is specified\"));\n            }\n            const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n            if (typeof maxAgeTimestamp === \"undefined\") {\n                return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n            }\n            if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"maxAge exceeded\", new Date(maxAgeTimestamp * 1000)));\n            }\n        }\n        if (options.complete === true) {\n            const signature = decodedToken.signature;\n            return done(null, {\n                header: header,\n                payload: payload,\n                signature: signature\n            });\n        }\n        return done(null, payload);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;