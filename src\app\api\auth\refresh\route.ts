/**
 * Token刷新API路由
 * 
 * POST /api/auth/refresh
 * 处理Token刷新请求，使用Refresh Token获取新的Access Token
 */

import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { RefreshTokenResponse } from '@/types/auth'
import { AuthErrorHandler, AuthErrorCode } from '@/services/auth/AuthErrorHandler'

/**
 * 处理Token刷新请求
 */
export async function POST(request: NextRequest) {
  try {
    // 从Cookie中获取Refresh Token
    const refreshToken = request.cookies.get('refresh-token')?.value

    if (!refreshToken) {
      AuthErrorHandler.logError(AuthErrorCode.TOKEN_NOT_FOUND, 'Refresh API')
      return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.TOKEN_NOT_FOUND, 'Refresh Token不存在')
    }

    console.log('🔍 [Refresh API] 开始验证Refresh Token')

    // 使用DataAccessManager进行Token验证
    const tokenValidation = await dataAccessManager.tokenManagement.verifyRefreshToken(refreshToken)
    if (!tokenValidation.isValid) {
      console.log('❌ [Refresh API] Refresh Token验证失败')
      return NextResponse.json({
        status: 'error',
        data: null,
        message: 'Refresh Token格式无效',
        code: 'TOKEN_INVALID',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as RefreshTokenResponse, { status: 401 })
    }

    // 从验证结果获取用户信息
    const payload = tokenValidation.payload
    if (!payload || !payload.userId || payload.type !== 'refresh') {
      console.log('❌ [Refresh API] Refresh Token payload无效')
      return NextResponse.json({
        status: 'error',
        data: null,
        message: 'Refresh Token格式无效',
        code: 'TOKEN_INVALID',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as RefreshTokenResponse, { status: 401 })
    }

    // 获取用户信息
    const userResult = await dataAccessManager.auth.getUserById(payload.userId)
    if (userResult.status !== 'success') {
      console.log('❌ [Refresh API] 用户不存在:', payload.userId)
      return NextResponse.json({
        status: 'error',
        data: null,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as RefreshTokenResponse, { status: 401 })
    }

    const user = userResult.data!

    // 检查用户状态
    if (user.status !== 'active') {
      console.log('❌ [Refresh API] 用户状态不正常:', user.status)
      return NextResponse.json({
        status: 'error',
        data: null,
        message: '用户账户状态异常',
        code: 'ACCOUNT_INACTIVE',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as RefreshTokenResponse, { status: 403 })
    }

    // 生成新的Token
    const newAccessToken = await dataAccessManager.tokenManagement.generateAccessToken({
      userId: user.id,
      username: user.username,
      roles: user.roles.map(role => role.code),
      permissions: user.roles.flatMap(role => role.permissions.map(perm => perm.code)),
      sessionId: payload.sessionId
    })

    const newRefreshToken = await dataAccessManager.tokenManagement.generateRefreshToken(user.id, payload.sessionId)

    const result = {
      status: 'success' as const,
      data: {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: 3600 // 1小时
      },
      message: 'Token刷新成功',
      code: 'REFRESH_SUCCESS',
      timestamp: new Date().toISOString(),
      requestId: generateRequestId()
    }

    // Token刷新成功，更新Cookie
    const response = NextResponse.json(result, { status: 200 })

    // 更新HttpOnly Cookie中的Token
    response.cookies.set('auth-token', result.data.accessToken, {
      httpOnly: true,
      secure: false, // 开发环境强制关闭secure，确保localhost可以正常设置cookies
      sameSite: 'lax',
      maxAge: 60 * 60, // 1小时
      path: '/'
    })

    response.cookies.set('refresh-token', result.data.refreshToken, {
      httpOnly: true,
      secure: false, // 开发环境强制关闭secure，确保localhost可以正常设置cookies
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7天
      path: '/'
    })

    console.log('✅ [Refresh API] Token刷新成功')
    return response
  } catch (error) {
    console.error('Token刷新API错误:', error)

    const response = NextResponse.json({
      status: 'error',
      data: null,
      message: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR',
      timestamp: new Date().toISOString(),
      requestId: generateRequestId()
    } as RefreshTokenResponse, { status: 500 })

    // 即使出错也清除Cookie
    response.cookies.delete('auth-token')
    response.cookies.delete('refresh-token')

    return response
  }
}

/**
 * 根据错误代码获取HTTP状态码
 */
function getStatusCodeFromErrorCode(code: string): number {
  switch (code) {
    case 'TOKEN_INVALID':
    case 'TOKEN_EXPIRED':
      return 401
    case 'SESSION_EXPIRED':
      return 401
    case 'ACCOUNT_INACTIVE':
      return 403
    case 'TOKEN_NOT_FOUND':
      return 400
    default:
      return 500
  }
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}
