# ERP系统部署指南

**版本**: 1.0  
**更新日期**: 2025年8月1日  
**适用环境**: 生产环境、测试环境

---

## 📋 **部署前准备**

### 系统要求

- **Node.js**: 18.0+ 
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 10GB 可用空间
- **网络**: 支持HTTPS的域名（生产环境）

### 环境变量配置

1. 复制环境变量模板：
```bash
cp .env.example .env.local
```

2. 配置必要的环境变量：
```bash
# 必须配置的变量
JWT_ACCESS_SECRET=生产环境专用密钥
JWT_REFRESH_SECRET=生产环境专用密钥
DB_HOST=数据库主机地址
DB_PASSWORD=数据库密码
```

---

## 🚀 **部署步骤**

### 1. 安装依赖

```bash
# 安装生产依赖
npm ci --only=production

# 或使用yarn
yarn install --production
```

### 2. 数据库初始化

```bash
# 运行数据库迁移
npm run db:migrate

# 初始化基础数据
npm run db:seed
```

### 3. 构建应用

```bash
# 构建生产版本
npm run build

# 验证构建结果
npm run start
```

### 4. 启动服务

```bash
# 使用PM2启动（推荐）
pm2 start ecosystem.config.js

# 或直接启动
npm run start
```

---

## 🔒 **安全配置**

### JWT密钥生成

```bash
# 生成强密钥
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### HTTPS配置

生产环境必须使用HTTPS：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 防火墙配置

```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

---

## 📊 **监控和日志**

### 应用监控

使用PM2监控应用状态：

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 监控面板
pm2 monit
```

### 日志配置

配置日志轮转：

```bash
# 安装logrotate
sudo apt-get install logrotate

# 配置日志轮转
sudo nano /etc/logrotate.d/erp-system
```

---

## 🔧 **维护操作**

### 应用更新

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm ci

# 重新构建
npm run build

# 重启应用
pm2 restart all
```

### 数据库备份

```bash
# 创建备份
mysqldump -u root -p erp_system > backup_$(date +%Y%m%d).sql

# 恢复备份
mysql -u root -p erp_system < backup_20250801.sql
```

### 性能优化

1. **启用Gzip压缩**
2. **配置CDN**
3. **数据库索引优化**
4. **缓存策略配置**

---

## 🚨 **故障排除**

### 常见问题

1. **应用无法启动**
   - 检查环境变量配置
   - 验证数据库连接
   - 查看错误日志

2. **认证失败**
   - 检查JWT密钥配置
   - 验证Token过期时间
   - 检查用户权限设置

3. **数据库连接失败**
   - 验证数据库服务状态
   - 检查连接参数
   - 确认网络连通性

### 日志查看

```bash
# 应用日志
pm2 logs erp-system

# 系统日志
sudo journalctl -u erp-system

# 数据库日志
sudo tail -f /var/log/mysql/error.log
```

---

## 📞 **技术支持**

如遇到部署问题，请提供以下信息：

1. 系统环境信息
2. 错误日志内容
3. 配置文件（隐藏敏感信息）
4. 复现步骤

---

**注意**: 生产环境部署前请务必进行充分测试，确保所有功能正常运行。
