# ERP系统部署配置指南

## 概述

本指南提供ERP系统认证和权限管理模块的完整部署配置说明，确保系统能够在生产环境中稳定运行。

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Windows Server 2019+
- **Node.js**: 18.0+
- **数据库**: MySQL 8.0+ / PostgreSQL 13+
- **Web服务器**: Nginx 1.18+ (推荐)

## 环境变量配置

### 1. 创建环境配置文件

创建 `.env.production` 文件：

```bash
# 应用配置
NODE_ENV=production
PORT=3000
APP_URL=https://your-domain.com

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=erp_system
DB_USER=erp_user
DB_PASSWORD=your_secure_password

# JWT配置
JWT_ACCESS_SECRET=your_very_long_and_secure_access_secret_key_here
JWT_REFRESH_SECRET=your_very_long_and_secure_refresh_secret_key_here

# 安全配置
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_key

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/erp/app.log

# 缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

### 2. 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `production` |
| `PORT` | 应用端口 | `3000` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `JWT_ACCESS_SECRET` | JWT访问密钥 | 64位随机字符串 |
| `BCRYPT_ROUNDS` | 密码加密轮数 | `12` |

## 数据库配置

### 1. MySQL数据库设置

#### 1.1 创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE erp_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'erp_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON erp_system.* TO 'erp_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 1.2 运行数据库迁移
```bash
# 运行基础表结构迁移
npm run db:migrate

# 运行认证系统初始化
npm run db:seed:auth

# 验证数据库结构
npm run db:verify
```

#### 1.3 初始化认证数据
```bash
# 运行认证系统初始化脚本
node scripts/verify-simple-auth-structure.js

# 创建默认管理员账户
npm run db:seed:admin
```

### 2. 数据库优化配置

#### 2.1 MySQL配置优化 (`/etc/mysql/mysql.conf.d/mysqld.cnf`)
```ini
[mysqld]
# 基础配置
max_connections = 100
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 性能优化
query_cache_type = 1
query_cache_size = 64M
tmp_table_size = 64M
max_heap_table_size = 64M

# 安全配置
bind-address = 127.0.0.1
skip-networking = false
```

## 应用部署

### 1. 代码部署

#### 1.1 克隆代码
```bash
# 克隆项目
git clone https://github.com/your-org/erp-system.git
cd erp-system

# 切换到生产分支
git checkout production
```

#### 1.2 安装依赖
```bash
# 安装生产依赖
npm ci --only=production

# 构建应用
npm run build
```

#### 1.3 配置文件
```bash
# 复制环境配置
cp .env.example .env.production

# 编辑配置文件
nano .env.production

# 设置文件权限
chmod 600 .env.production
```

### 2. PM2进程管理

#### 2.1 安装PM2
```bash
npm install -g pm2
```

#### 2.2 创建PM2配置文件 (`ecosystem.config.js`)
```javascript
module.exports = {
  apps: [{
    name: 'erp-system',
    script: './dist/server.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '/var/log/erp/combined.log',
    out_file: '/var/log/erp/out.log',
    error_file: '/var/log/erp/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
}
```

#### 2.3 启动应用
```bash
# 启动应用
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

## Web服务器配置

### 1. Nginx配置

#### 1.1 创建Nginx配置文件 (`/etc/nginx/sites-available/erp-system`)
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 静态文件
    location /static/ {
        alias /var/www/erp-system/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 主应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 日志配置
    access_log /var/log/nginx/erp-system.access.log;
    error_log /var/log/nginx/erp-system.error.log;
}
```

#### 1.2 启用配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/erp-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 安全配置

### 1. 防火墙设置

#### 1.1 UFW配置（Ubuntu）
```bash
# 启用UFW
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 拒绝直接访问应用端口
sudo ufw deny 3000

# 查看状态
sudo ufw status
```

### 2. SSL证书配置

#### 2.1 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 数据库安全

#### 3.1 MySQL安全配置
```bash
# 运行安全配置脚本
sudo mysql_secure_installation

# 配置项：
# - 设置root密码
# - 删除匿名用户
# - 禁止root远程登录
# - 删除test数据库
```

## 监控和日志

### 1. 日志配置

#### 1.1 创建日志目录
```bash
# 创建日志目录
sudo mkdir -p /var/log/erp
sudo chown $USER:$USER /var/log/erp

# 配置日志轮转
sudo nano /etc/logrotate.d/erp-system
```

#### 1.2 日志轮转配置
```
/var/log/erp/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 erp erp
    postrotate
        pm2 reload erp-system
    endscript
}
```

### 2. 系统监控

#### 2.1 PM2监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs erp-system

# 查看监控信息
pm2 monit
```

#### 2.2 系统资源监控
```bash
# 安装htop
sudo apt install htop

# 监控系统资源
htop

# 监控磁盘使用
df -h

# 监控内存使用
free -h
```

## 备份策略

### 1. 数据库备份

#### 1.1 自动备份脚本 (`backup-db.sh`)
```bash
#!/bin/bash

# 配置
DB_NAME="erp_system"
DB_USER="erp_user"
DB_PASSWORD="your_password"
BACKUP_DIR="/var/backups/erp"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u$DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/erp_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/erp_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: erp_backup_$DATE.sql.gz"
```

#### 1.2 设置定时备份
```bash
# 设置执行权限
chmod +x backup-db.sh

# 添加到crontab
crontab -e
# 添加：0 2 * * * /path/to/backup-db.sh
```

### 2. 应用备份

#### 2.1 代码备份
```bash
# 备份应用代码
tar -czf /var/backups/erp/app_backup_$(date +%Y%m%d).tar.gz /var/www/erp-system

# 备份配置文件
cp .env.production /var/backups/erp/env_backup_$(date +%Y%m%d)
```

## 部署检查清单

### 1. 部署前检查
- [ ] 环境变量配置完成
- [ ] 数据库连接测试通过
- [ ] SSL证书配置正确
- [ ] 防火墙规则设置
- [ ] 备份策略配置

### 2. 部署后验证
- [ ] 应用启动正常
- [ ] 数据库连接正常
- [ ] 登录功能测试
- [ ] 权限控制测试
- [ ] 性能测试通过
- [ ] 日志记录正常

### 3. 运行验证脚本
```bash
# 运行部署验证
npm run deploy:verify

# 运行健康检查
npm run health:check

# 运行性能测试
npm run test:performance
```

## 故障排除

### 1. 常见问题

#### 应用无法启动
```bash
# 检查日志
pm2 logs erp-system

# 检查端口占用
netstat -tulpn | grep 3000

# 检查环境变量
pm2 env 0
```

#### 数据库连接失败
```bash
# 测试数据库连接
mysql -u erp_user -p -h localhost erp_system

# 检查数据库状态
sudo systemctl status mysql
```

#### Nginx配置错误
```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx日志
sudo tail -f /var/log/nginx/error.log
```

### 2. 性能问题

#### 内存使用过高
```bash
# 重启应用
pm2 restart erp-system

# 调整内存限制
pm2 restart erp-system --max-memory-restart 512M
```

#### 数据库查询慢
```sql
-- 查看慢查询
SHOW PROCESSLIST;

-- 分析查询
EXPLAIN SELECT * FROM users WHERE username = 'admin';
```

---

## 联系支持

如遇到部署问题，请联系技术支持团队，提供以下信息：
- 系统环境信息
- 错误日志内容
- 配置文件内容（隐藏敏感信息）
- 问题复现步骤

---

*部署指南版本: v1.0*  
*最后更新: 2025年1月*
