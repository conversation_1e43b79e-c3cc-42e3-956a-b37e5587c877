/**
 * 简化认证服务测试
 * 
 * 测试PRD要求的极简版认证功能
 * 包括登录、token验证、权限检查等核心功能
 */

import { SimpleAuthService, SimpleUser, SimpleLoginResult } from '@/services/auth/SimpleAuthService'
import { SECURITY_CONFIG } from '@/config/security'

// Mock fetch
global.fetch = jest.fn()

// Mock dataAccessManager
jest.mock('@/services/dataAccess/DataAccessManager', () => ({
  dataAccessManager: {
    auth: {
      login: jest.fn(),
      getUserById: jest.fn()
    },
    tokenManagement: {
      generateAccessToken: jest.fn(),
      verifyAccessToken: jest.fn()
    }
  }
}))

describe('SimpleAuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // 清除console.log以减少测试输出
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('login', () => {
    it('应该成功登录管理员用户', async () => {
      // 模拟成功的登录响应
      const mockResponse = {
        status: 'success',
        data: {
          user: {
            id: 'user-admin',
            username: 'admin',
            roles: [{ code: 'admin', permissions: [] }],
            status: 'active'
          },
          sessionId: 'session-123'
        }
      }

      const mockToken = 'mock-jwt-token'

      // Mock API调用
      ;(fetch as jest.Mock)
        .mockResolvedValueOnce({
          json: () => Promise.resolve(mockResponse)
        })

      // Mock token生成
      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          auth: {
            login: jest.fn().mockResolvedValue({
              status: 'success',
              data: mockResponse.data
            })
          },
          tokenManagement: {
            generateAccessToken: jest.fn().mockResolvedValue(mockToken)
          }
        })

      const result = await SimpleAuthService.login('admin', 'admin123')

      expect(result).toEqual({
        user: {
          id: 'user-admin',
          username: 'admin',
          role: 'admin'
        },
        token: mockToken
      })
    })

    it('应该成功登录员工用户', async () => {
      const mockResponse = {
        status: 'success',
        data: {
          user: {
            id: 'user-employee',
            username: 'employee',
            roles: [{ code: 'employee', permissions: [] }],
            status: 'active'
          },
          sessionId: 'session-456'
        }
      }

      const mockToken = 'mock-jwt-token-employee'

      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          auth: {
            login: jest.fn().mockResolvedValue({
              status: 'success',
              data: mockResponse.data
            })
          },
          tokenManagement: {
            generateAccessToken: jest.fn().mockResolvedValue(mockToken)
          }
        })

      const result = await SimpleAuthService.login('employee', 'employee123')

      expect(result.user.role).toBe('employee')
      expect(result.token).toBe(mockToken)
    })

    it('应该在用户名或密码错误时抛出异常', async () => {
      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          auth: {
            login: jest.fn().mockResolvedValue({
              status: 'error',
              message: '用户名或密码错误'
            })
          }
        })

      await expect(SimpleAuthService.login('invalid', 'invalid'))
        .rejects.toThrow('用户名或密码错误')
    })

    it('应该在用户被禁用时抛出异常', async () => {
      const mockResponse = {
        status: 'success',
        data: {
          user: {
            id: 'user-disabled',
            username: 'disabled',
            roles: [{ code: 'employee', permissions: [] }],
            status: 'inactive'
          },
          sessionId: 'session-789'
        }
      }

      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          auth: {
            login: jest.fn().mockResolvedValue({
              status: 'success',
              data: mockResponse.data
            })
          }
        })

      await expect(SimpleAuthService.login('disabled', 'password'))
        .rejects.toThrow('用户不存在或已被禁用')
    })
  })

  describe('verifyToken', () => {
    it('应该成功验证有效token', async () => {
      const mockPayload = {
        userId: 'user-admin',
        username: 'admin'
      }

      const mockUser = {
        id: 'user-admin',
        username: 'admin',
        roles: [{ code: 'admin', permissions: [] }],
        status: 'active'
      }

      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          tokenManagement: {
            verifyAccessToken: jest.fn().mockResolvedValue({
              isValid: true,
              payload: mockPayload
            })
          },
          auth: {
            getUserById: jest.fn().mockResolvedValue({
              status: 'success',
              data: mockUser
            })
          }
        })

      const result = await SimpleAuthService.verifyToken('valid-token')

      expect(result).toEqual({
        id: 'user-admin',
        username: 'admin',
        role: 'admin'
      })
    })

    it('应该在token无效时返回null', async () => {
      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          tokenManagement: {
            verifyAccessToken: jest.fn().mockResolvedValue({
              isValid: false,
              payload: null
            })
          }
        })

      const result = await SimpleAuthService.verifyToken('invalid-token')

      expect(result).toBeNull()
    })

    it('应该在用户不存在时返回null', async () => {
      const mockPayload = {
        userId: 'non-existent-user',
        username: 'nonexistent'
      }

      jest.spyOn(require('@/services/dataAccess/DataAccessManager'), 'dataAccessManager', 'get')
        .mockReturnValue({
          tokenManagement: {
            verifyAccessToken: jest.fn().mockResolvedValue({
              isValid: true,
              payload: mockPayload
            })
          },
          auth: {
            getUserById: jest.fn().mockResolvedValue({
              status: 'error',
              data: null
            })
          }
        })

      const result = await SimpleAuthService.verifyToken('valid-token')

      expect(result).toBeNull()
    })
  })

  describe('hasPermission', () => {
    it('管理员应该拥有所有权限', () => {
      expect(SimpleAuthService.hasPermission('admin', 'admin:users:create')).toBe(true)
      expect(SimpleAuthService.hasPermission('admin', 'admin:system:config')).toBe(true)
      expect(SimpleAuthService.hasPermission('admin', 'data:delete')).toBe(true)
      expect(SimpleAuthService.hasPermission('admin', 'any:permission')).toBe(true)
    })

    it('员工应该只有基础权限', () => {
      expect(SimpleAuthService.hasPermission('employee', 'data:view')).toBe(true)
      expect(SimpleAuthService.hasPermission('employee', 'data:edit')).toBe(true)
      expect(SimpleAuthService.hasPermission('employee', 'orders:create')).toBe(true)
      
      // 员工不应该有管理员权限
      expect(SimpleAuthService.hasPermission('employee', 'admin:users:create')).toBe(false)
      expect(SimpleAuthService.hasPermission('employee', 'admin:system:config')).toBe(false)
      expect(SimpleAuthService.hasPermission('employee', 'data:delete')).toBe(false)
    })
  })

  describe('validatePasswordStrength', () => {
    it('应该接受符合要求的密码', () => {
      const result = SimpleAuthService.validatePasswordStrength('password123')
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝过短的密码', () => {
      const result = SimpleAuthService.validatePasswordStrength('123')
      
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain(`密码长度至少${SECURITY_CONFIG.password.minLength}位`)
    })

    it('应该接受最小长度的密码', () => {
      const minPassword = 'a'.repeat(SECURITY_CONFIG.password.minLength)
      const result = SimpleAuthService.validatePasswordStrength(minPassword)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('getSecurityConfig', () => {
    it('应该返回正确的安全配置', () => {
      const config = SimpleAuthService.getSecurityConfig()
      
      expect(config).toHaveProperty('passwordPolicy')
      expect(config).toHaveProperty('sessionDuration')
      expect(config).toHaveProperty('maxLoginAttempts')
      expect(config).toHaveProperty('lockoutDuration')
      
      expect(config.sessionDuration).toBe(8 * 60 * 60) // 8小时
      expect(config.passwordPolicy.minLength).toBe(SECURITY_CONFIG.password.minLength)
    })
  })

  describe('isAdmin', () => {
    it('应该正确识别管理员用户', () => {
      const adminUser: SimpleUser = {
        id: 'admin-1',
        username: 'admin',
        role: 'admin'
      }
      
      expect(SimpleAuthService.isAdmin(adminUser)).toBe(true)
    })

    it('应该正确识别非管理员用户', () => {
      const employeeUser: SimpleUser = {
        id: 'employee-1',
        username: 'employee',
        role: 'employee'
      }
      
      expect(SimpleAuthService.isAdmin(employeeUser)).toBe(false)
    })
  })

  describe('isEmployee', () => {
    it('应该正确识别员工用户', () => {
      const employeeUser: SimpleUser = {
        id: 'employee-1',
        username: 'employee',
        role: 'employee'
      }
      
      expect(SimpleAuthService.isEmployee(employeeUser)).toBe(true)
    })

    it('管理员也应该被识别为员工（权限包含关系）', () => {
      const adminUser: SimpleUser = {
        id: 'admin-1',
        username: 'admin',
        role: 'admin'
      }
      
      // 注意：这个测试可能需要根据具体的业务逻辑调整
      // 如果管理员不应该被识别为员工，则修改这个测试
      expect(SimpleAuthService.isEmployee(adminUser)).toBe(false)
    })
  })
})
