/**
 * 用户登录API路由
 * 
 * POST /api/auth/login
 * 处理用户登录请求，验证凭据并返回JWT Token
 */

import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { LoginRequest, LoginResponse } from '@/types/auth'
import { AuthErrorHandler, AuthErrorCode } from '@/services/auth/AuthErrorHandler'

/**
 * 处理用户登录请求
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [Auth API] 开始处理登录请求')
    
    // 解析请求体
    const body = await request.json()
    console.log('📋 [Auth API] 请求体解析完成:', { username: body.username, hasPassword: !!body.password })
    const { username, password, rememberMe } = body as LoginRequest

    // 验证请求参数
    if (!username || !password) {
      AuthErrorHandler.logError(AuthErrorCode.INVALID_REQUEST, '登录API', { username: !!username, password: !!password })
      return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.INVALID_REQUEST, '用户名和密码不能为空')
    }

    console.log('✅ [Auth API] 请求参数验证通过，准备调用认证服务')
    console.log('🔍 [Auth API] dataAccessManager是否存在:', !!dataAccessManager)
    console.log('🔍 [Auth API] dataAccessManager.auth是否存在:', !!dataAccessManager?.auth)
    console.log('🔍 [Auth API] dataAccessManager.auth.login是否存在:', !!dataAccessManager?.auth?.login)

    // 调用认证服务进行登录
    console.log('📞 [Auth API] 开始调用 dataAccessManager.auth.login()')
    const result = await dataAccessManager.auth.login({
      username,
      password,
      rememberMe
    })
    console.log('📋 [Auth API] 认证服务调用完成，结果状态:', result.status)

    // 根据登录结果返回响应
    if (result.status === 'success') {
      console.log('✅ [Auth API] 认证服务验证成功，开始生成JWT Token')
      
      try {
        // 在服务器端生成JWT Token
        const { user, sessionId, rememberMe } = result.data!
        
        const accessToken = await dataAccessManager.tokenManagement.generateAccessToken({
          userId: user.id,
          username: user.username,
          roles: user.roles.map(role => role.code),
          permissions: user.roles.flatMap(role => role.permissions.map(perm => perm.code)),
          sessionId: sessionId!
        })

        const refreshToken = await dataAccessManager.tokenManagement.generateRefreshToken(user.id, sessionId!)

        // 创建完整的登录结果
        const loginResult = {
          ...result.data,
          accessToken,
          refreshToken
        }

        // 登录成功，设置Cookie
        const response = NextResponse.json({
          ...result,
          data: loginResult
        }, { status: 200 })
        
        // 设置HttpOnly Cookie存储Token
        console.log('🍪 [Auth API] 开始设置Cookie, NODE_ENV:', process.env.NODE_ENV)
        console.log('🍪 [Auth API] secure设置为:', process.env.NODE_ENV === 'production')
        
        response.cookies.set('auth-token', accessToken, {
          httpOnly: true,
          secure: false, // 开发环境强制关闭secure，确保localhost可以正常设置cookies
          sameSite: 'lax',
          maxAge: rememberMe ? 7 * 24 * 60 * 60 : 8 * 60 * 60, // 记住我：7天，否则8小时
          path: '/',
        })

        response.cookies.set('refresh-token', refreshToken, {
          httpOnly: true,
          secure: false, // 开发环境强制关闭secure，确保localhost可以正常设置cookies
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60, // 7天
          path: '/',
        })
        
        console.log('🍪 [Auth API] Cookie设置完成')

        console.log(`✅ [Auth API] 用户登录成功，JWT Token已生成: ${user.username}`)
        return response
      } catch (tokenError) {
        AuthErrorHandler.logError(AuthErrorCode.TOKEN_GENERATION_ERROR, '登录API Token生成', tokenError)
        return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.TOKEN_GENERATION_ERROR)
      }
    } else {
      // 登录失败
      console.log(`❌ [Auth API] 用户登录失败: ${username}, 原因: ${result.message}`)
      return NextResponse.json(result, { 
        status: getStatusCodeFromErrorCode(result.code) 
      })
    }
  } catch (error) {
    const sanitizedError = AuthErrorHandler.sanitizeLogData(error)
    AuthErrorHandler.logError(AuthErrorCode.INTERNAL_SERVER_ERROR, '登录API异常', sanitizedError)
    return AuthErrorHandler.createNextErrorResponse(AuthErrorCode.INTERNAL_SERVER_ERROR)
  }
}


