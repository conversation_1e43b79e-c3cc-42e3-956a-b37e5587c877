/**
 * 事件监听器Hook - 架构合规版
 * ✅ 使用DataAccessManager统一监控，移除违规的独立MemoryManager
 */

import { useEffect, useRef, useState } from 'react'
import { dataChangeNotifier, DataChangeListener } from '@/services/dataAccess/DataChangeNotifier'
import { useDataAccessMonitor } from '@/hooks/useDataAccessMonitor'
// 🔧 P5-3事件通信错误处理优化：导入新的事件管理器
import EventListenerManager from '@/services/events/EventListenerManager'
// EventErrorHandler模块不存在，已移除导入
import { ListenerRegistrationConfig } from '@/services/events/types'

// 🔧 P5-3事件通信错误处理优化：创建全局事件管理器实例
const globalEventListenerManager = new EventListenerManager()

/**
 * 数据变更事件监听器Hook (P5-3优化版本)
 * @param listenerId 监听器唯一标识
 * @param listener 监听器回调函数
 * @param options 监听器选项
 */
export const useDataChangeListener = (
  listenerId: string,
  listener: DataChangeListener,
  options?: {
    autoCleanup?: boolean // 是否自动清理，默认true
    trackMemory?: boolean // 是否追踪内存，默认true
    useNewManager?: boolean // 是否使用新的事件管理器，默认false（保持向后兼容）
    priority?: number // 监听器优先级 (0-10)
    timeout?: number // 监听器超时时间 (毫秒)
  }
) => {
  // ✅ 架构合规：使用DataAccessManager统一监控
  const { isMonitoring } = useDataAccessMonitor({
    interval: 60000,
    enabled: process.env.NODE_ENV === 'development'
  })
  
  const listenerRef = useRef<DataChangeListener | null>(null)
  const opts = {
    autoCleanup: true,
    trackMemory: true,
    useNewManager: false, // 默认使用旧的管理器保持兼容性
    priority: 5,
    timeout: 5000,
    ...options
  }

  useEffect(() => {
    // 保存监听器引用
    listenerRef.current = listener

    // 🔧 P5-3事件通信错误处理优化：支持新的事件管理器
    if (opts.useNewManager) {
      // 使用新的事件管理器
      const config: ListenerRegistrationConfig = {
        id: listenerId,
        eventTypes: [
          'order_created', 'order_updated', 'order_deleted',
          'production_order_created', 'production_order_updated', 'production_order_deleted',
          'production_work_order_created', 'production_work_order_updated', 'production_work_order_deleted'
        ],
        callback: async (eventData: any) => {
          // 将新格式的事件转换为旧格式的监听器调用
          const eventType = eventData.type || eventData.eventType
          switch (eventType) {
            case 'order_created':
              listener.onOrderCreated?.(eventData.data)
              break
            case 'order_updated':
              listener.onOrderUpdated?.(eventData.data)
              break
            case 'order_deleted':
              listener.onOrderDeleted?.(eventData.data)
              break
            case 'production_order_created':
              listener.onProductionOrderCreated?.(eventData.data)
              break
            case 'production_order_updated':
              listener.onProductionOrderUpdated?.(eventData.data)
              break
            case 'production_order_deleted':
              listener.onProductionOrderDeleted?.(eventData.data)
              break
            case 'production_work_order_created':
              listener.onProductionWorkOrderCreated?.(eventData.data)
              break
            case 'production_work_order_updated':
              listener.onProductionWorkOrderUpdated?.(eventData.data)
              break
            case 'production_work_order_deleted':
              listener.onProductionWorkOrderDeleted?.(eventData.data)
              break
          }
        },
        priority: opts.priority,
        enabled: true,
        timeout: opts.timeout
      }

      globalEventListenerManager.registerListener(config)

      // 追踪内存资源 - 架构合规版
      if (opts.trackMemory && isMonitoring) {
        console.log(`Event listener registered: ${listenerId}`, { 
          manager: 'new', 
          priority: opts.priority,
          timeout: opts.timeout
        })
      }

      return () => {
        if (opts.autoCleanup) {
          globalEventListenerManager.unregisterListener(listenerId)
        }
        listenerRef.current = null
      }
    } else {
      // 使用旧的事件管理器（保持向后兼容）
      dataChangeNotifier.registerListener(listenerId, listener)

      // 追踪内存资源 - 架构合规版
      if (opts.trackMemory && isMonitoring) {
        console.log(`Event listener registered: ${listenerId}`, { 
          manager: 'legacy'
        })
      }

      return () => {
        if (opts.autoCleanup) {
          dataChangeNotifier.unregisterListener(listenerId)
        }
        listenerRef.current = null
      }
    }
  }, [listenerId, opts.autoCleanup, opts.trackMemory, opts.useNewManager, opts.priority, opts.timeout, isMonitoring])
  // ✅ 架构合规修复：移除listener依赖，避免监听器泄漏

  // 组件卸载时确保清理
  useEffect(() => {
    return () => {
      if (listenerRef.current) {
        dataChangeNotifier.unregisterListener(listenerId)
        listenerRef.current = null
      }
    }
  }, [listenerId])
}

/**
 * 增强的数据变更事件监听器Hook (P5-3新版本)
 * 使用新的事件管理器，提供更好的错误处理和监控
 * @param listenerId 监听器唯一标识
 * @param listener 监听器回调函数
 * @param options 监听器选项
 */
export const useEnhancedDataChangeListener = (
  listenerId: string,
  listener: DataChangeListener,
  options?: {
    priority?: number // 监听器优先级 (0-10)，默认5
    timeout?: number // 监听器超时时间 (毫秒)，默认5000
    trackMemory?: boolean // 是否追踪内存，默认true
    autoCleanup?: boolean // 是否自动清理，默认true
  }
) => {
  const opts = {
    priority: 5,
    timeout: 5000,
    trackMemory: true,
    autoCleanup: true,
    ...options
  }

  return useDataChangeListener(listenerId, listener, {
    ...opts,
    useNewManager: true // 强制使用新的事件管理器
  })
}

/**
 * DOM事件监听器Hook
 * @param element 目标元素或元素引用
 * @param eventType 事件类型
 * @param handler 事件处理函数
 * @param options 事件选项
 */
export const useDOMEventListener = <T extends HTMLElement = HTMLElement>(
  element: T | React.RefObject<T> | Window | Document | null,
  eventType: string,
  handler: (event: Event) => void,
  options?: AddEventListenerOptions & {
    trackMemory?: boolean // 是否追踪内存，默认true
  }
) => {
  // ✅ 架构合规：使用DataAccessManager统一监控
  const { isMonitoring } = useDataAccessMonitor({
    interval: 60000,
    enabled: process.env.NODE_ENV === 'development'
  })
  
  const handlerRef = useRef<(event: Event) => void>()
  const opts = {
    trackMemory: true,
    ...options
  }

  useEffect(() => {
    // 获取实际的DOM元素
    const targetElement = element && 'current' in element ? element.current : element
    if (!targetElement) return

    // 保存处理函数引用
    handlerRef.current = handler

    // 添加事件监听器
    targetElement.addEventListener(eventType, handler, options)

    // 追踪内存资源 - 架构合规版
    if (opts.trackMemory && isMonitoring) {
      console.log(`DOM event listener added: ${eventType}`)
    }
    
    // 不追踪内存时的简单清理
    return () => {
      if (targetElement && handlerRef.current) {
        targetElement.removeEventListener(eventType, handlerRef.current, options)
      }
      handlerRef.current = undefined
    }
  }, [element, eventType, handler, opts.trackMemory, isMonitoring])
}

/**
 * 窗口事件监听器Hook
 * @param eventType 事件类型
 * @param handler 事件处理函数
 * @param options 事件选项
 */
export const useWindowEventListener = (
  eventType: keyof WindowEventMap,
  handler: (event: WindowEventMap[keyof WindowEventMap]) => void,
  options?: AddEventListenerOptions & {
    trackMemory?: boolean
  }
) => {
  return useDOMEventListener(
    typeof window !== 'undefined' ? window : null,
    eventType,
    handler as (event: Event) => void,
    options
  )
}

/**
 * 文档事件监听器Hook
 * @param eventType 事件类型
 * @param handler 事件处理函数
 * @param options 事件选项
 */
export const useDocumentEventListener = (
  eventType: keyof DocumentEventMap,
  handler: (event: DocumentEventMap[keyof DocumentEventMap]) => void,
  options?: AddEventListenerOptions & {
    trackMemory?: boolean
  }
) => {
  return useDOMEventListener(
    typeof document !== 'undefined' ? document : null,
    eventType,
    handler as (event: Event) => void,
    options
  )
}

/**
 * 键盘事件监听器Hook
 * @param key 目标按键
 * @param handler 事件处理函数
 * @param options 事件选项
 */
export const useKeyboardListener = (
  key: string | string[],
  handler: (event: KeyboardEvent) => void,
  options?: {
    target?: 'window' | 'document' | React.RefObject<HTMLElement>
    preventDefault?: boolean
    stopPropagation?: boolean
    trackMemory?: boolean
  }
) => {
  const opts = {
    target: 'window' as const,
    preventDefault: false,
    stopPropagation: false,
    trackMemory: true,
    ...options
  }

  const keyHandler = (event: KeyboardEvent) => {
    const keys = Array.isArray(key) ? key : [key]
    
    if (keys.includes(event.key) || keys.includes(event.code)) {
      if (opts.preventDefault) {
        event.preventDefault()
      }
      if (opts.stopPropagation) {
        event.stopPropagation()
      }
      handler(event)
    }
  }

  // 使用useEffect来避免条件调用Hook的问题
  useEffect(() => {
    if (opts.target === 'window') {
      const handleKeyDown = (event: KeyboardEvent) => keyHandler(event)
      window.addEventListener('keydown', handleKeyDown)
      return () => window.removeEventListener('keydown', handleKeyDown)
    } else if (opts.target === 'document') {
      const handleKeyDown = (event: KeyboardEvent) => keyHandler(event)
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    } else if (opts.target && typeof opts.target === 'object' && 'current' in opts.target && opts.target.current) {
      const handleKeyDown = (event: KeyboardEvent) => keyHandler(event)
      const element = opts.target.current
      element.addEventListener('keydown', handleKeyDown)
      return () => element.removeEventListener('keydown', handleKeyDown)
    }
  }, [keyHandler, opts.target, opts.trackMemory])
}

/**
 * 鼠标点击外部监听器Hook
 * @param ref 目标元素引用
 * @param handler 点击外部时的处理函数
 * @param options 选项
 */
export const useClickOutside = <T extends HTMLElement = HTMLElement>(
  ref: React.RefObject<T>,
  handler: (event: MouseEvent) => void,
  options?: {
    trackMemory?: boolean
  }
) => {
  const opts = {
    trackMemory: true,
    ...options
  }

  const clickHandler = (event: MouseEvent) => {
    if (ref.current && !ref.current.contains(event.target as Node)) {
      handler(event)
    }
  }

  useDocumentEventListener('mousedown', clickHandler as any, { trackMemory: opts.trackMemory })
}

/**
 * Hook使用示例和最佳实践
 * 
 * @example
 * // 数据变更监听
 * useDataChangeListener('my-component', {
 *   onProductionOrderCreated: (order) => {
 *     console.log('新订单创建:', order)
 *   },
 *   onProductionOrderUpdated: (order) => {
 *     console.log('订单更新:', order)
 *   }
 * })
 * 
 * @example
 * // DOM事件监听
 * const buttonRef = useRef<HTMLButtonElement>(null)
 * useDOMEventListener(buttonRef, 'click', (event) => {
 *   console.log('按钮被点击')
 * })
 * 
 * @example
 * // 键盘事件监听
 * useKeyboardListener(['Escape', 'Enter'], (event) => {
 *   if (event.key === 'Escape') {
 *     closeModal()
 *   } else if (event.key === 'Enter') {
 *     submitForm()
 *   }
 * })
 * 
 * @example
 * // 点击外部监听
 * const modalRef = useRef<HTMLDivElement>(null)
 * useClickOutside(modalRef, () => {
 *   closeModal()
 * })
 */

/**
 * 获取事件通信调试信息的Hook
 */
export const useEventCommunicationDebug = () => {
  const [debugInfo, setDebugInfo] = useState<any>(null)

  useEffect(() => {
    const updateDebugInfo = () => {
      const info = globalEventListenerManager.getDebugInfo()
      setDebugInfo(info)
    }

    // 立即更新一次
    updateDebugInfo()

    // 每5秒更新一次调试信息
    const interval = setInterval(updateDebugInfo, 5000)

    return () => clearInterval(interval)
  }, [])

  return {
    debugInfo,
    listenerManager: globalEventListenerManager,
    // errorHandler已移除（模块不存在）
    // 手动刷新调试信息
    refresh: () => {
      const info = globalEventListenerManager.getDebugInfo()
      setDebugInfo(info)
    }
  }
}

export default useDataChangeListener
