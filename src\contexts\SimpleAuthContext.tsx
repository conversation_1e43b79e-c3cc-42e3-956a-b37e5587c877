/**
 * 简化认证上下文
 * 
 * 根据PRD要求，提供极简的认证状态管理
 * 专为admin/employee两级权限设计，简单易用
 */

'use client'

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { message } from 'antd'
import { SimpleAuthService, SimpleUser, SimpleLoginResult } from '@/services/auth/SimpleAuthService'
import { SECURITY_CONFIG } from '@/config/security'

/**
 * 简化认证上下文接口
 */
export interface SimpleAuthContextType {
  /** 当前用户信息 */
  user: SimpleUser | null
  /** 是否已认证 */
  isAuthenticated: boolean
  /** 是否正在加载 */
  isLoading: boolean
  /** 登录函数 */
  login: (username: string, password: string) => Promise<void>
  /** 登出函数 */
  logout: () => void
  /** 检查权限 */
  hasPermission: (permission: string) => boolean
  /** 检查角色 */
  hasRole: (role: 'admin' | 'employee') => boolean
  /** 是否为管理员 */
  isAdmin: boolean
  /** 是否为员工 */
  isEmployee: boolean
}

/**
 * 创建简化认证上下文
 */
const SimpleAuthContext = createContext<SimpleAuthContextType | undefined>(undefined)

/**
 * 简化认证提供者属性接口
 */
interface SimpleAuthProviderProps {
  children: React.ReactNode
}

/**
 * 简化认证提供者组件
 */
export const SimpleAuthProvider: React.FC<SimpleAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<SimpleUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  // 公开路径列表
  const publicPaths = ['/login', '/forgot-password', '/403', '/404']

  /**
   * 检查是否为公开路径
   */
  const isPublicPath = useCallback((path: string): boolean => {
    return publicPaths.some(publicPath => path.startsWith(publicPath))
  }, [])

  /**
   * 从本地存储获取token
   */
  const getStoredToken = (): string | null => {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('auth_token')
  }

  /**
   * 保存token到本地存储
   */
  const setStoredToken = (token: string): void => {
    if (typeof window === 'undefined') return
    localStorage.setItem('auth_token', token)
  }

  /**
   * 清除本地存储的token
   */
  const clearStoredToken = (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem('auth_token')
  }

  /**
   * 验证并设置用户信息
   */
  const verifyAndSetUser = useCallback(async (token: string): Promise<boolean> => {
    try {
      console.log('🔍 [SimpleAuth] 验证token并获取用户信息')
      
      const user = await SimpleAuthService.verifyToken(token)
      
      if (user) {
        setUser(user)
        console.log('✅ [SimpleAuth] 用户验证成功:', user.username)
        return true
      } else {
        console.log('❌ [SimpleAuth] Token验证失败')
        clearStoredToken()
        setUser(null)
        return false
      }
    } catch (error) {
      console.error('❌ [SimpleAuth] Token验证异常:', error)
      clearStoredToken()
      setUser(null)
      return false
    }
  }, [])

  /**
   * 初始化认证状态
   */
  useEffect(() => {
    const initAuth = async () => {
      console.log('🔄 [SimpleAuth] 初始化认证状态, 路径:', pathname)
      setIsLoading(true)

      // 如果是公开路径，直接完成加载
      if (isPublicPath(pathname)) {
        console.log('📖 [SimpleAuth] 公开路径，跳过认证检查')
        setIsLoading(false)
        return
      }

      // 检查本地存储的token
      const token = getStoredToken()
      
      if (token) {
        console.log('🔍 [SimpleAuth] 发现本地token，开始验证')
        const isValid = await verifyAndSetUser(token)
        
        if (!isValid) {
          console.log('❌ [SimpleAuth] Token无效，跳转到登录页')
          router.push('/login')
        }
      } else {
        console.log('❌ [SimpleAuth] 未找到token，跳转到登录页')
        router.push('/login')
      }

      setIsLoading(false)
    }

    initAuth()
  }, [pathname, router, isPublicPath, verifyAndSetUser])

  /**
   * 登录函数
   */
  const login = useCallback(async (username: string, password: string): Promise<void> => {
    try {
      setIsLoading(true)
      console.log('🔐 [SimpleAuth] 开始登录:', username)

      const result: SimpleLoginResult = await SimpleAuthService.login(username, password)
      
      // 保存token和用户信息
      setStoredToken(result.token)
      setUser(result.user)
      
      console.log('✅ [SimpleAuth] 登录成功:', result.user.username)
      message.success('登录成功')
      
      // 跳转到仪表盘
      router.push('/dashboard')
      
    } catch (error) {
      console.error('❌ [SimpleAuth] 登录失败:', error)
      const errorMessage = error instanceof Error ? error.message : '登录失败，请重试'
      message.error(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [router])

  /**
   * 登出函数
   */
  const logout = useCallback((): void => {
    console.log('🚪 [SimpleAuth] 用户登出')
    
    // 清除本地状态和存储
    clearStoredToken()
    setUser(null)
    
    message.success('已安全退出')
    
    // 跳转到登录页
    router.push('/login')
  }, [router])

  /**
   * 权限检查函数
   */
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user) return false
    return SimpleAuthService.hasPermission(user.role, permission)
  }, [user])

  /**
   * 角色检查函数
   */
  const hasRole = useCallback((role: 'admin' | 'employee'): boolean => {
    if (!user) return false
    
    if (role === 'admin') {
      return user.role === 'admin'
    }
    
    // employee权限：admin和employee都可以访问
    return ['admin', 'employee'].includes(user.role)
  }, [user])

  // 计算衍生状态
  const isAuthenticated = !!user
  const isAdmin = user?.role === 'admin'
  const isEmployee = user?.role === 'employee' || isAdmin

  /**
   * 上下文值
   */
  const contextValue: SimpleAuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    isAdmin,
    isEmployee
  }

  return (
    <SimpleAuthContext.Provider value={contextValue}>
      {children}
    </SimpleAuthContext.Provider>
  )
}

/**
 * 使用简化认证上下文的Hook
 */
export const useSimpleAuth = (): SimpleAuthContextType => {
  const context = useContext(SimpleAuthContext)
  
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider')
  }
  
  return context
}

/**
 * 简化的权限检查Hook
 */
export const useSimplePermissionCheck = () => {
  const { hasPermission } = useSimpleAuth()
  return hasPermission
}

/**
 * 简化的角色检查Hook
 */
export const useSimpleRoleCheck = () => {
  const { hasRole, isAdmin, isEmployee } = useSimpleAuth()
  
  return {
    hasRole,
    isAdmin,
    isEmployee
  }
}

/**
 * 简化的用户信息Hook
 */
export const useSimpleUser = () => {
  const { user, isAuthenticated } = useSimpleAuth()
  
  return {
    user,
    isAuthenticated,
    username: user?.username,
    role: user?.role,
    isAdmin: user?.role === 'admin',
    isEmployee: user?.role === 'employee' || user?.role === 'admin'
  }
}

export default SimpleAuthProvider
