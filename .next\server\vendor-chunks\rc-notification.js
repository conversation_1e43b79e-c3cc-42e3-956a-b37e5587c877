"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-notification";
exports.ids = ["vendor-chunks/rc-notification"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-notification/es/Notice.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-notification/es/Notice.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/pickAttrs */ \"(ssr)/./node_modules/rc-util/es/pickAttrs.js\");\n\n\n\n\n\n\n\n\nvar Notify = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6__.forwardRef(function(props, ref) {\n    var prefixCls = props.prefixCls, style = props.style, className = props.className, _props$duration = props.duration, duration = _props$duration === void 0 ? 4.5 : _props$duration, showProgress = props.showProgress, _props$pauseOnHover = props.pauseOnHover, pauseOnHover = _props$pauseOnHover === void 0 ? true : _props$pauseOnHover, eventKey = props.eventKey, content = props.content, closable = props.closable, _props$closeIcon = props.closeIcon, closeIcon = _props$closeIcon === void 0 ? \"x\" : _props$closeIcon, divProps = props.props, onClick = props.onClick, onNoticeClose = props.onNoticeClose, times = props.times, forcedHovering = props.hovering;\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_6__.useState(false), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState, 2), hovering = _React$useState2[0], setHovering = _React$useState2[1];\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState3, 2), percent = _React$useState4[0], setPercent = _React$useState4[1];\n    var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_6__.useState(0), _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_React$useState5, 2), spentTime = _React$useState6[0], setSpentTime = _React$useState6[1];\n    var mergedHovering = forcedHovering || hovering;\n    var mergedShowProgress = duration > 0 && showProgress;\n    // ======================== Close =========================\n    var onInternalClose = function onInternalClose() {\n        onNoticeClose(eventKey);\n    };\n    var onCloseKeyDown = function onCloseKeyDown(e) {\n        if (e.key === \"Enter\" || e.code === \"Enter\" || e.keyCode === rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_5__[\"default\"].ENTER) {\n            onInternalClose();\n        }\n    };\n    // ======================== Effect ========================\n    react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function() {\n        if (!mergedHovering && duration > 0) {\n            var start = Date.now() - spentTime;\n            var timeout = setTimeout(function() {\n                onInternalClose();\n            }, duration * 1000 - spentTime);\n            return function() {\n                if (pauseOnHover) {\n                    clearTimeout(timeout);\n                }\n                setSpentTime(Date.now() - start);\n            };\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        duration,\n        mergedHovering,\n        times\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_6__.useEffect(function() {\n        if (!mergedHovering && mergedShowProgress && (pauseOnHover || spentTime === 0)) {\n            var start = performance.now();\n            var animationFrame;\n            var calculate = function calculate() {\n                cancelAnimationFrame(animationFrame);\n                animationFrame = requestAnimationFrame(function(timestamp) {\n                    var runtime = timestamp + spentTime - start;\n                    var progress = Math.min(runtime / (duration * 1000), 1);\n                    setPercent(progress * 100);\n                    if (progress < 1) {\n                        calculate();\n                    }\n                });\n            };\n            calculate();\n            return function() {\n                if (pauseOnHover) {\n                    cancelAnimationFrame(animationFrame);\n                }\n            };\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        duration,\n        spentTime,\n        mergedHovering,\n        mergedShowProgress,\n        times\n    ]);\n    // ======================== Closable ========================\n    var closableObj = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function() {\n        if ((0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(closable) === \"object\" && closable !== null) {\n            return closable;\n        }\n        if (closable) {\n            return {\n                closeIcon: closeIcon\n            };\n        }\n        return {};\n    }, [\n        closable,\n        closeIcon\n    ]);\n    var ariaProps = (0,rc_util_es_pickAttrs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(closableObj, true);\n    // ======================== Progress ========================\n    var validPercent = 100 - (!percent || percent < 0 ? 0 : percent > 100 ? 100 : percent);\n    // ======================== Render ========================\n    var noticePrefixCls = \"\".concat(prefixCls, \"-notice\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, divProps, {\n        ref: ref,\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(noticePrefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(noticePrefixCls, \"-closable\"), closable)),\n        style: style,\n        onMouseEnter: function onMouseEnter(e) {\n            var _divProps$onMouseEnte;\n            setHovering(true);\n            divProps === null || divProps === void 0 || (_divProps$onMouseEnte = divProps.onMouseEnter) === null || _divProps$onMouseEnte === void 0 || _divProps$onMouseEnte.call(divProps, e);\n        },\n        onMouseLeave: function onMouseLeave(e) {\n            var _divProps$onMouseLeav;\n            setHovering(false);\n            divProps === null || divProps === void 0 || (_divProps$onMouseLeav = divProps.onMouseLeave) === null || _divProps$onMouseLeav === void 0 || _divProps$onMouseLeav.call(divProps, e);\n        },\n        onClick: onClick\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"div\", {\n        className: \"\".concat(noticePrefixCls, \"-content\")\n    }, content), closable && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"a\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        tabIndex: 0,\n        className: \"\".concat(noticePrefixCls, \"-close\"),\n        onKeyDown: onCloseKeyDown,\n        \"aria-label\": \"Close\"\n    }, ariaProps, {\n        onClick: function onClick(e) {\n            e.preventDefault();\n            e.stopPropagation();\n            onInternalClose();\n        }\n    }), closableObj.closeIcon), mergedShowProgress && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6__.createElement(\"progress\", {\n        className: \"\".concat(noticePrefixCls, \"-progress\"),\n        max: \"100\",\n        value: validPercent\n    }, validPercent + \"%\"));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NoticeList.js":
/*!*******************************************************!*\
  !*** ./node_modules/rc-notification/es/NoticeList.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n/* harmony import */ var _hooks_useStack__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./hooks/useStack */ \"(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\");\n\n\n\n\n\n\nvar _excluded = [\n    \"className\",\n    \"style\",\n    \"classNames\",\n    \"styles\"\n];\n\n\n\n\n\n\nvar NoticeList = function NoticeList(props) {\n    var configList = props.configList, placement = props.placement, prefixCls = props.prefixCls, className = props.className, style = props.style, motion = props.motion, onAllNoticeRemoved = props.onAllNoticeRemoved, onNoticeClose = props.onNoticeClose, stackConfig = props.stack;\n    var _useContext = (0,react__WEBPACK_IMPORTED_MODULE_6__.useContext)(_NotificationProvider__WEBPACK_IMPORTED_MODULE_10__.NotificationContext), ctxCls = _useContext.classNames;\n    var dictRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)({});\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null), _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState, 2), latestNotice = _useState2[0], setLatestNotice = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]), _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useState3, 2), hoverKeys = _useState4[0], setHoverKeys = _useState4[1];\n    var keys = configList.map(function(config) {\n        return {\n            config: config,\n            key: String(config.key)\n        };\n    });\n    var _useStack = (0,_hooks_useStack__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(stackConfig), _useStack2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_useStack, 2), stack = _useStack2[0], _useStack2$ = _useStack2[1], offset = _useStack2$.offset, threshold = _useStack2$.threshold, gap = _useStack2$.gap;\n    var expanded = stack && (hoverKeys.length > 0 || keys.length <= threshold);\n    var placementMotion = typeof motion === \"function\" ? motion(placement) : motion;\n    // Clean hover key\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function() {\n        if (stack && hoverKeys.length > 1) {\n            setHoverKeys(function(prev) {\n                return prev.filter(function(key) {\n                    return keys.some(function(_ref) {\n                        var dataKey = _ref.key;\n                        return key === dataKey;\n                    });\n                });\n            });\n        }\n    }, [\n        hoverKeys,\n        keys,\n        stack\n    ]);\n    // Force update latest notice\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function() {\n        var _keys;\n        if (stack && dictRef.current[(_keys = keys[keys.length - 1]) === null || _keys === void 0 ? void 0 : _keys.key]) {\n            var _keys2;\n            setLatestNotice(dictRef.current[(_keys2 = keys[keys.length - 1]) === null || _keys2 === void 0 ? void 0 : _keys2.key]);\n        }\n    }, [\n        keys,\n        stack\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(rc_motion__WEBPACK_IMPORTED_MODULE_8__.CSSMotionList, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: placement,\n        className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(prefixCls, \"\".concat(prefixCls, \"-\").concat(placement), ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.list, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({}, \"\".concat(prefixCls, \"-stack\"), !!stack), \"\".concat(prefixCls, \"-stack-expanded\"), expanded)),\n        style: style,\n        keys: keys,\n        motionAppear: true\n    }, placementMotion, {\n        onAllRemoved: function onAllRemoved() {\n            onAllNoticeRemoved(placement);\n        }\n    }), function(_ref2, nodeRef) {\n        var config = _ref2.config, motionClassName = _ref2.className, motionStyle = _ref2.style, motionIndex = _ref2.index;\n        var _ref3 = config, key = _ref3.key, times = _ref3.times;\n        var strKey = String(key);\n        var _ref4 = config, configClassName = _ref4.className, configStyle = _ref4.style, configClassNames = _ref4.classNames, configStyles = _ref4.styles, restConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_ref4, _excluded);\n        var dataIndex = keys.findIndex(function(item) {\n            return item.key === strKey;\n        });\n        // If dataIndex is -1, that means this notice has been removed in data, but still in dom\n        // Should minus (motionIndex - 1) to get the correct index because keys.length is not the same as dom length\n        var stackStyle = {};\n        if (stack) {\n            var index = keys.length - 1 - (dataIndex > -1 ? dataIndex : motionIndex - 1);\n            var transformX = placement === \"top\" || placement === \"bottom\" ? \"-50%\" : \"0\";\n            if (index > 0) {\n                var _dictRef$current$strK, _dictRef$current$strK2, _dictRef$current$strK3;\n                stackStyle.height = expanded ? (_dictRef$current$strK = dictRef.current[strKey]) === null || _dictRef$current$strK === void 0 ? void 0 : _dictRef$current$strK.offsetHeight : latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetHeight;\n                // Transform\n                var verticalOffset = 0;\n                for(var i = 0; i < index; i++){\n                    var _dictRef$current$keys;\n                    verticalOffset += ((_dictRef$current$keys = dictRef.current[keys[keys.length - 1 - i].key]) === null || _dictRef$current$keys === void 0 ? void 0 : _dictRef$current$keys.offsetHeight) + gap;\n                }\n                var transformY = (expanded ? verticalOffset : index * offset) * (placement.startsWith(\"top\") ? 1 : -1);\n                var scaleX = !expanded && latestNotice !== null && latestNotice !== void 0 && latestNotice.offsetWidth && (_dictRef$current$strK2 = dictRef.current[strKey]) !== null && _dictRef$current$strK2 !== void 0 && _dictRef$current$strK2.offsetWidth ? ((latestNotice === null || latestNotice === void 0 ? void 0 : latestNotice.offsetWidth) - offset * 2 * (index < 3 ? index : 3)) / ((_dictRef$current$strK3 = dictRef.current[strKey]) === null || _dictRef$current$strK3 === void 0 ? void 0 : _dictRef$current$strK3.offsetWidth) : 1;\n                stackStyle.transform = \"translate3d(\".concat(transformX, \", \").concat(transformY, \"px, 0) scaleX(\").concat(scaleX, \")\");\n            } else {\n                stackStyle.transform = \"translate3d(\".concat(transformX, \", 0, 0)\");\n            }\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(\"div\", {\n            ref: nodeRef,\n            className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(\"\".concat(prefixCls, \"-notice-wrapper\"), motionClassName, configClassNames === null || configClassNames === void 0 ? void 0 : configClassNames.wrapper),\n            style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, motionStyle), stackStyle), configStyles === null || configStyles === void 0 ? void 0 : configStyles.wrapper),\n            onMouseEnter: function onMouseEnter() {\n                return setHoverKeys(function(prev) {\n                    return prev.includes(strKey) ? prev : [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(prev), [\n                        strKey\n                    ]);\n                });\n            },\n            onMouseLeave: function onMouseLeave() {\n                return setHoverKeys(function(prev) {\n                    return prev.filter(function(k) {\n                        return k !== strKey;\n                    });\n                });\n            }\n        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_6___default().createElement(_Notice__WEBPACK_IMPORTED_MODULE_9__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restConfig, {\n            ref: function ref(node) {\n                if (dataIndex > -1) {\n                    dictRef.current[strKey] = node;\n                } else {\n                    delete dictRef.current[strKey];\n                }\n            },\n            prefixCls: prefixCls,\n            classNames: configClassNames,\n            styles: configStyles,\n            className: classnames__WEBPACK_IMPORTED_MODULE_7___default()(configClassName, ctxCls === null || ctxCls === void 0 ? void 0 : ctxCls.notice),\n            style: configStyle,\n            times: times,\n            key: key,\n            eventKey: key,\n            onNoticeClose: onNoticeClose,\n            hovering: stack && hoverKeys.length > 0\n        })));\n    });\n};\nif (true) {\n    NoticeList.displayName = \"NoticeList\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoticeList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGljZUxpc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBEO0FBQ29CO0FBQ1Q7QUFDcUI7QUFDbEI7QUFDRjtBQUN0RSxJQUFJTSxZQUFZO0lBQUM7SUFBYTtJQUFTO0lBQWM7Q0FBUztBQUNTO0FBQ3pDO0FBQ1k7QUFDWjtBQUMrQjtBQUNyQjtBQUN4QyxJQUFJVyxhQUFhLFNBQVNBLFdBQVdDLEtBQUs7SUFDeEMsSUFBSUMsYUFBYUQsTUFBTUMsVUFBVSxFQUMvQkMsWUFBWUYsTUFBTUUsU0FBUyxFQUMzQkMsWUFBWUgsTUFBTUcsU0FBUyxFQUMzQkMsWUFBWUosTUFBTUksU0FBUyxFQUMzQkMsUUFBUUwsTUFBTUssS0FBSyxFQUNuQkMsU0FBU04sTUFBTU0sTUFBTSxFQUNyQkMscUJBQXFCUCxNQUFNTyxrQkFBa0IsRUFDN0NDLGdCQUFnQlIsTUFBTVEsYUFBYSxFQUNuQ0MsY0FBY1QsTUFBTVUsS0FBSztJQUMzQixJQUFJQyxjQUFjckIsaURBQVVBLENBQUNPLHVFQUFtQkEsR0FDOUNlLFNBQVNELFlBQVlFLFVBQVU7SUFDakMsSUFBSUMsVUFBVXRCLDZDQUFNQSxDQUFDLENBQUM7SUFDdEIsSUFBSXVCLFlBQVl0QiwrQ0FBUUEsQ0FBQyxPQUN2QnVCLGFBQWE3QixvRkFBY0EsQ0FBQzRCLFdBQVcsSUFDdkNFLGVBQWVELFVBQVUsQ0FBQyxFQUFFLEVBQzVCRSxrQkFBa0JGLFVBQVUsQ0FBQyxFQUFFO0lBQ2pDLElBQUlHLGFBQWExQiwrQ0FBUUEsQ0FBQyxFQUFFLEdBQzFCMkIsYUFBYWpDLG9GQUFjQSxDQUFDZ0MsWUFBWSxJQUN4Q0UsWUFBWUQsVUFBVSxDQUFDLEVBQUUsRUFDekJFLGVBQWVGLFVBQVUsQ0FBQyxFQUFFO0lBQzlCLElBQUlHLE9BQU90QixXQUFXdUIsR0FBRyxDQUFDLFNBQVVDLE1BQU07UUFDeEMsT0FBTztZQUNMQSxRQUFRQTtZQUNSQyxLQUFLQyxPQUFPRixPQUFPQyxHQUFHO1FBQ3hCO0lBQ0Y7SUFDQSxJQUFJRSxZQUFZOUIsNERBQVFBLENBQUNXLGNBQ3ZCb0IsYUFBYTFDLG9GQUFjQSxDQUFDeUMsV0FBVyxJQUN2Q2xCLFFBQVFtQixVQUFVLENBQUMsRUFBRSxFQUNyQkMsY0FBY0QsVUFBVSxDQUFDLEVBQUUsRUFDM0JFLFNBQVNELFlBQVlDLE1BQU0sRUFDM0JDLFlBQVlGLFlBQVlFLFNBQVMsRUFDakNDLE1BQU1ILFlBQVlHLEdBQUc7SUFDdkIsSUFBSUMsV0FBV3hCLFNBQVVXLENBQUFBLFVBQVVjLE1BQU0sR0FBRyxLQUFLWixLQUFLWSxNQUFNLElBQUlILFNBQVE7SUFDeEUsSUFBSUksa0JBQWtCLE9BQU85QixXQUFXLGFBQWFBLE9BQU9KLGFBQWFJO0lBRXpFLGtCQUFrQjtJQUNsQmYsZ0RBQVNBLENBQUM7UUFDUixJQUFJbUIsU0FBU1csVUFBVWMsTUFBTSxHQUFHLEdBQUc7WUFDakNiLGFBQWEsU0FBVWUsSUFBSTtnQkFDekIsT0FBT0EsS0FBS0MsTUFBTSxDQUFDLFNBQVVaLEdBQUc7b0JBQzlCLE9BQU9ILEtBQUtnQixJQUFJLENBQUMsU0FBVUMsSUFBSTt3QkFDN0IsSUFBSUMsVUFBVUQsS0FBS2QsR0FBRzt3QkFDdEIsT0FBT0EsUUFBUWU7b0JBQ2pCO2dCQUNGO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQ3BCO1FBQVdFO1FBQU1iO0tBQU07SUFFM0IsNkJBQTZCO0lBQzdCbkIsZ0RBQVNBLENBQUM7UUFDUixJQUFJbUQ7UUFDSixJQUFJaEMsU0FBU0ksUUFBUTZCLE9BQU8sQ0FBQyxDQUFDRCxRQUFRbkIsSUFBSSxDQUFDQSxLQUFLWSxNQUFNLEdBQUcsRUFBRSxNQUFNLFFBQVFPLFVBQVUsS0FBSyxJQUFJLEtBQUssSUFBSUEsTUFBTWhCLEdBQUcsQ0FBQyxFQUFFO1lBQy9HLElBQUlrQjtZQUNKMUIsZ0JBQWdCSixRQUFRNkIsT0FBTyxDQUFDLENBQUNDLFNBQVNyQixJQUFJLENBQUNBLEtBQUtZLE1BQU0sR0FBRyxFQUFFLE1BQU0sUUFBUVMsV0FBVyxLQUFLLElBQUksS0FBSyxJQUFJQSxPQUFPbEIsR0FBRyxDQUFDO1FBQ3ZIO0lBQ0YsR0FBRztRQUFDSDtRQUFNYjtLQUFNO0lBQ2hCLE9BQU8sV0FBVyxHQUFFckIsMERBQW1CLENBQUNNLG9EQUFhQSxFQUFFYiw4RUFBUUEsQ0FBQztRQUM5RDRDLEtBQUt4QjtRQUNMRSxXQUFXVixpREFBSUEsQ0FBQ1MsV0FBVyxHQUFHMkMsTUFBTSxDQUFDM0MsV0FBVyxLQUFLMkMsTUFBTSxDQUFDNUMsWUFBWVUsV0FBVyxRQUFRQSxXQUFXLEtBQUssSUFBSSxLQUFLLElBQUlBLE9BQU9tQyxJQUFJLEVBQUUzQyxXQUFXbEIscUZBQWVBLENBQUNBLHFGQUFlQSxDQUFDLENBQUMsR0FBRyxHQUFHNEQsTUFBTSxDQUFDM0MsV0FBVyxXQUFXLENBQUMsQ0FBQ08sUUFBUSxHQUFHb0MsTUFBTSxDQUFDM0MsV0FBVyxvQkFBb0IrQjtRQUN2UTdCLE9BQU9BO1FBQ1BrQixNQUFNQTtRQUNOeUIsY0FBYztJQUNoQixHQUFHWixpQkFBaUI7UUFDbEJhLGNBQWMsU0FBU0E7WUFDckIxQyxtQkFBbUJMO1FBQ3JCO0lBQ0YsSUFBSSxTQUFVZ0QsS0FBSyxFQUFFQyxPQUFPO1FBQzFCLElBQUkxQixTQUFTeUIsTUFBTXpCLE1BQU0sRUFDdkIyQixrQkFBa0JGLE1BQU05QyxTQUFTLEVBQ2pDaUQsY0FBY0gsTUFBTTdDLEtBQUssRUFDekJpRCxjQUFjSixNQUFNSyxLQUFLO1FBQzNCLElBQUlDLFFBQVEvQixRQUNWQyxNQUFNOEIsTUFBTTlCLEdBQUcsRUFDZitCLFFBQVFELE1BQU1DLEtBQUs7UUFDckIsSUFBSUMsU0FBUy9CLE9BQU9EO1FBQ3BCLElBQUlpQyxRQUFRbEMsUUFDVm1DLGtCQUFrQkQsTUFBTXZELFNBQVMsRUFDakN5RCxjQUFjRixNQUFNdEQsS0FBSyxFQUN6QnlELG1CQUFtQkgsTUFBTTlDLFVBQVUsRUFDbkNrRCxlQUFlSixNQUFNSyxNQUFNLEVBQzNCQyxhQUFhaEYsOEZBQXdCQSxDQUFDMEUsT0FBT3ZFO1FBQy9DLElBQUk4RSxZQUFZM0MsS0FBSzRDLFNBQVMsQ0FBQyxTQUFVQyxJQUFJO1lBQzNDLE9BQU9BLEtBQUsxQyxHQUFHLEtBQUtnQztRQUN0QjtRQUVBLHdGQUF3RjtRQUN4Riw0R0FBNEc7UUFDNUcsSUFBSVcsYUFBYSxDQUFDO1FBQ2xCLElBQUkzRCxPQUFPO1lBQ1QsSUFBSTZDLFFBQVFoQyxLQUFLWSxNQUFNLEdBQUcsSUFBSytCLENBQUFBLFlBQVksQ0FBQyxJQUFJQSxZQUFZWixjQUFjO1lBQzFFLElBQUlnQixhQUFhcEUsY0FBYyxTQUFTQSxjQUFjLFdBQVcsU0FBUztZQUMxRSxJQUFJcUQsUUFBUSxHQUFHO2dCQUNiLElBQUlnQix1QkFBdUJDLHdCQUF3QkM7Z0JBQ25ESixXQUFXSyxNQUFNLEdBQUd4QyxXQUFXLENBQUNxQyx3QkFBd0J6RCxRQUFRNkIsT0FBTyxDQUFDZSxPQUFPLE1BQU0sUUFBUWEsMEJBQTBCLEtBQUssSUFBSSxLQUFLLElBQUlBLHNCQUFzQkksWUFBWSxHQUFHMUQsaUJBQWlCLFFBQVFBLGlCQUFpQixLQUFLLElBQUksS0FBSyxJQUFJQSxhQUFhMEQsWUFBWTtnQkFFblEsWUFBWTtnQkFDWixJQUFJQyxpQkFBaUI7Z0JBQ3JCLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJdEIsT0FBT3NCLElBQUs7b0JBQzlCLElBQUlDO29CQUNKRixrQkFBa0IsQ0FBQyxDQUFDRSx3QkFBd0JoRSxRQUFRNkIsT0FBTyxDQUFDcEIsSUFBSSxDQUFDQSxLQUFLWSxNQUFNLEdBQUcsSUFBSTBDLEVBQUUsQ0FBQ25ELEdBQUcsQ0FBQyxNQUFNLFFBQVFvRCwwQkFBMEIsS0FBSyxJQUFJLEtBQUssSUFBSUEsc0JBQXNCSCxZQUFZLElBQUkxQztnQkFDNUw7Z0JBQ0EsSUFBSThDLGFBQWEsQ0FBQzdDLFdBQVcwQyxpQkFBaUJyQixRQUFReEIsTUFBSyxJQUFNN0IsQ0FBQUEsVUFBVThFLFVBQVUsQ0FBQyxTQUFTLElBQUksQ0FBQztnQkFDcEcsSUFBSUMsU0FBUyxDQUFDL0MsWUFBWWpCLGlCQUFpQixRQUFRQSxpQkFBaUIsS0FBSyxLQUFLQSxhQUFhaUUsV0FBVyxJQUFJLENBQUNWLHlCQUF5QjFELFFBQVE2QixPQUFPLENBQUNlLE9BQU8sTUFBTSxRQUFRYywyQkFBMkIsS0FBSyxLQUFLQSx1QkFBdUJVLFdBQVcsR0FBRyxDQUFDLENBQUNqRSxpQkFBaUIsUUFBUUEsaUJBQWlCLEtBQUssSUFBSSxLQUFLLElBQUlBLGFBQWFpRSxXQUFXLElBQUluRCxTQUFTLElBQUt3QixDQUFBQSxRQUFRLElBQUlBLFFBQVEsRUFBQyxJQUFNLEVBQUNrQix5QkFBeUIzRCxRQUFRNkIsT0FBTyxDQUFDZSxPQUFPLE1BQU0sUUFBUWUsMkJBQTJCLEtBQUssSUFBSSxLQUFLLElBQUlBLHVCQUF1QlMsV0FBVyxJQUFJO2dCQUN4Z0JiLFdBQVdjLFNBQVMsR0FBRyxlQUFlckMsTUFBTSxDQUFDd0IsWUFBWSxNQUFNeEIsTUFBTSxDQUFDaUMsWUFBWSxrQkFBa0JqQyxNQUFNLENBQUNtQyxRQUFRO1lBQ3JILE9BQU87Z0JBQ0xaLFdBQVdjLFNBQVMsR0FBRyxlQUFlckMsTUFBTSxDQUFDd0IsWUFBWTtZQUMzRDtRQUNGO1FBQ0EsT0FBTyxXQUFXLEdBQUVqRiwwREFBbUIsQ0FBQyxPQUFPO1lBQzdDK0YsS0FBS2pDO1lBQ0wvQyxXQUFXVixpREFBSUEsQ0FBQyxHQUFHb0QsTUFBTSxDQUFDM0MsV0FBVyxvQkFBb0JpRCxpQkFBaUJVLHFCQUFxQixRQUFRQSxxQkFBcUIsS0FBSyxJQUFJLEtBQUssSUFBSUEsaUJBQWlCdUIsT0FBTztZQUN0S2hGLE9BQU9yQixvRkFBYUEsQ0FBQ0Esb0ZBQWFBLENBQUNBLG9GQUFhQSxDQUFDLENBQUMsR0FBR3FFLGNBQWNnQixhQUFhTixpQkFBaUIsUUFBUUEsaUJBQWlCLEtBQUssSUFBSSxLQUFLLElBQUlBLGFBQWFzQixPQUFPO1lBQ2hLQyxjQUFjLFNBQVNBO2dCQUNyQixPQUFPaEUsYUFBYSxTQUFVZSxJQUFJO29CQUNoQyxPQUFPQSxLQUFLa0QsUUFBUSxDQUFDN0IsVUFBVXJCLE9BQU8sRUFBRSxDQUFDUyxNQUFNLENBQUMvRCx3RkFBa0JBLENBQUNzRCxPQUFPO3dCQUFDcUI7cUJBQU87Z0JBQ3BGO1lBQ0Y7WUFDQThCLGNBQWMsU0FBU0E7Z0JBQ3JCLE9BQU9sRSxhQUFhLFNBQVVlLElBQUk7b0JBQ2hDLE9BQU9BLEtBQUtDLE1BQU0sQ0FBQyxTQUFVbUQsQ0FBQzt3QkFDNUIsT0FBT0EsTUFBTS9CO29CQUNmO2dCQUNGO1lBQ0Y7UUFDRixHQUFHLFdBQVcsR0FBRXJFLDBEQUFtQixDQUFDTywrQ0FBTUEsRUFBRWQsOEVBQVFBLENBQUMsQ0FBQyxHQUFHbUYsWUFBWTtZQUNuRW1CLEtBQUssU0FBU0EsSUFBSU0sSUFBSTtnQkFDcEIsSUFBSXhCLFlBQVksQ0FBQyxHQUFHO29CQUNsQnBELFFBQVE2QixPQUFPLENBQUNlLE9BQU8sR0FBR2dDO2dCQUM1QixPQUFPO29CQUNMLE9BQU81RSxRQUFRNkIsT0FBTyxDQUFDZSxPQUFPO2dCQUNoQztZQUNGO1lBQ0F2RCxXQUFXQTtZQUNYVSxZQUFZaUQ7WUFDWkUsUUFBUUQ7WUFDUjNELFdBQVdWLGlEQUFJQSxDQUFDa0UsaUJBQWlCaEQsV0FBVyxRQUFRQSxXQUFXLEtBQUssSUFBSSxLQUFLLElBQUlBLE9BQU8rRSxNQUFNO1lBQzlGdEYsT0FBT3dEO1lBQ1BKLE9BQU9BO1lBQ1AvQixLQUFLQTtZQUNMa0UsVUFBVWxFO1lBQ1ZsQixlQUFlQTtZQUNmcUYsVUFBVW5GLFNBQVNXLFVBQVVjLE1BQU0sR0FBRztRQUN4QztJQUNGO0FBQ0Y7QUFDQSxJQUFJMkQsSUFBeUIsRUFBYztJQUN6Qy9GLFdBQVdnRyxXQUFXLEdBQUc7QUFDM0I7QUFDQSxpRUFBZWhHLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcnAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGljZUxpc3QuanM/ZWQ0ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBfdG9Db25zdW1hYmxlQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5XCI7XG5pbXBvcnQgX29iamVjdFNwcmVhZCBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0U3ByZWFkMlwiO1xuaW1wb3J0IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNcIjtcbmltcG9ydCBfZGVmaW5lUHJvcGVydHkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5XCI7XG5pbXBvcnQgX3NsaWNlZFRvQXJyYXkgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL3NsaWNlZFRvQXJyYXlcIjtcbnZhciBfZXhjbHVkZWQgPSBbXCJjbGFzc05hbWVcIiwgXCJzdHlsZVwiLCBcImNsYXNzTmFtZXNcIiwgXCJzdHlsZXNcIl07XG5pbXBvcnQgUmVhY3QsIHsgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGNsc3ggZnJvbSAnY2xhc3NuYW1lcyc7XG5pbXBvcnQgeyBDU1NNb3Rpb25MaXN0IH0gZnJvbSAncmMtbW90aW9uJztcbmltcG9ydCBOb3RpY2UgZnJvbSBcIi4vTm90aWNlXCI7XG5pbXBvcnQgeyBOb3RpZmljYXRpb25Db250ZXh0IH0gZnJvbSBcIi4vTm90aWZpY2F0aW9uUHJvdmlkZXJcIjtcbmltcG9ydCB1c2VTdGFjayBmcm9tIFwiLi9ob29rcy91c2VTdGFja1wiO1xudmFyIE5vdGljZUxpc3QgPSBmdW5jdGlvbiBOb3RpY2VMaXN0KHByb3BzKSB7XG4gIHZhciBjb25maWdMaXN0ID0gcHJvcHMuY29uZmlnTGlzdCxcbiAgICBwbGFjZW1lbnQgPSBwcm9wcy5wbGFjZW1lbnQsXG4gICAgcHJlZml4Q2xzID0gcHJvcHMucHJlZml4Q2xzLFxuICAgIGNsYXNzTmFtZSA9IHByb3BzLmNsYXNzTmFtZSxcbiAgICBzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIG1vdGlvbiA9IHByb3BzLm1vdGlvbixcbiAgICBvbkFsbE5vdGljZVJlbW92ZWQgPSBwcm9wcy5vbkFsbE5vdGljZVJlbW92ZWQsXG4gICAgb25Ob3RpY2VDbG9zZSA9IHByb3BzLm9uTm90aWNlQ2xvc2UsXG4gICAgc3RhY2tDb25maWcgPSBwcm9wcy5zdGFjaztcbiAgdmFyIF91c2VDb250ZXh0ID0gdXNlQ29udGV4dChOb3RpZmljYXRpb25Db250ZXh0KSxcbiAgICBjdHhDbHMgPSBfdXNlQ29udGV4dC5jbGFzc05hbWVzO1xuICB2YXIgZGljdFJlZiA9IHVzZVJlZih7fSk7XG4gIHZhciBfdXNlU3RhdGUgPSB1c2VTdGF0ZShudWxsKSxcbiAgICBfdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX3VzZVN0YXRlLCAyKSxcbiAgICBsYXRlc3ROb3RpY2UgPSBfdXNlU3RhdGUyWzBdLFxuICAgIHNldExhdGVzdE5vdGljZSA9IF91c2VTdGF0ZTJbMV07XG4gIHZhciBfdXNlU3RhdGUzID0gdXNlU3RhdGUoW10pLFxuICAgIF91c2VTdGF0ZTQgPSBfc2xpY2VkVG9BcnJheShfdXNlU3RhdGUzLCAyKSxcbiAgICBob3ZlcktleXMgPSBfdXNlU3RhdGU0WzBdLFxuICAgIHNldEhvdmVyS2V5cyA9IF91c2VTdGF0ZTRbMV07XG4gIHZhciBrZXlzID0gY29uZmlnTGlzdC5tYXAoZnVuY3Rpb24gKGNvbmZpZykge1xuICAgIHJldHVybiB7XG4gICAgICBjb25maWc6IGNvbmZpZyxcbiAgICAgIGtleTogU3RyaW5nKGNvbmZpZy5rZXkpXG4gICAgfTtcbiAgfSk7XG4gIHZhciBfdXNlU3RhY2sgPSB1c2VTdGFjayhzdGFja0NvbmZpZyksXG4gICAgX3VzZVN0YWNrMiA9IF9zbGljZWRUb0FycmF5KF91c2VTdGFjaywgMiksXG4gICAgc3RhY2sgPSBfdXNlU3RhY2syWzBdLFxuICAgIF91c2VTdGFjazIkID0gX3VzZVN0YWNrMlsxXSxcbiAgICBvZmZzZXQgPSBfdXNlU3RhY2syJC5vZmZzZXQsXG4gICAgdGhyZXNob2xkID0gX3VzZVN0YWNrMiQudGhyZXNob2xkLFxuICAgIGdhcCA9IF91c2VTdGFjazIkLmdhcDtcbiAgdmFyIGV4cGFuZGVkID0gc3RhY2sgJiYgKGhvdmVyS2V5cy5sZW5ndGggPiAwIHx8IGtleXMubGVuZ3RoIDw9IHRocmVzaG9sZCk7XG4gIHZhciBwbGFjZW1lbnRNb3Rpb24gPSB0eXBlb2YgbW90aW9uID09PSAnZnVuY3Rpb24nID8gbW90aW9uKHBsYWNlbWVudCkgOiBtb3Rpb247XG5cbiAgLy8gQ2xlYW4gaG92ZXIga2V5XG4gIHVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgaWYgKHN0YWNrICYmIGhvdmVyS2V5cy5sZW5ndGggPiAxKSB7XG4gICAgICBzZXRIb3ZlcktleXMoZnVuY3Rpb24gKHByZXYpIHtcbiAgICAgICAgcmV0dXJuIHByZXYuZmlsdGVyKGZ1bmN0aW9uIChrZXkpIHtcbiAgICAgICAgICByZXR1cm4ga2V5cy5zb21lKGZ1bmN0aW9uIChfcmVmKSB7XG4gICAgICAgICAgICB2YXIgZGF0YUtleSA9IF9yZWYua2V5O1xuICAgICAgICAgICAgcmV0dXJuIGtleSA9PT0gZGF0YUtleTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtob3ZlcktleXMsIGtleXMsIHN0YWNrXSk7XG5cbiAgLy8gRm9yY2UgdXBkYXRlIGxhdGVzdCBub3RpY2VcbiAgdXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgX2tleXM7XG4gICAgaWYgKHN0YWNrICYmIGRpY3RSZWYuY3VycmVudFsoX2tleXMgPSBrZXlzW2tleXMubGVuZ3RoIC0gMV0pID09PSBudWxsIHx8IF9rZXlzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfa2V5cy5rZXldKSB7XG4gICAgICB2YXIgX2tleXMyO1xuICAgICAgc2V0TGF0ZXN0Tm90aWNlKGRpY3RSZWYuY3VycmVudFsoX2tleXMyID0ga2V5c1trZXlzLmxlbmd0aCAtIDFdKSA9PT0gbnVsbCB8fCBfa2V5czIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9rZXlzMi5rZXldKTtcbiAgICB9XG4gIH0sIFtrZXlzLCBzdGFja10pO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQ1NTTW90aW9uTGlzdCwgX2V4dGVuZHMoe1xuICAgIGtleTogcGxhY2VtZW50LFxuICAgIGNsYXNzTmFtZTogY2xzeChwcmVmaXhDbHMsIFwiXCIuY29uY2F0KHByZWZpeENscywgXCItXCIpLmNvbmNhdChwbGFjZW1lbnQpLCBjdHhDbHMgPT09IG51bGwgfHwgY3R4Q2xzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjdHhDbHMubGlzdCwgY2xhc3NOYW1lLCBfZGVmaW5lUHJvcGVydHkoX2RlZmluZVByb3BlcnR5KHt9LCBcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLXN0YWNrXCIpLCAhIXN0YWNrKSwgXCJcIi5jb25jYXQocHJlZml4Q2xzLCBcIi1zdGFjay1leHBhbmRlZFwiKSwgZXhwYW5kZWQpKSxcbiAgICBzdHlsZTogc3R5bGUsXG4gICAga2V5czoga2V5cyxcbiAgICBtb3Rpb25BcHBlYXI6IHRydWVcbiAgfSwgcGxhY2VtZW50TW90aW9uLCB7XG4gICAgb25BbGxSZW1vdmVkOiBmdW5jdGlvbiBvbkFsbFJlbW92ZWQoKSB7XG4gICAgICBvbkFsbE5vdGljZVJlbW92ZWQocGxhY2VtZW50KTtcbiAgICB9XG4gIH0pLCBmdW5jdGlvbiAoX3JlZjIsIG5vZGVSZWYpIHtcbiAgICB2YXIgY29uZmlnID0gX3JlZjIuY29uZmlnLFxuICAgICAgbW90aW9uQ2xhc3NOYW1lID0gX3JlZjIuY2xhc3NOYW1lLFxuICAgICAgbW90aW9uU3R5bGUgPSBfcmVmMi5zdHlsZSxcbiAgICAgIG1vdGlvbkluZGV4ID0gX3JlZjIuaW5kZXg7XG4gICAgdmFyIF9yZWYzID0gY29uZmlnLFxuICAgICAga2V5ID0gX3JlZjMua2V5LFxuICAgICAgdGltZXMgPSBfcmVmMy50aW1lcztcbiAgICB2YXIgc3RyS2V5ID0gU3RyaW5nKGtleSk7XG4gICAgdmFyIF9yZWY0ID0gY29uZmlnLFxuICAgICAgY29uZmlnQ2xhc3NOYW1lID0gX3JlZjQuY2xhc3NOYW1lLFxuICAgICAgY29uZmlnU3R5bGUgPSBfcmVmNC5zdHlsZSxcbiAgICAgIGNvbmZpZ0NsYXNzTmFtZXMgPSBfcmVmNC5jbGFzc05hbWVzLFxuICAgICAgY29uZmlnU3R5bGVzID0gX3JlZjQuc3R5bGVzLFxuICAgICAgcmVzdENvbmZpZyA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllcyhfcmVmNCwgX2V4Y2x1ZGVkKTtcbiAgICB2YXIgZGF0YUluZGV4ID0ga2V5cy5maW5kSW5kZXgoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgIHJldHVybiBpdGVtLmtleSA9PT0gc3RyS2V5O1xuICAgIH0pO1xuXG4gICAgLy8gSWYgZGF0YUluZGV4IGlzIC0xLCB0aGF0IG1lYW5zIHRoaXMgbm90aWNlIGhhcyBiZWVuIHJlbW92ZWQgaW4gZGF0YSwgYnV0IHN0aWxsIGluIGRvbVxuICAgIC8vIFNob3VsZCBtaW51cyAobW90aW9uSW5kZXggLSAxKSB0byBnZXQgdGhlIGNvcnJlY3QgaW5kZXggYmVjYXVzZSBrZXlzLmxlbmd0aCBpcyBub3QgdGhlIHNhbWUgYXMgZG9tIGxlbmd0aFxuICAgIHZhciBzdGFja1N0eWxlID0ge307XG4gICAgaWYgKHN0YWNrKSB7XG4gICAgICB2YXIgaW5kZXggPSBrZXlzLmxlbmd0aCAtIDEgLSAoZGF0YUluZGV4ID4gLTEgPyBkYXRhSW5kZXggOiBtb3Rpb25JbmRleCAtIDEpO1xuICAgICAgdmFyIHRyYW5zZm9ybVggPSBwbGFjZW1lbnQgPT09ICd0b3AnIHx8IHBsYWNlbWVudCA9PT0gJ2JvdHRvbScgPyAnLTUwJScgOiAnMCc7XG4gICAgICBpZiAoaW5kZXggPiAwKSB7XG4gICAgICAgIHZhciBfZGljdFJlZiRjdXJyZW50JHN0ckssIF9kaWN0UmVmJGN1cnJlbnQkc3RySzIsIF9kaWN0UmVmJGN1cnJlbnQkc3RySzM7XG4gICAgICAgIHN0YWNrU3R5bGUuaGVpZ2h0ID0gZXhwYW5kZWQgPyAoX2RpY3RSZWYkY3VycmVudCRzdHJLID0gZGljdFJlZi5jdXJyZW50W3N0cktleV0pID09PSBudWxsIHx8IF9kaWN0UmVmJGN1cnJlbnQkc3RySyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2RpY3RSZWYkY3VycmVudCRzdHJLLm9mZnNldEhlaWdodCA6IGxhdGVzdE5vdGljZSA9PT0gbnVsbCB8fCBsYXRlc3ROb3RpY2UgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGxhdGVzdE5vdGljZS5vZmZzZXRIZWlnaHQ7XG5cbiAgICAgICAgLy8gVHJhbnNmb3JtXG4gICAgICAgIHZhciB2ZXJ0aWNhbE9mZnNldCA9IDA7XG4gICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgaW5kZXg7IGkrKykge1xuICAgICAgICAgIHZhciBfZGljdFJlZiRjdXJyZW50JGtleXM7XG4gICAgICAgICAgdmVydGljYWxPZmZzZXQgKz0gKChfZGljdFJlZiRjdXJyZW50JGtleXMgPSBkaWN0UmVmLmN1cnJlbnRba2V5c1trZXlzLmxlbmd0aCAtIDEgLSBpXS5rZXldKSA9PT0gbnVsbCB8fCBfZGljdFJlZiRjdXJyZW50JGtleXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kaWN0UmVmJGN1cnJlbnQka2V5cy5vZmZzZXRIZWlnaHQpICsgZ2FwO1xuICAgICAgICB9XG4gICAgICAgIHZhciB0cmFuc2Zvcm1ZID0gKGV4cGFuZGVkID8gdmVydGljYWxPZmZzZXQgOiBpbmRleCAqIG9mZnNldCkgKiAocGxhY2VtZW50LnN0YXJ0c1dpdGgoJ3RvcCcpID8gMSA6IC0xKTtcbiAgICAgICAgdmFyIHNjYWxlWCA9ICFleHBhbmRlZCAmJiBsYXRlc3ROb3RpY2UgIT09IG51bGwgJiYgbGF0ZXN0Tm90aWNlICE9PSB2b2lkIDAgJiYgbGF0ZXN0Tm90aWNlLm9mZnNldFdpZHRoICYmIChfZGljdFJlZiRjdXJyZW50JHN0cksyID0gZGljdFJlZi5jdXJyZW50W3N0cktleV0pICE9PSBudWxsICYmIF9kaWN0UmVmJGN1cnJlbnQkc3RySzIgIT09IHZvaWQgMCAmJiBfZGljdFJlZiRjdXJyZW50JHN0cksyLm9mZnNldFdpZHRoID8gKChsYXRlc3ROb3RpY2UgPT09IG51bGwgfHwgbGF0ZXN0Tm90aWNlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBsYXRlc3ROb3RpY2Uub2Zmc2V0V2lkdGgpIC0gb2Zmc2V0ICogMiAqIChpbmRleCA8IDMgPyBpbmRleCA6IDMpKSAvICgoX2RpY3RSZWYkY3VycmVudCRzdHJLMyA9IGRpY3RSZWYuY3VycmVudFtzdHJLZXldKSA9PT0gbnVsbCB8fCBfZGljdFJlZiRjdXJyZW50JHN0ckszID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZGljdFJlZiRjdXJyZW50JHN0ckszLm9mZnNldFdpZHRoKSA6IDE7XG4gICAgICAgIHN0YWNrU3R5bGUudHJhbnNmb3JtID0gXCJ0cmFuc2xhdGUzZChcIi5jb25jYXQodHJhbnNmb3JtWCwgXCIsIFwiKS5jb25jYXQodHJhbnNmb3JtWSwgXCJweCwgMCkgc2NhbGVYKFwiKS5jb25jYXQoc2NhbGVYLCBcIilcIik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzdGFja1N0eWxlLnRyYW5zZm9ybSA9IFwidHJhbnNsYXRlM2QoXCIuY29uY2F0KHRyYW5zZm9ybVgsIFwiLCAwLCAwKVwiKTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICAgIHJlZjogbm9kZVJlZixcbiAgICAgIGNsYXNzTmFtZTogY2xzeChcIlwiLmNvbmNhdChwcmVmaXhDbHMsIFwiLW5vdGljZS13cmFwcGVyXCIpLCBtb3Rpb25DbGFzc05hbWUsIGNvbmZpZ0NsYXNzTmFtZXMgPT09IG51bGwgfHwgY29uZmlnQ2xhc3NOYW1lcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnQ2xhc3NOYW1lcy53cmFwcGVyKSxcbiAgICAgIHN0eWxlOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgbW90aW9uU3R5bGUpLCBzdGFja1N0eWxlKSwgY29uZmlnU3R5bGVzID09PSBudWxsIHx8IGNvbmZpZ1N0eWxlcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY29uZmlnU3R5bGVzLndyYXBwZXIpLFxuICAgICAgb25Nb3VzZUVudGVyOiBmdW5jdGlvbiBvbk1vdXNlRW50ZXIoKSB7XG4gICAgICAgIHJldHVybiBzZXRIb3ZlcktleXMoZnVuY3Rpb24gKHByZXYpIHtcbiAgICAgICAgICByZXR1cm4gcHJldi5pbmNsdWRlcyhzdHJLZXkpID8gcHJldiA6IFtdLmNvbmNhdChfdG9Db25zdW1hYmxlQXJyYXkocHJldiksIFtzdHJLZXldKTtcbiAgICAgICAgfSk7XG4gICAgICB9LFxuICAgICAgb25Nb3VzZUxlYXZlOiBmdW5jdGlvbiBvbk1vdXNlTGVhdmUoKSB7XG4gICAgICAgIHJldHVybiBzZXRIb3ZlcktleXMoZnVuY3Rpb24gKHByZXYpIHtcbiAgICAgICAgICByZXR1cm4gcHJldi5maWx0ZXIoZnVuY3Rpb24gKGspIHtcbiAgICAgICAgICAgIHJldHVybiBrICE9PSBzdHJLZXk7XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0sIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KE5vdGljZSwgX2V4dGVuZHMoe30sIHJlc3RDb25maWcsIHtcbiAgICAgIHJlZjogZnVuY3Rpb24gcmVmKG5vZGUpIHtcbiAgICAgICAgaWYgKGRhdGFJbmRleCA+IC0xKSB7XG4gICAgICAgICAgZGljdFJlZi5jdXJyZW50W3N0cktleV0gPSBub2RlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGRlbGV0ZSBkaWN0UmVmLmN1cnJlbnRbc3RyS2V5XTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHByZWZpeENsczogcHJlZml4Q2xzLFxuICAgICAgY2xhc3NOYW1lczogY29uZmlnQ2xhc3NOYW1lcyxcbiAgICAgIHN0eWxlczogY29uZmlnU3R5bGVzLFxuICAgICAgY2xhc3NOYW1lOiBjbHN4KGNvbmZpZ0NsYXNzTmFtZSwgY3R4Q2xzID09PSBudWxsIHx8IGN0eENscyA9PT0gdm9pZCAwID8gdm9pZCAwIDogY3R4Q2xzLm5vdGljZSksXG4gICAgICBzdHlsZTogY29uZmlnU3R5bGUsXG4gICAgICB0aW1lczogdGltZXMsXG4gICAgICBrZXk6IGtleSxcbiAgICAgIGV2ZW50S2V5OiBrZXksXG4gICAgICBvbk5vdGljZUNsb3NlOiBvbk5vdGljZUNsb3NlLFxuICAgICAgaG92ZXJpbmc6IHN0YWNrICYmIGhvdmVyS2V5cy5sZW5ndGggPiAwXG4gICAgfSkpKTtcbiAgfSk7XG59O1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgTm90aWNlTGlzdC5kaXNwbGF5TmFtZSA9ICdOb3RpY2VMaXN0Jztcbn1cbmV4cG9ydCBkZWZhdWx0IE5vdGljZUxpc3Q7Il0sIm5hbWVzIjpbIl9leHRlbmRzIiwiX3RvQ29uc3VtYWJsZUFycmF5IiwiX29iamVjdFNwcmVhZCIsIl9vYmplY3RXaXRob3V0UHJvcGVydGllcyIsIl9kZWZpbmVQcm9wZXJ0eSIsIl9zbGljZWRUb0FycmF5IiwiX2V4Y2x1ZGVkIiwiUmVhY3QiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJjbHN4IiwiQ1NTTW90aW9uTGlzdCIsIk5vdGljZSIsIk5vdGlmaWNhdGlvbkNvbnRleHQiLCJ1c2VTdGFjayIsIk5vdGljZUxpc3QiLCJwcm9wcyIsImNvbmZpZ0xpc3QiLCJwbGFjZW1lbnQiLCJwcmVmaXhDbHMiLCJjbGFzc05hbWUiLCJzdHlsZSIsIm1vdGlvbiIsIm9uQWxsTm90aWNlUmVtb3ZlZCIsIm9uTm90aWNlQ2xvc2UiLCJzdGFja0NvbmZpZyIsInN0YWNrIiwiX3VzZUNvbnRleHQiLCJjdHhDbHMiLCJjbGFzc05hbWVzIiwiZGljdFJlZiIsIl91c2VTdGF0ZSIsIl91c2VTdGF0ZTIiLCJsYXRlc3ROb3RpY2UiLCJzZXRMYXRlc3ROb3RpY2UiLCJfdXNlU3RhdGUzIiwiX3VzZVN0YXRlNCIsImhvdmVyS2V5cyIsInNldEhvdmVyS2V5cyIsImtleXMiLCJtYXAiLCJjb25maWciLCJrZXkiLCJTdHJpbmciLCJfdXNlU3RhY2siLCJfdXNlU3RhY2syIiwiX3VzZVN0YWNrMiQiLCJvZmZzZXQiLCJ0aHJlc2hvbGQiLCJnYXAiLCJleHBhbmRlZCIsImxlbmd0aCIsInBsYWNlbWVudE1vdGlvbiIsInByZXYiLCJmaWx0ZXIiLCJzb21lIiwiX3JlZiIsImRhdGFLZXkiLCJfa2V5cyIsImN1cnJlbnQiLCJfa2V5czIiLCJjcmVhdGVFbGVtZW50IiwiY29uY2F0IiwibGlzdCIsIm1vdGlvbkFwcGVhciIsIm9uQWxsUmVtb3ZlZCIsIl9yZWYyIiwibm9kZVJlZiIsIm1vdGlvbkNsYXNzTmFtZSIsIm1vdGlvblN0eWxlIiwibW90aW9uSW5kZXgiLCJpbmRleCIsIl9yZWYzIiwidGltZXMiLCJzdHJLZXkiLCJfcmVmNCIsImNvbmZpZ0NsYXNzTmFtZSIsImNvbmZpZ1N0eWxlIiwiY29uZmlnQ2xhc3NOYW1lcyIsImNvbmZpZ1N0eWxlcyIsInN0eWxlcyIsInJlc3RDb25maWciLCJkYXRhSW5kZXgiLCJmaW5kSW5kZXgiLCJpdGVtIiwic3RhY2tTdHlsZSIsInRyYW5zZm9ybVgiLCJfZGljdFJlZiRjdXJyZW50JHN0cksiLCJfZGljdFJlZiRjdXJyZW50JHN0cksyIiwiX2RpY3RSZWYkY3VycmVudCRzdHJLMyIsImhlaWdodCIsIm9mZnNldEhlaWdodCIsInZlcnRpY2FsT2Zmc2V0IiwiaSIsIl9kaWN0UmVmJGN1cnJlbnQka2V5cyIsInRyYW5zZm9ybVkiLCJzdGFydHNXaXRoIiwic2NhbGVYIiwib2Zmc2V0V2lkdGgiLCJ0cmFuc2Zvcm0iLCJyZWYiLCJ3cmFwcGVyIiwib25Nb3VzZUVudGVyIiwiaW5jbHVkZXMiLCJvbk1vdXNlTGVhdmUiLCJrIiwibm9kZSIsIm5vdGljZSIsImV2ZW50S2V5IiwiaG92ZXJpbmciLCJwcm9jZXNzIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NoticeList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/NotificationProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/rc-notification/es/NotificationProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationContext: () => (/* binding */ NotificationContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar NotificationContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createContext({});\nvar NotificationProvider = function NotificationProvider(_ref) {\n    var children = _ref.children, classNames = _ref.classNames;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NotificationContext.Provider, {\n        value: {\n            classNames: classNames\n        }\n    }, children);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NotificationProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL05vdGlmaWNhdGlvblByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDbkIsSUFBSUMsc0JBQXNCLFdBQVcsR0FBRUQsMERBQW1CLENBQUMsQ0FBQyxHQUFHO0FBQ3RFLElBQUlHLHVCQUF1QixTQUFTQSxxQkFBcUJDLElBQUk7SUFDM0QsSUFBSUMsV0FBV0QsS0FBS0MsUUFBUSxFQUMxQkMsYUFBYUYsS0FBS0UsVUFBVTtJQUM5QixPQUFPLFdBQVcsR0FBRU4sMERBQW1CLENBQUNDLG9CQUFvQk8sUUFBUSxFQUFFO1FBQ3BFQyxPQUFPO1lBQ0xILFlBQVlBO1FBQ2Q7SUFDRixHQUFHRDtBQUNMO0FBQ0EsaUVBQWVGLG9CQUFvQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2VycC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yYy1ub3RpZmljYXRpb24vZXMvTm90aWZpY2F0aW9uUHJvdmlkZXIuanM/MjJhYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBOb3RpZmljYXRpb25Db250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQoe30pO1xudmFyIE5vdGlmaWNhdGlvblByb3ZpZGVyID0gZnVuY3Rpb24gTm90aWZpY2F0aW9uUHJvdmlkZXIoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuLFxuICAgIGNsYXNzTmFtZXMgPSBfcmVmLmNsYXNzTmFtZXM7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChOb3RpZmljYXRpb25Db250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWU6IHtcbiAgICAgIGNsYXNzTmFtZXM6IGNsYXNzTmFtZXNcbiAgICB9XG4gIH0sIGNoaWxkcmVuKTtcbn07XG5leHBvcnQgZGVmYXVsdCBOb3RpZmljYXRpb25Qcm92aWRlcjsiXSwibmFtZXMiOlsiUmVhY3QiLCJOb3RpZmljYXRpb25Db250ZXh0IiwiY3JlYXRlQ29udGV4dCIsIk5vdGlmaWNhdGlvblByb3ZpZGVyIiwiX3JlZiIsImNoaWxkcmVuIiwiY2xhc3NOYW1lcyIsImNyZWF0ZUVsZW1lbnQiLCJQcm92aWRlciIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/Notifications.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-notification/es/Notifications.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _NoticeList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NoticeList */ \"(ssr)/./node_modules/rc-notification/es/NoticeList.js\");\n\n\n\n\n\n\n// ant-notification ant-notification-topRight\nvar Notifications = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(function(props, ref) {\n    var _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? \"rc-notification\" : _props$prefixCls, container = props.container, motion = props.motion, maxCount = props.maxCount, className = props.className, style = props.style, onAllRemoved = props.onAllRemoved, stack = props.stack, renderNotifications = props.renderNotifications;\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState([]), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2), configList = _React$useState2[0], setConfigList = _React$useState2[1];\n    // ======================== Close =========================\n    var onNoticeClose = function onNoticeClose(key) {\n        var _config$onClose;\n        // Trigger close event\n        var config = configList.find(function(item) {\n            return item.key === key;\n        });\n        config === null || config === void 0 || (_config$onClose = config.onClose) === null || _config$onClose === void 0 || _config$onClose.call(config);\n        setConfigList(function(list) {\n            return list.filter(function(item) {\n                return item.key !== key;\n            });\n        });\n    };\n    // ========================= Refs =========================\n    react__WEBPACK_IMPORTED_MODULE_3__.useImperativeHandle(ref, function() {\n        return {\n            open: function open(config) {\n                setConfigList(function(list) {\n                    var clone = (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(list);\n                    // Replace if exist\n                    var index = clone.findIndex(function(item) {\n                        return item.key === config.key;\n                    });\n                    var innerConfig = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, config);\n                    if (index >= 0) {\n                        var _list$index;\n                        innerConfig.times = (((_list$index = list[index]) === null || _list$index === void 0 ? void 0 : _list$index.times) || 0) + 1;\n                        clone[index] = innerConfig;\n                    } else {\n                        innerConfig.times = 0;\n                        clone.push(innerConfig);\n                    }\n                    if (maxCount > 0 && clone.length > maxCount) {\n                        clone = clone.slice(-maxCount);\n                    }\n                    return clone;\n                });\n            },\n            close: function close(key) {\n                onNoticeClose(key);\n            },\n            destroy: function destroy() {\n                setConfigList([]);\n            }\n        };\n    });\n    // ====================== Placements ======================\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState({}), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState3, 2), placements = _React$useState4[0], setPlacements = _React$useState4[1];\n    react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function() {\n        var nextPlacements = {};\n        configList.forEach(function(config) {\n            var _config$placement = config.placement, placement = _config$placement === void 0 ? \"topRight\" : _config$placement;\n            if (placement) {\n                nextPlacements[placement] = nextPlacements[placement] || [];\n                nextPlacements[placement].push(config);\n            }\n        });\n        // Fill exist placements to avoid empty list causing remove without motion\n        Object.keys(placements).forEach(function(placement) {\n            nextPlacements[placement] = nextPlacements[placement] || [];\n        });\n        setPlacements(nextPlacements);\n    }, [\n        configList\n    ]);\n    // Clean up container if all notices fade out\n    var onAllNoticeRemoved = function onAllNoticeRemoved(placement) {\n        setPlacements(function(originPlacements) {\n            var clone = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, originPlacements);\n            var list = clone[placement] || [];\n            if (!list.length) {\n                delete clone[placement];\n            }\n            return clone;\n        });\n    };\n    // Effect tell that placements is empty now\n    var emptyRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function() {\n        if (Object.keys(placements).length > 0) {\n            emptyRef.current = true;\n        } else if (emptyRef.current) {\n            // Trigger only when from exist to empty\n            onAllRemoved === null || onAllRemoved === void 0 || onAllRemoved();\n            emptyRef.current = false;\n        }\n    }, [\n        placements\n    ]);\n    // ======================== Render ========================\n    if (!container) {\n        return null;\n    }\n    var placementList = Object.keys(placements);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, placementList.map(function(placement) {\n        var placementConfigList = placements[placement];\n        var list = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(_NoticeList__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            key: placement,\n            configList: placementConfigList,\n            placement: placement,\n            prefixCls: prefixCls,\n            className: className === null || className === void 0 ? void 0 : className(placement),\n            style: style === null || style === void 0 ? void 0 : style(placement),\n            motion: motion,\n            onNoticeClose: onNoticeClose,\n            onAllNoticeRemoved: onAllNoticeRemoved,\n            stack: stack\n        });\n        return renderNotifications ? renderNotifications(list, {\n            prefixCls: prefixCls,\n            key: placement\n        }) : list;\n    })), container);\n});\nif (true) {\n    Notifications.displayName = \"Notifications\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Notifications);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/Notifications.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useNotification.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Notifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Notifications */ \"(ssr)/./node_modules/rc-notification/es/Notifications.js\");\n/* harmony import */ var rc_util__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util */ \"(ssr)/./node_modules/rc-util/es/index.js\");\n\n\n\nvar _excluded = [\n    \"getContainer\",\n    \"motion\",\n    \"prefixCls\",\n    \"maxCount\",\n    \"className\",\n    \"style\",\n    \"onAllRemoved\",\n    \"stack\",\n    \"renderNotifications\"\n];\n\n\n\nvar defaultGetContainer = function defaultGetContainer() {\n    return document.body;\n};\nvar uniqueKey = 0;\nfunction mergeConfig() {\n    var clone = {};\n    for(var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++){\n        objList[_key] = arguments[_key];\n    }\n    objList.forEach(function(obj) {\n        if (obj) {\n            Object.keys(obj).forEach(function(key) {\n                var val = obj[key];\n                if (val !== undefined) {\n                    clone[key] = val;\n                }\n            });\n        }\n    });\n    return clone;\n}\nfunction useNotification() {\n    var rootConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _rootConfig$getContai = rootConfig.getContainer, getContainer = _rootConfig$getContai === void 0 ? defaultGetContainer : _rootConfig$getContai, motion = rootConfig.motion, prefixCls = rootConfig.prefixCls, maxCount = rootConfig.maxCount, className = rootConfig.className, style = rootConfig.style, onAllRemoved = rootConfig.onAllRemoved, stack = rootConfig.stack, renderNotifications = rootConfig.renderNotifications, shareConfig = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rootConfig, _excluded);\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_3__.useState(), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 2), container = _React$useState2[0], setContainer = _React$useState2[1];\n    var notificationsRef = react__WEBPACK_IMPORTED_MODULE_3__.useRef();\n    var contextHolder = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(_Notifications__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        container: container,\n        ref: notificationsRef,\n        prefixCls: prefixCls,\n        motion: motion,\n        maxCount: maxCount,\n        className: className,\n        style: style,\n        onAllRemoved: onAllRemoved,\n        stack: stack,\n        renderNotifications: renderNotifications\n    });\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_3__.useState([]), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState3, 2), taskQueue = _React$useState4[0], setTaskQueue = _React$useState4[1];\n    var open = (0,rc_util__WEBPACK_IMPORTED_MODULE_5__.useEvent)(function(config) {\n        var mergedConfig = mergeConfig(shareConfig, config);\n        if (mergedConfig.key === null || mergedConfig.key === undefined) {\n            mergedConfig.key = \"rc-notification-\".concat(uniqueKey);\n            uniqueKey += 1;\n        }\n        setTaskQueue(function(queue) {\n            return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [\n                {\n                    type: \"open\",\n                    config: mergedConfig\n                }\n            ]);\n        });\n    });\n    // ========================= Refs =========================\n    var api = react__WEBPACK_IMPORTED_MODULE_3__.useMemo(function() {\n        return {\n            open: open,\n            close: function close(key) {\n                setTaskQueue(function(queue) {\n                    return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [\n                        {\n                            type: \"close\",\n                            key: key\n                        }\n                    ]);\n                });\n            },\n            destroy: function destroy() {\n                setTaskQueue(function(queue) {\n                    return [].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(queue), [\n                        {\n                            type: \"destroy\"\n                        }\n                    ]);\n                });\n            }\n        };\n    }, []);\n    // ======================= Container ======================\n    // React 18 should all in effect that we will check container in each render\n    // Which means getContainer should be stable.\n    react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function() {\n        setContainer(getContainer());\n    });\n    // ======================== Effect ========================\n    react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function() {\n        // Flush task when node ready\n        if (notificationsRef.current && taskQueue.length) {\n            taskQueue.forEach(function(task) {\n                switch(task.type){\n                    case \"open\":\n                        notificationsRef.current.open(task.config);\n                        break;\n                    case \"close\":\n                        notificationsRef.current.close(task.key);\n                        break;\n                    case \"destroy\":\n                        notificationsRef.current.destroy();\n                        break;\n                }\n            });\n            // https://github.com/ant-design/ant-design/issues/52590\n            // React `startTransition` will run once `useEffect` but many times `setState`,\n            // So `setTaskQueue` with filtered array will cause infinite loop.\n            // We cache the first match queue instead.\n            var oriTaskQueue;\n            var tgtTaskQueue;\n            // React 17 will mix order of effect & setState in async\n            // - open: setState[0]\n            // - effect[0]\n            // - open: setState[1]\n            // - effect setState([]) * here will clean up [0, 1] in React 17\n            setTaskQueue(function(oriQueue) {\n                if (oriTaskQueue !== oriQueue || !tgtTaskQueue) {\n                    oriTaskQueue = oriQueue;\n                    tgtTaskQueue = oriQueue.filter(function(task) {\n                        return !taskQueue.includes(task);\n                    });\n                }\n                return tgtTaskQueue;\n            });\n        }\n    }, [\n        taskQueue\n    ]);\n    // ======================== Return ========================\n    return [\n        api,\n        contextHolder\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/hooks/useStack.js":
/*!***********************************************************!*\
  !*** ./node_modules/rc-notification/es/hooks/useStack.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n\nvar DEFAULT_OFFSET = 8;\nvar DEFAULT_THRESHOLD = 3;\nvar DEFAULT_GAP = 16;\nvar useStack = function useStack(config) {\n    var result = {\n        offset: DEFAULT_OFFSET,\n        threshold: DEFAULT_THRESHOLD,\n        gap: DEFAULT_GAP\n    };\n    if (config && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(config) === \"object\") {\n        var _config$offset, _config$threshold, _config$gap;\n        result.offset = (_config$offset = config.offset) !== null && _config$offset !== void 0 ? _config$offset : DEFAULT_OFFSET;\n        result.threshold = (_config$threshold = config.threshold) !== null && _config$threshold !== void 0 ? _config$threshold : DEFAULT_THRESHOLD;\n        result.gap = (_config$gap = config.gap) !== null && _config$gap !== void 0 ? _config$gap : DEFAULT_GAP;\n    }\n    return [\n        !!config,\n        result\n    ];\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useStack);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/hooks/useStack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-notification/es/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rc-notification/es/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notice: () => (/* reexport safe */ _Notice__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NotificationProvider: () => (/* reexport safe */ _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   useNotification: () => (/* reexport safe */ _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _hooks_useNotification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/useNotification */ \"(ssr)/./node_modules/rc-notification/es/hooks/useNotification.js\");\n/* harmony import */ var _Notice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Notice */ \"(ssr)/./node_modules/rc-notification/es/Notice.js\");\n/* harmony import */ var _NotificationProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationProvider */ \"(ssr)/./node_modules/rc-notification/es/NotificationProvider.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtbm90aWZpY2F0aW9uL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFzRDtBQUN4QjtBQUM0QjtBQUNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJwLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3JjLW5vdGlmaWNhdGlvbi9lcy9pbmRleC5qcz9hMWI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB1c2VOb3RpZmljYXRpb24gZnJvbSBcIi4vaG9va3MvdXNlTm90aWZpY2F0aW9uXCI7XG5pbXBvcnQgTm90aWNlIGZyb20gXCIuL05vdGljZVwiO1xuaW1wb3J0IE5vdGlmaWNhdGlvblByb3ZpZGVyIGZyb20gXCIuL05vdGlmaWNhdGlvblByb3ZpZGVyXCI7XG5leHBvcnQgeyB1c2VOb3RpZmljYXRpb24sIE5vdGljZSwgTm90aWZpY2F0aW9uUHJvdmlkZXIgfTsiXSwibmFtZXMiOlsidXNlTm90aWZpY2F0aW9uIiwiTm90aWNlIiwiTm90aWZpY2F0aW9uUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-notification/es/index.js\n");

/***/ })

};
;