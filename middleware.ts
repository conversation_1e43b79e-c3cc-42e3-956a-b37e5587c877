/**
 * Next.js中间件
 * 
 * 实现路由保护和权限验证
 * 遵循PRD文档中的中间件设计要求
 */

import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { TokenValidator } from '@/services/auth/TokenValidator'

/**
 * 公开路径配置
 * 这些路径不需要认证即可访问
 */
const PUBLIC_PATHS = [
  '/login',
  '/forgot-password',
  '/api/auth/login',
  '/api/auth/refresh',
  '/_next',
  '/favicon.ico',
  '/logo.png'
]

/**
 * API路径配置
 * 需要特殊处理的API路径
 */
const API_PATHS = {
  AUTH: ['/api/auth/login', '/api/auth/refresh'],
  PROTECTED: ['/api/auth/logout', '/api/auth/profile']
}



/**
 * 检查路径是否为公开路径
 */
function isPublicPath(pathname: string): boolean {
  return PUBLIC_PATHS.some(path => {
    if (path.endsWith('*')) {
      return pathname.startsWith(path.slice(0, -1))
    }
    return pathname === path || pathname.startsWith(path + '/')
  })
}

/**
 * 检查路径是否为API路径
 */
function isApiPath(pathname: string): boolean {
  return pathname.startsWith('/api/')
}

/**
 * 检查是否为认证API路径
 */
function isAuthApiPath(pathname: string): boolean {
  return API_PATHS.AUTH.includes(pathname)
}

/**
 * 检查是否为受保护的API路径
 */
function isProtectedApiPath(pathname: string): boolean {
  return API_PATHS.PROTECTED.includes(pathname) || 
         (pathname.startsWith('/api/') && !isAuthApiPath(pathname))
}





/**
 * 创建重定向响应
 */
function createRedirectResponse(request: NextRequest, path: string) {
  const url = new URL(path, request.url)
  
  // 如果不是登录页面，保存原始URL用于登录后跳转
  if (path === '/login' && request.nextUrl.pathname !== '/login') {
    url.searchParams.set('redirect', request.nextUrl.pathname)
  }
  
  return NextResponse.redirect(url)
}

/**
 * 创建未授权响应
 */
function createUnauthorizedResponse(message: string = '未授权访问') {
  return NextResponse.json({
    status: 'error',
    data: null,
    message,
    code: 'UNAUTHORIZED',
    timestamp: new Date().toISOString(),
    requestId: TokenValidator.generateRequestId()
  }, { status: 401 })
}

/**
 * 创建禁止访问响应
 */
function createForbiddenResponse(message: string = '权限不足') {
  return NextResponse.json({
    status: 'error',
    data: null,
    message,
    code: 'FORBIDDEN',
    timestamp: new Date().toISOString(),
    requestId: TokenValidator.generateRequestId()
  }, { status: 403 })
}

/**
 * 中间件主函数
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 1. 检查是否为公开路径
  if (isPublicPath(pathname)) {
    return NextResponse.next()
  }

  // 2. 获取认证Token
  const accessToken = request.cookies.get('auth-token')?.value
  const refreshToken = request.cookies.get('refresh-token')?.value
  
  // 调试：输出Cookie状态
  console.log(`🔍 [Middleware] 访问路径: ${pathname}`)
  console.log(`🔍 [Middleware] Cookie状态: auth-token=${!!accessToken}, refresh-token=${!!refreshToken}`)
  if (accessToken) {
    console.log(`🔍 [Middleware] Token长度: ${accessToken.length}`)
  }

  // 3. 处理API路径
  if (isApiPath(pathname)) {
    // 认证API路径不需要Token验证
    if (isAuthApiPath(pathname)) {
      return NextResponse.next()
    }

    // 受保护的API路径需要Token验证
    if (isProtectedApiPath(pathname)) {
      if (!accessToken) {
        return createUnauthorizedResponse('Access Token不存在')
      }

      // 使用统一的Token验证服务
      const tokenInfo = TokenValidator.parseJWTToken(accessToken)
      if (!tokenInfo.valid) {
        return createUnauthorizedResponse(tokenInfo.error || 'Access Token格式无效')
      }

      if (tokenInfo.expired) {
        if (refreshToken) {
          // Token过期但有Refresh Token，返回特殊状态码提示前端刷新
          return NextResponse.json({
            status: 'error',
            data: null,
            message: 'Access Token已过期',
            code: 'TOKEN_EXPIRED',
            timestamp: new Date().toISOString(),
            requestId: TokenValidator.generateRequestId()
          }, { status: 401 })
        }

        return createUnauthorizedResponse('Access Token已过期')
      }

      // Token有效，继续处理请求
      return NextResponse.next()
    }

    // 其他API路径直接通过
    return NextResponse.next()
  }

  // 4. 处理页面路径
  if (!accessToken) {
    // 没有Token，重定向到登录页
    console.log(`🔒 [Middleware] 未认证访问: ${pathname}`)
    return createRedirectResponse(request, '/login')
  }

  // 5. 验证Access Token
  const tokenInfo = TokenValidator.parseJWTToken(accessToken)
  if (!tokenInfo.valid) {
    console.log(`🔒 [Middleware] Token格式无效: ${pathname}, 错误: ${tokenInfo.error}`)
    return createRedirectResponse(request, '/login')
  }

  if (tokenInfo.expired) {
    if (refreshToken) {
      // Token过期但有Refresh Token，重定向到登录页并提示
      console.log(`🔒 [Middleware] Token过期: ${pathname}`)
      return createRedirectResponse(request, '/login?expired=true')
    }

    // Token过期，重定向到登录页
    console.log(`🔒 [Middleware] Token过期: ${pathname}`)
    return createRedirectResponse(request, '/login')
  }

  // 6. 检查页面权限
  const userPermissions = tokenInfo.payload?.permissions || []
  if (!TokenValidator.checkPagePermission(userPermissions, pathname)) {
    console.log(`🚫 [Middleware] 权限不足: ${pathname}, 用户权限: ${userPermissions.join(', ')}`)
    return createRedirectResponse(request, '/403')
  }

  // 7. 检查Token是否即将过期（剩余时间少于5分钟）
  if (TokenValidator.isTokenNearExpiry(tokenInfo.payload, 5)) {
    const response = NextResponse.next()
    response.headers.set('X-Token-Refresh-Needed', 'true')
    return response
  }

  // 8. 验证通过，继续处理请求
  console.log(`✅ [Middleware] 访问授权: ${pathname}`)
  return NextResponse.next()
}

/**
 * 中间件配置
 * 定义中间件应用的路径匹配规则
 */
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了:
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - public文件夹中的文件
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
