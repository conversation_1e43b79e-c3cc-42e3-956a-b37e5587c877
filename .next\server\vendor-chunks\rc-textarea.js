"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-textarea";
exports.ids = ["vendor-chunks/rc-textarea"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-textarea/es/ResizableTextArea.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./calculateNodeHeight */ \"(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\");\n\n\n\n\n\n\nvar _excluded = [\n    \"prefixCls\",\n    \"defaultValue\",\n    \"value\",\n    \"autoSize\",\n    \"onResize\",\n    \"className\",\n    \"style\",\n    \"disabled\",\n    \"onChange\",\n    \"onInternalAutoSize\"\n];\n\n\n\n\n\n\n\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function(props, ref) {\n    var _ref = props, prefixCls = _ref.prefixCls, defaultValue = _ref.defaultValue, value = _ref.value, autoSize = _ref.autoSize, onResize = _ref.onResize, className = _ref.className, style = _ref.style, disabled = _ref.disabled, onChange = _ref.onChange, onInternalAutoSize = _ref.onInternalAutoSize, restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n    // =============================== Value ================================\n    var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(defaultValue, {\n        value: value,\n        postState: function postState(val) {\n            return val !== null && val !== void 0 ? val : \"\";\n        }\n    }), _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2), mergedValue = _useMergedState2[0], setMergedValue = _useMergedState2[1];\n    var onInternalChange = function onInternalChange(event) {\n        setMergedValue(event.target.value);\n        onChange === null || onChange === void 0 || onChange(event);\n    };\n    // ================================ Ref =================================\n    var textareaRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n    react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle(ref, function() {\n        return {\n            textArea: textareaRef.current\n        };\n    });\n    // ============================== AutoSize ==============================\n    var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_11__.useMemo(function() {\n        if (autoSize && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(autoSize) === \"object\") {\n            return [\n                autoSize.minRows,\n                autoSize.maxRows\n            ];\n        }\n        return [];\n    }, [\n        autoSize\n    ]), _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useMemo, 2), minRows = _React$useMemo2[0], maxRows = _React$useMemo2[1];\n    var needAutoSize = !!autoSize;\n    // =============================== Scroll ===============================\n    // https://github.com/ant-design/ant-design/issues/21870\n    var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n        try {\n            // FF has bug with jump of scroll to top. We force back here.\n            if (document.activeElement === textareaRef.current) {\n                var _textareaRef$current = textareaRef.current, selectionStart = _textareaRef$current.selectionStart, selectionEnd = _textareaRef$current.selectionEnd, scrollTop = _textareaRef$current.scrollTop;\n                // Fix Safari bug which not rollback when break line\n                // This makes Chinese IME can't input. Do not fix this\n                // const { value: tmpValue } = textareaRef.current;\n                // textareaRef.current.value = '';\n                // textareaRef.current.value = tmpValue;\n                textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n                textareaRef.current.scrollTop = scrollTop;\n            }\n        } catch (e) {\n        // Fix error in Chrome:\n        // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n        // http://stackoverflow.com/q/21177489/3040605\n        }\n    };\n    // =============================== Resize ===============================\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_11__.useState(RESIZE_STABLE), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2), resizeState = _React$useState2[0], setResizeState = _React$useState2[1];\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11__.useState(), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2), autoSizeStyle = _React$useState4[0], setAutoSizeStyle = _React$useState4[1];\n    var startResize = function startResize() {\n        setResizeState(RESIZE_START);\n        if (false) {}\n    };\n    // Change to trigger resize measure\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        if (needAutoSize) {\n            startResize();\n        }\n    }, [\n        value,\n        minRows,\n        maxRows,\n        needAutoSize\n    ]);\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        if (resizeState === RESIZE_START) {\n            setResizeState(RESIZE_MEASURING);\n        } else if (resizeState === RESIZE_MEASURING) {\n            var textareaStyles = (0,_calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(textareaRef.current, false, minRows, maxRows);\n            // Safari has bug that text will keep break line on text cut when it's prev is break line.\n            // ZombieJ: This not often happen. So we just skip it.\n            // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n            // const { value: tmpValue } = textareaRef.current;\n            // textareaRef.current.value = '';\n            // textareaRef.current.value = tmpValue;\n            // if (document.activeElement === textareaRef.current) {\n            //   textareaRef.current.scrollTop = scrollTop;\n            //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n            // }\n            setResizeState(RESIZE_STABLE);\n            setAutoSizeStyle(textareaStyles);\n        } else {\n            fixFirefoxAutoScroll();\n        }\n    }, [\n        resizeState\n    ]);\n    // We lock resize trigger by raf to avoid Safari warning\n    var resizeRafRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n    var cleanRaf = function cleanRaf() {\n        rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"].cancel(resizeRafRef.current);\n    };\n    var onInternalResize = function onInternalResize(size) {\n        if (resizeState === RESIZE_STABLE) {\n            onResize === null || onResize === void 0 || onResize(size);\n            if (autoSize) {\n                cleanRaf();\n                resizeRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n                    startResize();\n                });\n            }\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_11__.useEffect(function() {\n        return cleanRaf;\n    }, []);\n    // =============================== Render ===============================\n    var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n    var mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), mergedAutoSizeStyle);\n    if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n        mergedStyle.overflowY = \"hidden\";\n        mergedStyle.overflowX = \"hidden\";\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        onResize: onInternalResize,\n        disabled: !(autoSize || onResize)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, {\n        ref: textareaRef,\n        style: mergedStyle,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n        disabled: disabled,\n        value: mergedValue,\n        onChange: onInternalChange\n    })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResizableTextArea);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/TextArea.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-textarea/es/TextArea.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-input */ \"(ssr)/./node_modules/rc-input/es/index.js\");\n/* harmony import */ var rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-input/es/hooks/useCount */ \"(ssr)/./node_modules/rc-input/es/hooks/useCount.js\");\n/* harmony import */ var rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-input/es/utils/commonUtils */ \"(ssr)/./node_modules/rc-input/es/utils/commonUtils.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n\n\n\n\nvar _excluded = [\n    \"defaultValue\",\n    \"value\",\n    \"onFocus\",\n    \"onBlur\",\n    \"onChange\",\n    \"allowClear\",\n    \"maxLength\",\n    \"onCompositionStart\",\n    \"onCompositionEnd\",\n    \"suffix\",\n    \"prefixCls\",\n    \"showCount\",\n    \"count\",\n    \"className\",\n    \"style\",\n    \"disabled\",\n    \"hidden\",\n    \"classNames\",\n    \"styles\",\n    \"onResize\",\n    \"onClear\",\n    \"onPressEnter\",\n    \"readOnly\",\n    \"autoSize\",\n    \"onKeyDown\"\n];\n\n\n\n\n\n\n\nvar TextArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().forwardRef(function(_ref, ref) {\n    var _countConfig$max;\n    var defaultValue = _ref.defaultValue, customValue = _ref.value, onFocus = _ref.onFocus, onBlur = _ref.onBlur, onChange = _ref.onChange, allowClear = _ref.allowClear, maxLength = _ref.maxLength, onCompositionStart = _ref.onCompositionStart, onCompositionEnd = _ref.onCompositionEnd, suffix = _ref.suffix, _ref$prefixCls = _ref.prefixCls, prefixCls = _ref$prefixCls === void 0 ? \"rc-textarea\" : _ref$prefixCls, showCount = _ref.showCount, count = _ref.count, className = _ref.className, style = _ref.style, disabled = _ref.disabled, hidden = _ref.hidden, classNames = _ref.classNames, styles = _ref.styles, onResize = _ref.onResize, onClear = _ref.onClear, onPressEnter = _ref.onPressEnter, readOnly = _ref.readOnly, autoSize = _ref.autoSize, onKeyDown = _ref.onKeyDown, rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n    var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(defaultValue, {\n        value: customValue,\n        defaultValue: defaultValue\n    }), _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2), value = _useMergedState2[0], setValue = _useMergedState2[1];\n    var formatValue = value === undefined || value === null ? \"\" : String(value);\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_11___default().useState(false), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2), focused = _React$useState2[0], setFocused = _React$useState2[1];\n    var compositionRef = react__WEBPACK_IMPORTED_MODULE_11___default().useRef(false);\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2), textareaResized = _React$useState4[0], setTextareaResized = _React$useState4[1];\n    // =============================== Ref ================================\n    var holderRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n    var resizableTextAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n    var getTextArea = function getTextArea() {\n        var _resizableTextAreaRef;\n        return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;\n    };\n    var focus = function focus() {\n        getTextArea().focus();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle)(ref, function() {\n        var _holderRef$current;\n        return {\n            resizableTextArea: resizableTextAreaRef.current,\n            focus: focus,\n            blur: function blur() {\n                getTextArea().blur();\n            },\n            nativeElement: ((_holderRef$current = holderRef.current) === null || _holderRef$current === void 0 ? void 0 : _holderRef$current.nativeElement) || getTextArea()\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function() {\n        setFocused(function(prev) {\n            return !disabled && prev;\n        });\n    }, [\n        disabled\n    ]);\n    // =========================== Select Range ===========================\n    var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null), _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState5, 2), selection = _React$useState6[0], setSelection = _React$useState6[1];\n    react__WEBPACK_IMPORTED_MODULE_11___default().useEffect(function() {\n        if (selection) {\n            var _getTextArea;\n            (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(selection));\n        }\n    }, [\n        selection\n    ]);\n    // ============================== Count ===============================\n    var countConfig = (0,rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(count, showCount);\n    var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;\n    // Max length value\n    var hasMaxLength = Number(mergedMax) > 0;\n    var valueLength = countConfig.strategy(formatValue);\n    var isOutOfRange = !!mergedMax && valueLength > mergedMax;\n    // ============================== Change ==============================\n    var triggerChange = function triggerChange(e, currentValue) {\n        var cutValue = currentValue;\n        if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {\n            cutValue = countConfig.exceedFormatter(currentValue, {\n                max: countConfig.max\n            });\n            if (currentValue !== cutValue) {\n                setSelection([\n                    getTextArea().selectionStart || 0,\n                    getTextArea().selectionEnd || 0\n                ]);\n            }\n        }\n        setValue(cutValue);\n        (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(e.currentTarget, e, onChange, cutValue);\n    };\n    // =========================== Value Update ===========================\n    var onInternalCompositionStart = function onInternalCompositionStart(e) {\n        compositionRef.current = true;\n        onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);\n    };\n    var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n        compositionRef.current = false;\n        triggerChange(e, e.currentTarget.value);\n        onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);\n    };\n    var onInternalChange = function onInternalChange(e) {\n        triggerChange(e, e.target.value);\n    };\n    var handleKeyDown = function handleKeyDown(e) {\n        if (e.key === \"Enter\" && onPressEnter) {\n            onPressEnter(e);\n        }\n        onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);\n    };\n    var handleFocus = function handleFocus(e) {\n        setFocused(true);\n        onFocus === null || onFocus === void 0 || onFocus(e);\n    };\n    var handleBlur = function handleBlur(e) {\n        setFocused(false);\n        onBlur === null || onBlur === void 0 || onBlur(e);\n    };\n    // ============================== Reset ===============================\n    var handleReset = function handleReset(e) {\n        setValue(\"\");\n        focus();\n        (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(getTextArea(), e, onChange);\n    };\n    var suffixNode = suffix;\n    var dataCount;\n    if (countConfig.show) {\n        if (countConfig.showFormatter) {\n            dataCount = countConfig.showFormatter({\n                value: formatValue,\n                count: valueLength,\n                maxLength: mergedMax\n            });\n        } else {\n            dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(mergedMax) : \"\");\n        }\n        suffixNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement((react__WEBPACK_IMPORTED_MODULE_11___default().Fragment), null, suffixNode, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"span\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n            style: styles === null || styles === void 0 ? void 0 : styles.count\n        }, dataCount));\n    }\n    var handleResize = function handleResize(size) {\n        var _getTextArea2;\n        onResize === null || onResize === void 0 || onResize(size);\n        if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {\n            setTextareaResized(true);\n        }\n    };\n    var isPureTextArea = !autoSize && !showCount && !allowClear;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_input__WEBPACK_IMPORTED_MODULE_7__.BaseInput, {\n        ref: holderRef,\n        value: formatValue,\n        allowClear: allowClear,\n        handleReset: handleReset,\n        suffix: suffixNode,\n        prefixCls: prefixCls,\n        classNames: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, classNames), {}, {\n            affixWrapper: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-show-count\"), showCount), \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear))\n        }),\n        disabled: disabled,\n        focused: focused,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(className, isOutOfRange && \"\".concat(prefixCls, \"-out-of-range\")),\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), textareaResized && !isPureTextArea ? {\n            height: \"auto\"\n        } : {}),\n        dataAttrs: {\n            affixWrapper: {\n                \"data-count\": typeof dataCount === \"string\" ? dataCount : undefined\n            }\n        },\n        hidden: hidden,\n        readOnly: readOnly,\n        onClear: onClear\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, rest, {\n        autoSize: autoSize,\n        maxLength: maxLength,\n        onKeyDown: handleKeyDown,\n        onChange: onInternalChange,\n        onFocus: handleFocus,\n        onBlur: handleBlur,\n        onCompositionStart: onInternalCompositionStart,\n        onCompositionEnd: onInternalCompositionEnd,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n            resize: style === null || style === void 0 ? void 0 : style.resize\n        }),\n        disabled: disabled,\n        prefixCls: prefixCls,\n        onResize: handleResize,\n        ref: resizableTextAreaRef,\n        readOnly: readOnly\n    })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextArea);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/TextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-textarea/es/calculateNodeHeight.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNodeStyling: () => (/* binding */ calculateNodeStyling),\n/* harmony export */   \"default\": () => (/* binding */ calculateAutoSizeStyle)\n/* harmony export */ });\n// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */ var HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = [\n    \"letter-spacing\",\n    \"line-height\",\n    \"padding-top\",\n    \"padding-bottom\",\n    \"font-family\",\n    \"font-weight\",\n    \"font-size\",\n    \"font-variant\",\n    \"text-rendering\",\n    \"text-transform\",\n    \"width\",\n    \"text-indent\",\n    \"padding-left\",\n    \"padding-right\",\n    \"border-width\",\n    \"box-sizing\",\n    \"word-break\",\n    \"white-space\"\n];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nfunction calculateNodeStyling(node) {\n    var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var nodeRef = node.getAttribute(\"id\") || node.getAttribute(\"data-reactid\") || node.getAttribute(\"name\");\n    if (useCache && computedStyleCache[nodeRef]) {\n        return computedStyleCache[nodeRef];\n    }\n    var style = window.getComputedStyle(node);\n    var boxSizing = style.getPropertyValue(\"box-sizing\") || style.getPropertyValue(\"-moz-box-sizing\") || style.getPropertyValue(\"-webkit-box-sizing\");\n    var paddingSize = parseFloat(style.getPropertyValue(\"padding-bottom\")) + parseFloat(style.getPropertyValue(\"padding-top\"));\n    var borderSize = parseFloat(style.getPropertyValue(\"border-bottom-width\")) + parseFloat(style.getPropertyValue(\"border-top-width\"));\n    var sizingStyle = SIZING_STYLE.map(function(name) {\n        return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n    }).join(\";\");\n    var nodeInfo = {\n        sizingStyle: sizingStyle,\n        paddingSize: paddingSize,\n        borderSize: borderSize,\n        boxSizing: boxSizing\n    };\n    if (useCache && nodeRef) {\n        computedStyleCache[nodeRef] = nodeInfo;\n    }\n    return nodeInfo;\n}\nfunction calculateAutoSizeStyle(uiTextNode) {\n    var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    if (!hiddenTextarea) {\n        hiddenTextarea = document.createElement(\"textarea\");\n        hiddenTextarea.setAttribute(\"tab-index\", \"-1\");\n        hiddenTextarea.setAttribute(\"aria-hidden\", \"true\");\n        // fix: A form field element should have an id or name attribute\n        // A form field element has neither an id nor a name attribute. This might prevent the browser from correctly autofilling the form.\n        // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/textarea\n        hiddenTextarea.setAttribute(\"name\", \"hiddenTextarea\");\n        document.body.appendChild(hiddenTextarea);\n    }\n    // Fix wrap=\"off\" issue\n    // https://github.com/ant-design/ant-design/issues/6577\n    if (uiTextNode.getAttribute(\"wrap\")) {\n        hiddenTextarea.setAttribute(\"wrap\", uiTextNode.getAttribute(\"wrap\"));\n    } else {\n        hiddenTextarea.removeAttribute(\"wrap\");\n    }\n    // Copy all CSS properties that have an impact on the height of the content in\n    // the textbox\n    var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache), paddingSize = _calculateNodeStyling.paddingSize, borderSize = _calculateNodeStyling.borderSize, boxSizing = _calculateNodeStyling.boxSizing, sizingStyle = _calculateNodeStyling.sizingStyle;\n    // Need to have the overflow attribute to hide the scrollbar otherwise\n    // text-lines will not calculated properly as the shadow will technically be\n    // narrower for content\n    hiddenTextarea.setAttribute(\"style\", \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n    hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || \"\";\n    var minHeight = undefined;\n    var maxHeight = undefined;\n    var overflowY;\n    var height = hiddenTextarea.scrollHeight;\n    if (boxSizing === \"border-box\") {\n        // border-box: add border, since height = content + padding + border\n        height += borderSize;\n    } else if (boxSizing === \"content-box\") {\n        // remove padding, since height = content\n        height -= paddingSize;\n    }\n    if (minRows !== null || maxRows !== null) {\n        // measure height of a textarea with a single row\n        hiddenTextarea.value = \" \";\n        var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n        if (minRows !== null) {\n            minHeight = singleRowHeight * minRows;\n            if (boxSizing === \"border-box\") {\n                minHeight = minHeight + paddingSize + borderSize;\n            }\n            height = Math.max(minHeight, height);\n        }\n        if (maxRows !== null) {\n            maxHeight = singleRowHeight * maxRows;\n            if (boxSizing === \"border-box\") {\n                maxHeight = maxHeight + paddingSize + borderSize;\n            }\n            overflowY = height > maxHeight ? \"\" : \"hidden\";\n            height = Math.min(maxHeight, height);\n        }\n    }\n    var style = {\n        height: height,\n        overflowY: overflowY,\n        resize: \"none\"\n    };\n    if (minHeight) {\n        style.minHeight = minHeight;\n    }\n    if (maxHeight) {\n        style.maxHeight = maxHeight;\n    }\n    return style;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-textarea/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResizableTextArea: () => (/* reexport safe */ _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _TextArea__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextArea */ \"(ssr)/./node_modules/rc-textarea/es/TextArea.js\");\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_TextArea__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNpQztBQUNuRSxpRUFBZUEsaURBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcnAtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanM/ZGMyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVGV4dEFyZWEgZnJvbSBcIi4vVGV4dEFyZWFcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUmVzaXphYmxlVGV4dEFyZWEgfSBmcm9tIFwiLi9SZXNpemFibGVUZXh0QXJlYVwiO1xuZXhwb3J0IGRlZmF1bHQgVGV4dEFyZWE7Il0sIm5hbWVzIjpbIlRleHRBcmVhIiwiZGVmYXVsdCIsIlJlc2l6YWJsZVRleHRBcmVhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/index.js\n");

/***/ })

};
;