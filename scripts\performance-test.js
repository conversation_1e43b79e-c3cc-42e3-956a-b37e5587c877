#!/usr/bin/env node

/**
 * 认证和权限管理系统性能测试脚本
 * 
 * 根据PRD要求测试系统性能：
 * - 登录响应 < 2秒
 * - 页面加载 < 3秒  
 * - 支持20人并发
 * - 满足小企业需求
 */

const { performance } = require('perf_hooks')
const cluster = require('cluster')
const os = require('os')

/**
 * 性能测试配置
 */
const PERFORMANCE_CONFIG = {
  // PRD要求的性能指标
  targets: {
    loginResponse: 2000,      // 登录响应 < 2秒
    pageLoad: 3000,          // 页面加载 < 3秒
    tokenVerification: 500,   // Token验证 < 500ms
    permissionCheck: 100,     // 权限检查 < 100ms
    concurrentUsers: 20       // 支持20人并发
  },
  
  // 测试参数
  test: {
    iterations: 100,          // 每个测试的迭代次数
    warmupIterations: 10,     // 预热迭代次数
    concurrentBatches: 5,     // 并发批次数
    timeout: 30000           // 测试超时时间
  }
}

/**
 * 性能测试结果
 */
class PerformanceResult {
  constructor(testName) {
    this.testName = testName
    this.measurements = []
    this.startTime = null
    this.endTime = null
  }

  start() {
    this.startTime = performance.now()
  }

  end() {
    this.endTime = performance.now()
    const duration = this.endTime - this.startTime
    this.measurements.push(duration)
    return duration
  }

  getStats() {
    if (this.measurements.length === 0) {
      return null
    }

    const sorted = [...this.measurements].sort((a, b) => a - b)
    const avg = this.measurements.reduce((sum, val) => sum + val, 0) / this.measurements.length
    const min = sorted[0]
    const max = sorted[sorted.length - 1]
    const p50 = sorted[Math.floor(sorted.length * 0.5)]
    const p95 = sorted[Math.floor(sorted.length * 0.95)]
    const p99 = sorted[Math.floor(sorted.length * 0.99)]

    return {
      testName: this.testName,
      count: this.measurements.length,
      avg: Math.round(avg * 100) / 100,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
      p50: Math.round(p50 * 100) / 100,
      p95: Math.round(p95 * 100) / 100,
      p99: Math.round(p99 * 100) / 100
    }
  }
}

/**
 * 模拟登录性能测试
 */
async function testLoginPerformance() {
  console.log('🔐 测试登录性能...')
  
  const result = new PerformanceResult('登录响应时间')
  
  // 预热
  for (let i = 0; i < PERFORMANCE_CONFIG.test.warmupIterations; i++) {
    await simulateLogin()
  }
  
  // 正式测试
  for (let i = 0; i < PERFORMANCE_CONFIG.test.iterations; i++) {
    result.start()
    await simulateLogin()
    const duration = result.end()
    
    if (i % 20 === 0) {
      process.stdout.write(`   进度: ${i}/${PERFORMANCE_CONFIG.test.iterations} (当前: ${Math.round(duration)}ms)\r`)
    }
  }
  
  console.log('') // 换行
  return result.getStats()
}

/**
 * 模拟Token验证性能测试
 */
async function testTokenVerificationPerformance() {
  console.log('🔍 测试Token验证性能...')
  
  const result = new PerformanceResult('Token验证时间')
  
  // 预热
  for (let i = 0; i < PERFORMANCE_CONFIG.test.warmupIterations; i++) {
    await simulateTokenVerification()
  }
  
  // 正式测试
  for (let i = 0; i < PERFORMANCE_CONFIG.test.iterations; i++) {
    result.start()
    await simulateTokenVerification()
    const duration = result.end()
    
    if (i % 20 === 0) {
      process.stdout.write(`   进度: ${i}/${PERFORMANCE_CONFIG.test.iterations} (当前: ${Math.round(duration)}ms)\r`)
    }
  }
  
  console.log('') // 换行
  return result.getStats()
}

/**
 * 模拟权限检查性能测试
 */
async function testPermissionCheckPerformance() {
  console.log('🛡️ 测试权限检查性能...')
  
  const result = new PerformanceResult('权限检查时间')
  
  // 预热
  for (let i = 0; i < PERFORMANCE_CONFIG.test.warmupIterations; i++) {
    await simulatePermissionCheck()
  }
  
  // 正式测试
  for (let i = 0; i < PERFORMANCE_CONFIG.test.iterations; i++) {
    result.start()
    await simulatePermissionCheck()
    const duration = result.end()
    
    if (i % 20 === 0) {
      process.stdout.write(`   进度: ${i}/${PERFORMANCE_CONFIG.test.iterations} (当前: ${Math.round(duration)}ms)\r`)
    }
  }
  
  console.log('') // 换行
  return result.getStats()
}

/**
 * 并发用户测试
 */
async function testConcurrentUsers() {
  console.log('👥 测试并发用户性能...')
  
  const concurrentUsers = PERFORMANCE_CONFIG.targets.concurrentUsers
  const promises = []
  
  console.log(`   模拟 ${concurrentUsers} 个并发用户...`)
  
  const startTime = performance.now()
  
  // 创建并发用户会话
  for (let i = 0; i < concurrentUsers; i++) {
    promises.push(simulateConcurrentUserSession(i + 1))
  }
  
  // 等待所有用户会话完成
  const results = await Promise.all(promises)
  const endTime = performance.now()
  
  const totalTime = endTime - startTime
  const avgResponseTime = results.reduce((sum, result) => sum + result.avgResponseTime, 0) / results.length
  const successRate = results.filter(result => result.success).length / results.length * 100
  
  return {
    testName: '并发用户测试',
    concurrentUsers,
    totalTime: Math.round(totalTime),
    avgResponseTime: Math.round(avgResponseTime * 100) / 100,
    successRate: Math.round(successRate * 100) / 100,
    results
  }
}

/**
 * 模拟单个用户会话
 */
async function simulateConcurrentUserSession(userId) {
  const sessionResult = {
    userId,
    success: true,
    operations: [],
    avgResponseTime: 0
  }
  
  try {
    // 模拟用户操作序列
    const operations = [
      { name: 'login', fn: simulateLogin },
      { name: 'tokenVerification', fn: simulateTokenVerification },
      { name: 'permissionCheck', fn: simulatePermissionCheck },
      { name: 'pageLoad', fn: simulatePageLoad },
      { name: 'dataAccess', fn: simulateDataAccess }
    ]
    
    for (const operation of operations) {
      const startTime = performance.now()
      await operation.fn()
      const duration = performance.now() - startTime
      
      sessionResult.operations.push({
        name: operation.name,
        duration: Math.round(duration * 100) / 100
      })
    }
    
    sessionResult.avgResponseTime = sessionResult.operations.reduce(
      (sum, op) => sum + op.duration, 0
    ) / sessionResult.operations.length
    
  } catch (error) {
    sessionResult.success = false
    sessionResult.error = error.message
  }
  
  return sessionResult
}

/**
 * 模拟登录操作
 */
async function simulateLogin() {
  // 模拟网络延迟和处理时间
  const processingTime = Math.random() * 800 + 200 // 200-1000ms
  await new Promise(resolve => setTimeout(resolve, processingTime))
  
  // 模拟数据库查询
  await simulateDatabaseQuery(50, 150) // 50-200ms
  
  // 模拟JWT生成
  await simulateJWTGeneration(10, 50) // 10-60ms
}

/**
 * 模拟Token验证操作
 */
async function simulateTokenVerification() {
  // 模拟JWT验证
  const verificationTime = Math.random() * 100 + 50 // 50-150ms
  await new Promise(resolve => setTimeout(resolve, verificationTime))
  
  // 模拟用户信息查询
  await simulateDatabaseQuery(20, 80) // 20-100ms
}

/**
 * 模拟权限检查操作
 */
async function simulatePermissionCheck() {
  // 模拟权限计算
  const checkTime = Math.random() * 30 + 10 // 10-40ms
  await new Promise(resolve => setTimeout(resolve, checkTime))
}

/**
 * 模拟页面加载操作
 */
async function simulatePageLoad() {
  // 模拟页面渲染和资源加载
  const loadTime = Math.random() * 1500 + 500 // 500-2000ms
  await new Promise(resolve => setTimeout(resolve, loadTime))
}

/**
 * 模拟数据访问操作
 */
async function simulateDataAccess() {
  // 模拟数据查询和处理
  const accessTime = Math.random() * 300 + 100 // 100-400ms
  await new Promise(resolve => setTimeout(resolve, accessTime))
}

/**
 * 模拟数据库查询
 */
async function simulateDatabaseQuery(minMs, maxMs) {
  const queryTime = Math.random() * (maxMs - minMs) + minMs
  await new Promise(resolve => setTimeout(resolve, queryTime))
}

/**
 * 模拟JWT生成
 */
async function simulateJWTGeneration(minMs, maxMs) {
  const genTime = Math.random() * (maxMs - minMs) + minMs
  await new Promise(resolve => setTimeout(resolve, genTime))
}

/**
 * 检查性能指标是否达标
 */
function checkPerformanceTargets(results) {
  console.log('\n📊 性能指标检查:')
  console.log('=' .repeat(60))
  
  const checks = []
  
  // 检查登录响应时间
  const loginResult = results.find(r => r.testName === '登录响应时间')
  if (loginResult) {
    const passed = loginResult.avg <= PERFORMANCE_CONFIG.targets.loginResponse
    checks.push({
      metric: '登录响应时间',
      actual: `${loginResult.avg}ms`,
      target: `< ${PERFORMANCE_CONFIG.targets.loginResponse}ms`,
      passed
    })
  }
  
  // 检查Token验证时间
  const tokenResult = results.find(r => r.testName === 'Token验证时间')
  if (tokenResult) {
    const passed = tokenResult.avg <= PERFORMANCE_CONFIG.targets.tokenVerification
    checks.push({
      metric: 'Token验证时间',
      actual: `${tokenResult.avg}ms`,
      target: `< ${PERFORMANCE_CONFIG.targets.tokenVerification}ms`,
      passed
    })
  }
  
  // 检查权限检查时间
  const permissionResult = results.find(r => r.testName === '权限检查时间')
  if (permissionResult) {
    const passed = permissionResult.avg <= PERFORMANCE_CONFIG.targets.permissionCheck
    checks.push({
      metric: '权限检查时间',
      actual: `${permissionResult.avg}ms`,
      target: `< ${PERFORMANCE_CONFIG.targets.permissionCheck}ms`,
      passed
    })
  }
  
  // 检查并发用户测试
  const concurrentResult = results.find(r => r.testName === '并发用户测试')
  if (concurrentResult) {
    const passed = concurrentResult.successRate >= 95 && concurrentResult.avgResponseTime <= 3000
    checks.push({
      metric: '并发用户支持',
      actual: `${concurrentResult.concurrentUsers}用户, ${concurrentResult.successRate}%成功率`,
      target: `${PERFORMANCE_CONFIG.targets.concurrentUsers}用户, >95%成功率`,
      passed
    })
  }
  
  // 显示检查结果
  checks.forEach(check => {
    const status = check.passed ? '✅' : '❌'
    console.log(`${status} ${check.metric}:`)
    console.log(`   实际: ${check.actual}`)
    console.log(`   目标: ${check.target}`)
    console.log('')
  })
  
  const allPassed = checks.every(check => check.passed)
  
  if (allPassed) {
    console.log('🎉 所有性能指标均达标!')
    console.log('✅ 系统性能满足PRD要求')
  } else {
    console.log('⚠️ 部分性能指标未达标')
    console.log('🔧 建议进行性能优化')
  }
  
  return allPassed
}

/**
 * 主函数
 */
async function main() {
  console.log('⚡ ERP系统认证模块性能测试')
  console.log('📋 PRD性能要求:')
  console.log(`   - 登录响应: < ${PERFORMANCE_CONFIG.targets.loginResponse}ms`)
  console.log(`   - 页面加载: < ${PERFORMANCE_CONFIG.targets.pageLoad}ms`)
  console.log(`   - 并发用户: ${PERFORMANCE_CONFIG.targets.concurrentUsers}人`)
  console.log('')
  
  const results = []
  
  try {
    // 1. 登录性能测试
    const loginResult = await testLoginPerformance()
    results.push(loginResult)
    console.log(`   ✅ 平均响应时间: ${loginResult.avg}ms`)
    console.log('')
    
    // 2. Token验证性能测试
    const tokenResult = await testTokenVerificationPerformance()
    results.push(tokenResult)
    console.log(`   ✅ 平均验证时间: ${tokenResult.avg}ms`)
    console.log('')
    
    // 3. 权限检查性能测试
    const permissionResult = await testPermissionCheckPerformance()
    results.push(permissionResult)
    console.log(`   ✅ 平均检查时间: ${permissionResult.avg}ms`)
    console.log('')
    
    // 4. 并发用户测试
    const concurrentResult = await testConcurrentUsers()
    results.push(concurrentResult)
    console.log(`   ✅ 并发测试完成: ${concurrentResult.successRate}% 成功率`)
    console.log('')
    
    // 5. 检查性能指标
    const allPassed = checkPerformanceTargets(results)
    
    // 6. 生成性能报告
    generatePerformanceReport(results)
    
    if (!allPassed) {
      process.exit(1)
    }
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error.message)
    process.exit(1)
  }
}

/**
 * 生成性能报告
 */
function generatePerformanceReport(results) {
  console.log('\n📋 详细性能报告:')
  console.log('=' .repeat(60))
  
  results.forEach(result => {
    if (result.testName === '并发用户测试') {
      console.log(`📊 ${result.testName}:`)
      console.log(`   并发用户数: ${result.concurrentUsers}`)
      console.log(`   总耗时: ${result.totalTime}ms`)
      console.log(`   平均响应时间: ${result.avgResponseTime}ms`)
      console.log(`   成功率: ${result.successRate}%`)
    } else {
      console.log(`📊 ${result.testName}:`)
      console.log(`   测试次数: ${result.count}`)
      console.log(`   平均时间: ${result.avg}ms`)
      console.log(`   最小时间: ${result.min}ms`)
      console.log(`   最大时间: ${result.max}ms`)
      console.log(`   P95: ${result.p95}ms`)
      console.log(`   P99: ${result.p99}ms`)
    }
    console.log('')
  })
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  testLoginPerformance,
  testTokenVerificationPerformance,
  testPermissionCheckPerformance,
  testConcurrentUsers,
  PERFORMANCE_CONFIG
}
