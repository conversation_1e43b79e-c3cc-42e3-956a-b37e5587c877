# ERP系统认证模块性能优化指南

## 概述

本文档提供了针对ERP系统认证和权限管理模块的性能优化建议，确保系统满足PRD文档中的性能要求：

- **登录响应时间**: < 2秒
- **页面加载时间**: < 3秒
- **并发用户支持**: 20人
- **Token验证时间**: < 500ms
- **权限检查时间**: < 100ms

## 性能优化策略

### 1. 认证性能优化

#### 1.1 JWT Token优化
```typescript
// 优化JWT配置，减少Token大小
export const JWT_CONFIG = {
  accessToken: {
    expiresIn: '8h',
    algorithm: 'HS256', // 使用更快的算法
    // 减少payload大小
    minimalPayload: true
  }
}

// 简化Token payload
const tokenPayload = {
  userId: user.id,
  username: user.username,
  role: user.role, // 只包含必要信息
  exp: expirationTime
}
```

#### 1.2 密码验证优化
```typescript
// 使用适当的bcrypt rounds（平衡安全性和性能）
const BCRYPT_ROUNDS = 10 // 小企业推荐值

// 异步密码验证，避免阻塞
async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}
```

#### 1.3 会话管理优化
```typescript
// 使用内存缓存减少数据库查询
const sessionCache = new Map<string, SessionData>()

// 批量清理过期会话
setInterval(() => {
  cleanupExpiredSessions()
}, 5 * 60 * 1000) // 每5分钟清理一次
```

### 2. 权限检查优化

#### 2.1 权限缓存策略
```typescript
// 用户权限缓存
const permissionCache = new Map<string, UserPermissions>()

class SimplePermissionManager {
  static checkPermission(user: User, permission: string): boolean {
    // 优先从缓存获取
    const cacheKey = `${user.id}:${permission}`
    if (permissionCache.has(cacheKey)) {
      return permissionCache.get(cacheKey)!
    }
    
    // 计算权限并缓存
    const hasPermission = this.calculatePermission(user, permission)
    permissionCache.set(cacheKey, hasPermission)
    
    return hasPermission
  }
}
```

#### 2.2 权限预计算
```typescript
// 登录时预计算用户权限
async function precomputeUserPermissions(user: User): Promise<void> {
  const commonPermissions = [
    'data:view',
    'data:edit',
    'orders:create',
    'admin:users:create'
  ]
  
  commonPermissions.forEach(permission => {
    const hasPermission = SimplePermissionManager.checkPermission(user, permission)
    permissionCache.set(`${user.id}:${permission}`, hasPermission)
  })
}
```

### 3. 数据库性能优化

#### 3.1 索引优化
```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_email ON users(email);

-- 会话表索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 角色权限表索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
```

#### 3.2 查询优化
```typescript
// 使用连接查询减少数据库往返
async function getUserWithRoles(userId: string): Promise<User> {
  const query = `
    SELECT 
      u.*,
      r.code as role_code,
      r.name as role_name
    FROM users u
    LEFT JOIN user_roles ur ON u.id = ur.user_id
    LEFT JOIN roles r ON ur.role_id = r.id
    WHERE u.id = ? AND u.status = 'active'
  `
  
  return await database.query(query, [userId])
}
```

### 4. 前端性能优化

#### 4.1 组件懒加载
```typescript
// 权限组件懒加载
const AdminPanel = lazy(() => import('@/components/admin/AdminPanel'))
const UserManagement = lazy(() => import('@/components/admin/UserManagement'))

// 使用Suspense包装
<Suspense fallback={<Loading />}>
  <AdminOnly>
    <AdminPanel />
  </AdminOnly>
</Suspense>
```

#### 4.2 权限检查优化
```typescript
// 使用useMemo缓存权限检查结果
export const usePermissionCheck = (permission: string) => {
  const { user } = useSimpleAuth()
  
  return useMemo(() => {
    if (!user) return false
    return SimplePermissionManager.checkPermission(user, permission).hasPermission
  }, [user, permission])
}
```

#### 4.3 状态管理优化
```typescript
// 减少不必要的重新渲染
const AuthContext = createContext<AuthContextType>()

export const AuthProvider: React.FC = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>()
  
  // 使用useCallback避免函数重新创建
  const login = useCallback(async (credentials) => {
    // 登录逻辑
  }, [])
  
  // 使用useMemo优化context值
  const contextValue = useMemo(() => ({
    ...authState,
    login,
    logout
  }), [authState, login, logout])
  
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}
```

### 5. 网络性能优化

#### 5.1 请求优化
```typescript
// 使用请求去重避免重复请求
const requestCache = new Map<string, Promise<any>>()

async function cachedRequest(url: string, options: RequestInit): Promise<any> {
  const cacheKey = `${url}:${JSON.stringify(options)}`
  
  if (requestCache.has(cacheKey)) {
    return requestCache.get(cacheKey)!
  }
  
  const promise = fetch(url, options).then(res => res.json())
  requestCache.set(cacheKey, promise)
  
  // 清理缓存
  setTimeout(() => {
    requestCache.delete(cacheKey)
  }, 5000)
  
  return promise
}
```

#### 5.2 批量操作
```typescript
// 批量权限检查
async function batchPermissionCheck(
  user: User, 
  permissions: string[]
): Promise<Record<string, boolean>> {
  const results: Record<string, boolean> = {}
  
  permissions.forEach(permission => {
    results[permission] = SimplePermissionManager.checkPermission(user, permission).hasPermission
  })
  
  return results
}
```

### 6. 内存管理优化

#### 6.1 缓存清理策略
```typescript
// 定期清理权限缓存
class PermissionCacheManager {
  private static readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟
  private static readonly MAX_CACHE_SIZE = 1000
  
  static cleanupCache(): void {
    if (permissionCache.size > this.MAX_CACHE_SIZE) {
      // 清理最旧的缓存项
      const entries = Array.from(permissionCache.entries())
      const toDelete = entries.slice(0, entries.length - this.MAX_CACHE_SIZE)
      
      toDelete.forEach(([key]) => {
        permissionCache.delete(key)
      })
    }
  }
  
  static startCleanupTimer(): void {
    setInterval(() => {
      this.cleanupCache()
    }, this.CACHE_TTL)
  }
}
```

### 7. 监控和调试

#### 7.1 性能监控
```typescript
// 性能监控装饰器
function performanceMonitor(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value
  
  descriptor.value = async function (...args: any[]) {
    const start = performance.now()
    
    try {
      const result = await method.apply(this, args)
      const duration = performance.now() - start
      
      // 记录性能数据
      console.log(`[Performance] ${propertyName}: ${duration.toFixed(2)}ms`)
      
      // 如果超过阈值，发出警告
      if (duration > 1000) {
        console.warn(`[Performance Warning] ${propertyName} took ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      const duration = performance.now() - start
      console.error(`[Performance Error] ${propertyName}: ${duration.toFixed(2)}ms`, error)
      throw error
    }
  }
}

// 使用示例
class AuthService {
  @performanceMonitor
  async login(credentials: LoginRequest): Promise<LoginResult> {
    // 登录逻辑
  }
}
```

#### 7.2 性能分析工具
```typescript
// 简单的性能分析器
class PerformanceProfiler {
  private static measurements: Map<string, number[]> = new Map()
  
  static start(label: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const duration = performance.now() - startTime
      
      if (!this.measurements.has(label)) {
        this.measurements.set(label, [])
      }
      
      this.measurements.get(label)!.push(duration)
    }
  }
  
  static getReport(): Record<string, any> {
    const report: Record<string, any> = {}
    
    this.measurements.forEach((durations, label) => {
      const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length
      const min = Math.min(...durations)
      const max = Math.max(...durations)
      
      report[label] = {
        count: durations.length,
        avg: Math.round(avg * 100) / 100,
        min: Math.round(min * 100) / 100,
        max: Math.round(max * 100) / 100
      }
    })
    
    return report
  }
}
```

## 性能测试

### 运行性能测试
```bash
# 运行完整性能测试
node scripts/performance-test.js

# 运行特定测试
npm run test:performance:login
npm run test:performance:concurrent
```

### 性能基准
- **登录响应**: 目标 < 2秒，优秀 < 1秒
- **Token验证**: 目标 < 500ms，优秀 < 200ms
- **权限检查**: 目标 < 100ms，优秀 < 50ms
- **页面加载**: 目标 < 3秒，优秀 < 2秒
- **并发支持**: 目标 20用户，优秀 50用户

## 部署优化

### 生产环境配置
```typescript
// 生产环境优化配置
export const PRODUCTION_CONFIG = {
  // 启用缓存
  enableCache: true,
  cacheSize: 1000,
  cacheTTL: 5 * 60 * 1000,
  
  // 数据库连接池
  database: {
    poolSize: 10,
    maxConnections: 20,
    connectionTimeout: 5000
  },
  
  // JWT配置
  jwt: {
    algorithm: 'HS256',
    expiresIn: '8h'
  }
}
```

### 服务器优化
```bash
# Nginx配置示例
server {
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain application/json application/javascript text/css;
    
    # 静态资源缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API请求
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_cache_valid 200 5m;
    }
}
```

## 总结

通过实施以上优化策略，ERP系统的认证和权限管理模块能够：

1. **满足性能要求**: 登录 < 2秒，页面加载 < 3秒
2. **支持并发访问**: 稳定支持20人同时使用
3. **提供良好体验**: 快速响应，流畅操作
4. **保持系统稳定**: 内存使用合理，无性能瓶颈

定期运行性能测试，监控系统表现，根据实际使用情况调整优化策略。
