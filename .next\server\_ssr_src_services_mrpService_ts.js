"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_mrpService_ts";
exports.ids = ["_ssr_src_services_mrpService_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/mrpService.ts":
/*!************************************!*\
  !*** ./src/services/mrpService.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   mrpService: () => (/* binding */ mrpService)\n/* harmony export */ });\n/* harmony import */ var _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataAccess/DataAccessManager */ \"(ssr)/./src/services/dataAccess/DataAccessManager.ts\");\n/* harmony import */ var _BusinessIdGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BusinessIdGenerator */ \"(ssr)/./src/services/BusinessIdGenerator.ts\");\n/**\n * MRP（物料需求计划）服务\n * 完整版本，支持共享模具处理功能\n */ \n\n/**\n * MRP服务类\n */ class MRPService {\n    static getInstance() {\n        if (!MRPService.instance) {\n            MRPService.instance = new MRPService();\n        }\n        return MRPService.instance;\n    }\n    /**\n   * 执行MRP\n   */ async executeMRP(request) {\n        const { salesOrder, executedBy, executionDate } = request;\n        console.log(\"\\uD83D\\uDE80 [MRPService] 开始执行MRP\", {\n            orderId: salesOrder.id,\n            orderNumber: salesOrder.orderNumber,\n            executedBy\n        });\n        try {\n            // 1. 库存检查\n            const inventoryCheckResults = await this.checkInventory(salesOrder);\n            // 2. 分析共享模具情况\n            const sharedMoldAnalysis = await this.analyzeSharedMolds(salesOrder, inventoryCheckResults);\n            // 🔧 修复：生成MRP执行ID，用于生产订单创建\n            const mrpExecutionId = `mrp-${Date.now()}`;\n            // 3. 生成生产订单（包含共享模具处理）\n            const generatedProductionOrders = await this.generateProductionOrdersWithSharedMold(salesOrder, inventoryCheckResults, sharedMoldAnalysis, mrpExecutionId, executedBy);\n            // 4. 计算统计信息\n            const totalShortageValue = inventoryCheckResults.reduce((sum, item)=>sum + item.shortageQuantity * 100, 0);\n            // 5. 统计共享模具订单和传统订单数量\n            const sharedMoldOrders = generatedProductionOrders.filter((order)=>order.isSharedMold).length;\n            const traditionalOrders = generatedProductionOrders.filter((order)=>!order.isSharedMold).length;\n            // 6. 构建MRP结果\n            const mrpResult = {\n                id: mrpExecutionId,\n                salesOrderId: salesOrder.id,\n                salesOrderNumber: salesOrder.orderNumber,\n                executionDate,\n                status: generatedProductionOrders.length > 0 ? \"success\" : \"partial_shortage\",\n                totalProductionOrders: generatedProductionOrders.length,\n                sharedMoldOrders,\n                traditionalOrders,\n                inventoryCheckResults,\n                generatedProductionOrders,\n                totalShortageValue,\n                executedBy,\n                executedAt: new Date().toISOString()\n            };\n            console.log(\"✅ [MRPService] MRP执行完成\", {\n                resultId: mrpResult.id,\n                totalOrders: mrpResult.totalProductionOrders,\n                sharedMoldOrders: mrpResult.sharedMoldOrders,\n                traditionalOrders: mrpResult.traditionalOrders,\n                status: mrpResult.status\n            });\n            return mrpResult;\n        } catch (error) {\n            console.error(\"❌ [MRPService] MRP执行失败:\", error);\n            // 返回失败结果\n            return {\n                id: `mrp-error-${Date.now()}`,\n                salesOrderId: salesOrder.id,\n                salesOrderNumber: salesOrder.orderNumber,\n                executionDate,\n                status: \"failed\",\n                totalProductionOrders: 0,\n                sharedMoldOrders: 0,\n                traditionalOrders: 0,\n                inventoryCheckResults: [],\n                generatedProductionOrders: [],\n                totalShortageValue: 0,\n                executedBy,\n                executedAt: new Date().toISOString()\n            };\n        }\n    }\n    /**\n   * 检查库存\n   */ async checkInventory(salesOrder) {\n        const results = [];\n        for (const item of salesOrder.items){\n            try {\n                // 获取产品库存信息 - 修复：使用正确的API调用方式\n                const inventoryResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.inventory.getByProductCode(item.productCode);\n                let availableQuantity = 0;\n                if (inventoryResponse.status === \"success\" && inventoryResponse.data) {\n                    // 使用currentStock字段作为可用数量\n                    availableQuantity = inventoryResponse.data.currentStock || 0;\n                }\n                const requiredQuantity = item.quantity;\n                const shortageQuantity = Math.max(0, requiredQuantity - availableQuantity);\n                results.push({\n                    productCode: item.productCode,\n                    productName: item.productName,\n                    requiredQuantity,\n                    availableQuantity,\n                    shortageQuantity,\n                    isSufficient: shortageQuantity === 0\n                });\n            } catch (error) {\n                console.error(`❌ [MRPService] 库存检查失败 - 产品: ${item.productCode}`, error);\n                // 如果库存检查失败，假设需要全部生产\n                results.push({\n                    productCode: item.productCode,\n                    productName: item.productName,\n                    requiredQuantity: item.quantity,\n                    availableQuantity: 0,\n                    shortageQuantity: item.quantity,\n                    isSufficient: false\n                });\n            }\n        }\n        return results;\n    }\n    /**\n   * 分析共享模具情况\n   */ async analyzeSharedMolds(salesOrder, inventoryCheckResults) {\n        console.log(\"\\uD83D\\uDD0D [MRPService] 开始分析共享模具情况\");\n        const sharedMoldGroups = [];\n        const moldProductMap = new Map();\n        // 1. 按模具编号分组产品\n        for (const checkResult of inventoryCheckResults){\n            if (checkResult.shortageQuantity > 0) {\n                try {\n                    // 获取产品主数据中的成型模具编号\n                    const productResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(checkResult.productCode);\n                    if (productResponse.status === \"success\" && productResponse.data) {\n                        const moldNumber = productResponse.data.formingMold || \"\";\n                        if (moldNumber) {\n                            const productRequirement = {\n                                productCode: checkResult.productCode,\n                                productName: checkResult.productName,\n                                productModelCode: productResponse.data.modelCode,\n                                requiredQuantity: checkResult.requiredQuantity,\n                                deliveryDate: salesOrder.deliveryDate,\n                                sourceOrderId: salesOrder.id,\n                                sourceOrderNumber: salesOrder.orderNumber,\n                                customerName: salesOrder.customerName,\n                                urgencyLevel: \"medium\"\n                            };\n                            if (!moldProductMap.has(moldNumber)) {\n                                moldProductMap.set(moldNumber, []);\n                            }\n                            moldProductMap.get(moldNumber).push(productRequirement);\n                        }\n                    }\n                } catch (error) {\n                    console.error(`❌ 获取产品 ${checkResult.productCode} 主数据失败:`, error);\n                }\n            }\n        }\n        // 2. 识别共享模具（多个产品使用同一模具）\n        for (const [moldNumber, products] of moldProductMap.entries()){\n            if (products.length > 1) {\n                console.log(`🔧 发现共享模具: ${moldNumber}, 包含 ${products.length} 个产品`);\n                const maxRequiredQuantity = Math.max(...products.map((p)=>p.requiredQuantity));\n                // 计算最大缺货量\n                const maxShortageQuantity = Math.max(...products.map((p)=>{\n                    const checkResult = inventoryCheckResults.find((r)=>r.productCode === p.productCode);\n                    return checkResult?.shortageQuantity || 0;\n                }));\n                const sharedMoldGroup = {\n                    moldNumber,\n                    deliveryDate: salesOrder.deliveryDate,\n                    products,\n                    isFirstOccurrence: true,\n                    maxRequiredQuantity,\n                    maxShortageQuantity\n                };\n                sharedMoldGroups.push(sharedMoldGroup);\n            }\n        }\n        console.log(`✅ [MRPService] 共享模具分析完成，发现 ${sharedMoldGroups.length} 个共享模具组`);\n        return sharedMoldGroups;\n    }\n    /**\n   * 生成生产订单（包含共享模具处理）\n   * 🔧 修复：添加MRP执行信息参数，用于新的createFromMRP方法\n   */ async generateProductionOrdersWithSharedMold(salesOrder, inventoryCheckResults, sharedMoldGroups, mrpExecutionId, executedBy) {\n        const productionOrders = [];\n        const processedProducts = new Set();\n        // 1. 处理共享模具订单\n        for (const moldGroup of sharedMoldGroups){\n            const sharedMoldOrder = await this.generateSharedMoldOrder(moldGroup, inventoryCheckResults, salesOrder, mrpExecutionId, executedBy);\n            if (sharedMoldOrder) {\n                productionOrders.push(sharedMoldOrder);\n                // 标记已处理的产品\n                moldGroup.products.forEach((p)=>processedProducts.add(p.productCode));\n            }\n        }\n        // 2. 处理传统订单（未被共享模具处理的产品）\n        for (const checkResult of inventoryCheckResults){\n            if (checkResult.shortageQuantity > 0 && !processedProducts.has(checkResult.productCode)) {\n                const traditionalOrder = await this.generateTraditionalOrder(checkResult, salesOrder, mrpExecutionId, executedBy);\n                if (traditionalOrder) {\n                    productionOrders.push(traditionalOrder);\n                }\n            }\n        }\n        return productionOrders;\n    }\n    /**\n   * 生成传统生产订单（原有逻辑）\n   * 🔧 修复：添加MRP执行信息参数，用于新的createFromMRP方法\n   */ async generateTraditionalOrder(checkResult, salesOrder, mrpExecutionId, executedBy) {\n        try {\n            // 获取产品主数据中的成型模具编号\n            let formingMoldNumber = \"\";\n            const productResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.products.getByCode(checkResult.productCode);\n            if (productResponse.status === \"success\" && productResponse.data) {\n                formingMoldNumber = productResponse.data.formingMold || \"\";\n                console.log(`✅ 获取产品 ${checkResult.productCode} 的成型模具编号: ${formingMoldNumber}`);\n            } else {\n                console.warn(`⚠️ 未找到产品 ${checkResult.productCode} 的主数据，模具编号将为空`);\n            }\n            // 获取客户信息和信用等级\n            let customerCreditLevel = undefined;\n            const customerResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.customers.getById(salesOrder.customerId);\n            if (customerResponse.status === \"success\" && customerResponse.data) {\n                customerCreditLevel = customerResponse.data.customerLevel;\n                console.log(`✅ 获取客户 ${salesOrder.customerName} 的信用等级: ${customerCreditLevel}`);\n            } else {\n                console.warn(`⚠️ 未找到客户 ${salesOrder.customerId} 的主数据，信用等级将为空`);\n            }\n            // 🔧 简化：基于销售订单号生成生产订单ID，固定格式\n            const orderNumber = _BusinessIdGenerator__WEBPACK_IMPORTED_MODULE_1__.BusinessIdGenerator.generateProductionOrderId(salesOrder.orderNumber);\n            const productionOrder = {\n                id: `po-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                orderNumber,\n                // 产品信息\n                productName: checkResult.productName,\n                productCode: checkResult.productCode,\n                // 共享模具相关字段\n                formingMoldNumber: formingMoldNumber,\n                isSharedMold: false,\n                productItems: [],\n                // 数量字段\n                plannedQuantity: checkResult.shortageQuantity,\n                producedQuantity: 0,\n                // 时间字段\n                startDate: new Date().toISOString().split(\"T\")[0],\n                endDate: salesOrder.deliveryDate,\n                deliveryDate: salesOrder.deliveryDate,\n                // 状态字段\n                status: \"in_plan\",\n                workstation: \"默认工位\",\n                // 客户信息\n                customerName: salesOrder.customerName,\n                customerId: salesOrder.customerId,\n                customerCreditLevel: customerCreditLevel,\n                prioritySource: \"auto\",\n                // 订单追溯\n                salesOrderNumber: salesOrder.orderNumber,\n                sourceOrderNumbers: [\n                    salesOrder.orderNumber\n                ],\n                sourceOrderIds: [\n                    salesOrder.id\n                ],\n                // 其他字段\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // 🔧 修复：使用新的createFromMRP方法，添加MRP执行信息\n            const createResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.productionOrders.createFromMRP({\n                ...productionOrder,\n                mrpExecutionId,\n                mrpExecutedBy: executedBy,\n                mrpExecutedAt: new Date().toISOString()\n            });\n            if (createResponse.status !== \"success\") {\n                throw new Error(`生产订单创建失败: ${createResponse.message}`);\n            }\n            console.log(`✅ [MRPService] 传统生产订单已保存: ${productionOrder.orderNumber}, MRP执行ID: ${mrpExecutionId}`);\n            return productionOrder;\n        } catch (error) {\n            console.error(`❌ [MRPService] 生成传统订单失败 - 产品: ${checkResult.productCode}`, error);\n            return null;\n        }\n    }\n    /**\n   * 生成共享模具生产订单\n   * 🔧 修复：添加MRP执行信息参数，用于新的createFromMRP方法\n   */ async generateSharedMoldOrder(moldGroup, inventoryCheckResults, salesOrder, mrpExecutionId, executedBy) {\n        try {\n            console.log(`🔧 [MRPService] 开始生成共享模具订单: ${moldGroup.moldNumber}`);\n            // 获取客户信息和信用等级\n            let customerCreditLevel = undefined;\n            const customerResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.customers.getById(salesOrder.customerId);\n            if (customerResponse.status === \"success\" && customerResponse.data) {\n                customerCreditLevel = customerResponse.data.customerLevel;\n            }\n            // 🔧 简化：基于销售订单号生成生产订单ID，固定格式\n            const orderNumber = _BusinessIdGenerator__WEBPACK_IMPORTED_MODULE_1__.BusinessIdGenerator.generateProductionOrderId(salesOrder.orderNumber);\n            // 构建产品明细列表\n            const productItems = moldGroup.products.map((product)=>({\n                    id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                    productCode: product.productCode,\n                    productName: product.productName,\n                    plannedQuantity: product.requiredQuantity,\n                    producedQuantity: 0,\n                    requiredQuantity: product.requiredQuantity,\n                    sourceOrderItems: [\n                        salesOrder.id\n                    ] // 简化处理\n                }));\n            // 使用最大缺货量作为计划数量（如果没有则使用最大需求量）\n            const plannedQuantity = moldGroup.maxShortageQuantity ?? moldGroup.maxRequiredQuantity;\n            // 主产品信息（使用第一个产品作为主产品）\n            const primaryProduct = moldGroup.products[0];\n            const sharedMoldOrder = {\n                id: `po-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                orderNumber,\n                // 产品信息（共享模具订单的特殊处理）\n                productName: `${moldGroup.moldNumber} 共享模具生产`,\n                productCode: primaryProduct.productCode,\n                // 共享模具相关字段\n                formingMoldNumber: moldGroup.moldNumber,\n                isSharedMold: true,\n                moldGroup: moldGroup.moldNumber,\n                productItems,\n                // 数量字段\n                plannedQuantity,\n                producedQuantity: 0,\n                // 时间字段\n                startDate: new Date().toISOString().split(\"T\")[0],\n                endDate: moldGroup.deliveryDate,\n                deliveryDate: moldGroup.deliveryDate,\n                // 状态字段\n                status: \"in_plan\",\n                workstation: \"默认工位\",\n                // 客户信息\n                customerName: salesOrder.customerName,\n                customerId: salesOrder.customerId,\n                customerCreditLevel: customerCreditLevel,\n                prioritySource: \"auto\",\n                // 订单追溯\n                salesOrderNumber: salesOrder.orderNumber,\n                sourceOrderNumbers: [\n                    ...new Set(moldGroup.products.map((p)=>p.sourceOrderNumber))\n                ],\n                sourceOrderIds: [\n                    ...new Set(moldGroup.products.map((p)=>p.sourceOrderId))\n                ],\n                // 其他字段\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            // 🔧 修复：使用新的createFromMRP方法，添加MRP执行信息\n            const createResponse = await _dataAccess_DataAccessManager__WEBPACK_IMPORTED_MODULE_0__.dataAccessManager.productionOrders.createFromMRP({\n                ...sharedMoldOrder,\n                mrpExecutionId,\n                mrpExecutedBy: executedBy,\n                mrpExecutedAt: new Date().toISOString()\n            });\n            if (createResponse.status !== \"success\") {\n                throw new Error(`共享模具生产订单创建失败: ${createResponse.message}`);\n            }\n            console.log(`✅ [MRPService] 共享模具生产订单已保存: ${sharedMoldOrder.orderNumber}, 生产数量: ${plannedQuantity}, MRP执行ID: ${mrpExecutionId}`);\n            return sharedMoldOrder;\n        } catch (error) {\n            console.error(`❌ [MRPService] 生成共享模具订单失败 - 模具: ${moldGroup.moldNumber}`, error);\n            return null;\n        }\n    }\n}\n// 导出单例实例\nconst mrpService = MRPService.getInstance();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (mrpService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/mrpService.ts\n");

/***/ })

};
;