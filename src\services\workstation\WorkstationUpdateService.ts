/**
 * 工位统一更新服务
 * 
 * 功能说明：
 * - 提供统一的工位状态更新入口，确保数据一致性
 * - 实现乐观锁机制防止并发更新冲突
 * - 集中处理权限验证、冲突检测和日志记录
 * - 支持不同来源的更新操作（用户、系统、排程）
 * 
 * 设计原则：
 * - 单一职责：专门负责工位状态更新
 * - 原子操作：确保更新操作的原子性
 * - 错误处理：提供详细的错误信息和恢复建议
 * - 审计日志：记录所有更新操作的详细信息
 * 
 * @version 1.0.0
 * <AUTHOR>
 * @since 2025-07-19
 */

import { Workstation } from '@/types'
import { WorkstationDataAccessService } from '@/services/dataAccess/WorkstationDataAccessService'
import { ApiResponse } from '@/services/dataAccess/DataAccessLayer'

/**
 * 更新来源类型
 */
export type UpdateSource = 'user' | 'system' | 'scheduling' | 'batch'

/**
 * 更新操作类型
 */
export type UpdateOperation = 'status_change' | 'production_assignment' | 'queue_management' | 'reset' | 'general'

/**
 * 更新上下文信息
 */
export interface UpdateContext {
  source: UpdateSource           // 更新来源
  operation: UpdateOperation     // 操作类型
  userId?: string               // 用户ID（如果是用户操作）
  reason?: string               // 更新原因
  metadata?: Record<string, any> // 额外的元数据
}

/**
 * 更新结果
 */
export interface UpdateResult {
  success: boolean
  workstation?: Workstation
  error?: string
  conflictInfo?: {
    expectedVersion: number
    currentVersion: number
    lastModifiedBy: string
    lastModifiedAt: string
  }
  warnings?: string[]
}

/**
 * 批量更新结果
 */
export interface BatchUpdateResult {
  totalCount: number
  successCount: number
  failureCount: number
  results: Array<{
    workstationId: string
    result: UpdateResult
  }>
  summary: {
    conflicts: number
    errors: number
    warnings: number
  }
}

/**
 * 工位统一更新服务类
 */
export class WorkstationUpdateService {
  private static instance: WorkstationUpdateService
  private workstationService: WorkstationDataAccessService

  private constructor() {
    this.workstationService = WorkstationDataAccessService.getInstance()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): WorkstationUpdateService {
    if (!WorkstationUpdateService.instance) {
      WorkstationUpdateService.instance = new WorkstationUpdateService()
    }
    return WorkstationUpdateService.instance
  }

  /**
   * 统一工位更新入口
   * @param workstationId 工位ID
   * @param updates 更新数据
   * @param context 更新上下文
   * @param expectedVersion 期望版本号（用于乐观锁）
   */
  async updateWorkstation(
    workstationId: string,
    updates: Partial<Workstation>,
    context: UpdateContext,
    expectedVersion?: number
  ): Promise<UpdateResult> {
    const startTime = Date.now()
    
    try {
      console.log(`🔄 [WorkstationUpdateService] 开始更新工位 ${workstationId}`, {
        source: context.source,
        operation: context.operation,
        expectedVersion,
        reason: context.reason
      })

      // 1. 权限验证
      const permissionCheck = await this.validatePermissions(workstationId, context)
      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`
        }
      }

      // 2. 业务规则验证
      const businessValidation = await this.validateBusinessRules(workstationId, updates, context)
      if (!businessValidation.valid) {
        return {
          success: false,
          error: `业务规则验证失败: ${businessValidation.reason}`,
          warnings: businessValidation.warnings
        }
      }

      // 3. 执行更新（使用乐观锁）
      const modifiedBy = this.getModifiedBy(context)
      const updateResponse = await this.workstationService.update(
        workstationId,
        updates,
        expectedVersion,
        modifiedBy
      )

      if (updateResponse.status !== 'success') {
        // 处理版本冲突
        if (updateResponse.code && updateResponse.code.toString() === 'VERSION_CONFLICT') {
          const conflictInfo = updateResponse.data as any
          return {
            success: false,
            error: updateResponse.message || '版本冲突',
            conflictInfo: {
              expectedVersion: expectedVersion || 0,
              currentVersion: conflictInfo?.currentVersion || 0,
              lastModifiedBy: 'unknown',
              lastModifiedAt: new Date().toISOString()
            }
          }
        }

        return {
          success: false,
          error: updateResponse.message || '更新失败'
        }
      }

      // 4. 记录审计日志
      if (updateResponse.data) {
        await this.logUpdateOperation(workstationId, updates, context, updateResponse.data, startTime)
      }

      // 5. 清除相关缓存 - 🔧 修复：确保缓存清理在状态更新后立即执行
      await this.clearWorkstationCaches(workstationId, context.operation)

      // 🔧 修复：额外清理DataAccessManager的工位数据缓存
      await this.clearDataAccessManagerCaches()

      // 6. 触发后置处理
      if (updateResponse.data) {
        await this.triggerPostUpdateActions(workstationId, updates, context, updateResponse.data)
      }

      console.log(`✅ [WorkstationUpdateService] 工位 ${workstationId} 更新成功`, {
        duration: Date.now() - startTime,
        newVersion: updateResponse.data?.version
      })

      return {
        success: true,
        workstation: updateResponse.data,
        warnings: businessValidation.warnings
      }

    } catch (error) {
      console.error(`❌ [WorkstationUpdateService] 工位 ${workstationId} 更新失败:`, error)
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 批量更新工位
   * @param updates 批量更新数据
   * @param context 更新上下文
   */
  async batchUpdateWorkstations(
    updates: Array<{
      workstationId: string
      updates: Partial<Workstation>
      expectedVersion?: number
    }>,
    context: UpdateContext
  ): Promise<BatchUpdateResult> {
    console.log(`🔄 [WorkstationUpdateService] 开始批量更新 ${updates.length} 个工位`)

    const results: BatchUpdateResult['results'] = []
    let successCount = 0
    let failureCount = 0
    let conflicts = 0
    let errors = 0
    let warnings = 0

    for (const update of updates) {
      const result = await this.updateWorkstation(
        update.workstationId,
        update.updates,
        context,
        update.expectedVersion
      )

      results.push({
        workstationId: update.workstationId,
        result
      })

      if (result.success) {
        successCount++
      } else {
        failureCount++
        if (result.conflictInfo) {
          conflicts++
        } else {
          errors++
        }
      }

      if (result.warnings && result.warnings.length > 0) {
        warnings++
      }
    }

    const batchResult: BatchUpdateResult = {
      totalCount: updates.length,
      successCount,
      failureCount,
      results,
      summary: {
        conflicts,
        errors,
        warnings
      }
    }

    console.log(`✅ [WorkstationUpdateService] 批量更新完成`, batchResult.summary)
    return batchResult
  }

  /**
   * 权限验证
   */
  private async validatePermissions(
    workstationId: string,
    context: UpdateContext
  ): Promise<{ allowed: boolean; reason?: string }> {
    // 基础权限检查
    switch (context.source) {
      case 'user':
        // 用户操作需要检查用户权限
        if (!context.userId) {
          return { allowed: false, reason: '用户操作必须提供用户ID' }
        }
        // TODO: 实现具体的用户权限检查
        break
      
      case 'system':
      case 'scheduling':
        // 系统和排程操作默认允许
        break
      
      case 'batch':
        // 批量操作需要特殊权限
        // TODO: 实现批量操作权限检查
        break
      
      default:
        return { allowed: false, reason: '未知的更新来源' }
    }

    return { allowed: true }
  }

  /**
   * 业务规则验证
   */
  private async validateBusinessRules(
    workstationId: string,
    updates: Partial<Workstation>,
    context: UpdateContext
  ): Promise<{ valid: boolean; reason?: string; warnings?: string[] }> {
    const warnings: string[] = []

    // 获取当前工位状态
    const currentWorkstationResponse = await this.workstationService.getWorkstationById(workstationId)
    if (currentWorkstationResponse.status !== 'success') {
      return { valid: false, reason: '无法获取当前工位状态' }
    }

    const currentWorkstation = currentWorkstationResponse.data
    if (!currentWorkstation) {
      return { valid: false, reason: '工位数据不存在' }
    }

    // 状态转换验证
    if (updates.status && updates.status !== currentWorkstation.status) {
      const transitionValid = this.validateStatusTransition(
        currentWorkstation.status,
        updates.status,
        context
      )
      if (!transitionValid.valid) {
        return { valid: false, reason: transitionValid.reason }
      }
      if (transitionValid.warnings) {
        warnings.push(...transitionValid.warnings)
      }
    }

    // 生产状态验证
    if (this.hasProductionStateChanges(updates)) {
      const productionValid = this.validateProductionStateChanges(
        currentWorkstation,
        updates,
        context
      )
      if (!productionValid.valid) {
        return { valid: false, reason: productionValid.reason }
      }
      if (productionValid.warnings) {
        warnings.push(...productionValid.warnings)
      }
    }

    return { valid: true, warnings: warnings.length > 0 ? warnings : undefined }
  }

  /**
   * 状态转换验证
   */
  private validateStatusTransition(
    currentStatus: string,
    newStatus: string,
    context: UpdateContext
  ): { valid: boolean; reason?: string; warnings?: string[] } {
    // 定义允许的状态转换
    const allowedTransitions: Record<string, string[]> = {
      'active': ['inactive'],
      'inactive': ['active']
    }

    if (!allowedTransitions[currentStatus]?.includes(newStatus)) {
      return {
        valid: false,
        reason: `不允许从状态 ${currentStatus} 转换到 ${newStatus}`
      }
    }

    return { valid: true }
  }

  /**
   * 检查是否有生产状态变更
   */
  private hasProductionStateChanges(updates: Partial<Workstation>): boolean {
    return !!(
      updates.currentMoldNumber !== undefined ||
      updates.currentBatchNumber !== undefined ||
      updates.batchNumberQueue !== undefined ||
      updates.lastEndTime !== undefined
    )
  }

  /**
   * 生产状态变更验证
   */
  private validateProductionStateChanges(
    currentWorkstation: Workstation,
    updates: Partial<Workstation>,
    context: UpdateContext
  ): { valid: boolean; reason?: string; warnings?: string[] } {
    const warnings: string[] = []

    // 只有排程系统可以修改生产状态
    if (context.source !== 'scheduling' && context.source !== 'system') {
      return {
        valid: false,
        reason: '只有排程系统可以修改工位的生产状态'
      }
    }

    // 检查模具和批次号的一致性
    if (updates.currentMoldNumber && updates.currentBatchNumber) {
      // TODO: 验证模具和批次号的匹配关系
      warnings.push('请确认模具编号与批次号匹配')
    }

    return { valid: true, warnings: warnings.length > 0 ? warnings : undefined }
  }

  /**
   * 获取修改者标识
   */
  private getModifiedBy(context: UpdateContext): string {
    switch (context.source) {
      case 'user':
        return context.userId || 'unknown_user'
      case 'system':
        return 'system'
      case 'scheduling':
        return 'scheduling_service'
      case 'batch':
        return 'batch_operation'
      default:
        return 'unknown'
    }
  }

  /**
   * 记录更新操作（简化版 - 符合PRD极简要求）
   */
  private async logUpdateOperation(
    workstationId: string,
    updates: Partial<Workstation>,
    context: UpdateContext,
    result: Workstation,
    startTime: number
  ): Promise<void> {
    // 简化日志记录，只在开发环境记录基础信息
    if (process.env.NODE_ENV === 'development') {
      const duration = Date.now() - startTime
      console.log(`📝 [WorkstationUpdate] 工位 ${workstationId} 更新完成 (${duration}ms)`)
      console.log(`   - 更新字段: ${Object.keys(updates).join(', ')}`)
      console.log(`   - 操作来源: ${context.source}`)
    }
  }

  /**
   * 清除工位相关缓存
   * 🔧 修复：完善缓存清理模式，确保能匹配DataAccessManager生成的所有缓存键
   */
  private async clearWorkstationCaches(workstationId: string, operation: UpdateOperation): Promise<void> {
    try {
      const { dataAccessManager } = require('@/services/dataAccess/DataAccessManager')

      let totalDeleted = 0

      // 🔧 修复：完善工位相关的缓存模式，确保覆盖所有可能的缓存键格式
      const patterns = [
        // DataAccessManager生成的缓存键格式
        'WorkstationService:*',
        'WorkstationService.*',
        'WorkstationDataAccessService:*',
        'WorkstationDataAccessService.*',

        // 具体的工位数据获取方法
        'WorkstationService:getWorkstations*',
        'WorkstationService:getWorkstations',
        'WorkstationService:getAll*',
        'WorkstationService:getAll',
        'WorkstationService:getActiveWorkstations*',
        'WorkstationService:getActiveWorkstations',

        // 特定工位的缓存
        `WorkstationService:getById:*`,
        `WorkstationService:getWorkstationById:*`,
        `WorkstationService:getByCode:*`,
        `workstation:${workstationId}*`,
        `workstations:*`,

        // 操作相关的缓存
        `WorkstationService:${operation}:*`,
        `workstation:${operation}:*`
      ]

      // 使用DataAccessManager的内置缓存系统清理缓存
      const deletedCount = dataAccessManager.clearDataTypeCache('workstations', [workstationId])
      const serviceDeleted = dataAccessManager.clearServiceCache('WorkstationService')

      totalDeleted = deletedCount + serviceDeleted

    } catch (error) {
      console.error(`[WorkstationUpdateService] 清除工位 ${workstationId} 缓存失败:`, error)
    }
  }

  /**
   * 🔧 新增：清理DataAccessManager的工位数据缓存
   * 确保排程算法能获取最新的工位状态
   */
  private async clearDataAccessManagerCaches(): Promise<void> {
    try {
      const { dataAccessManager } = await import('@/services/dataAccess/DataAccessManager')

      // 🔧 修复：强制清理DataAccessManager的统一缓存
      // 清理工位数据相关的所有缓存
      const patterns = [
        'WorkstationService:*',
        'WorkstationService.*'
      ]

      let totalDeleted = 0
      patterns.forEach(pattern => {
        // clearCacheByPattern方法不存在，使用clearServiceCache替代
        const deleted = dataAccessManager.clearServiceCache('WorkstationService')
        totalDeleted += deleted
      })

      // 清理工位数据类型缓存
      const typeDeleted = dataAccessManager.clearDataTypeCache('workstations')
      totalDeleted += typeDeleted

      console.log(`[WorkstationUpdateService] 清理DataAccessManager缓存: ${totalDeleted} 个条目`)
    } catch (error) {
      console.error('[WorkstationUpdateService] 清理DataAccessManager缓存失败:', error)
    }
  }

  /**
   * 触发后置处理
   */
  private async triggerPostUpdateActions(
    workstationId: string,
    updates: Partial<Workstation>,
    context: UpdateContext,
    result: Workstation
  ): Promise<void> {
    // TODO: 实现后置处理逻辑
    // 例如：通知其他服务、发送实时事件等
    console.log(`🔔 [WorkstationUpdateService] 触发后置处理: ${workstationId}`)

    // 发送实时事件通知（如果实时服务可用）
    try {
      // TODO: 集成实时事件发送
      // await this.sendRealtimeEvent(workstationId, updates, context, result)
    } catch (error) {
      console.warn(`[WorkstationUpdateService] 发送实时事件失败:`, error)
    }
  }
}

// 导出单例实例
export const workstationUpdateService = WorkstationUpdateService.getInstance()
