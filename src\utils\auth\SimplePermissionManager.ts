/**
 * 简化权限管理器
 * 
 * 根据PRD文档要求，提供极简的admin/employee两级权限管理
 * 替代复杂的权限计算引擎，专注于小企业的基础需求
 */

import { User, Role } from '@/types/auth'

/**
 * 简化的权限类型
 */
export type SimplePermissionLevel = 'admin' | 'employee'

/**
 * 简化的权限检查结果
 */
export interface SimplePermissionResult {
  hasPermission: boolean
  userLevel: SimplePermissionLevel
  reason?: string
}

/**
 * 预定义的权限映射
 * 将复杂权限代码映射到简单的admin/employee级别
 */
const PERMISSION_LEVEL_MAP: Record<string, SimplePermissionLevel> = {
  // 管理员专用权限
  'admin:*': 'admin',
  'admin:users:*': 'admin',
  'admin:users:create': 'admin',
  'admin:users:update': 'admin',
  'admin:users:delete': 'admin',
  'admin:roles:*': 'admin',
  'admin:permissions:*': 'admin',
  'system:admin': 'admin',
  'system:config': 'admin',
  
  // 员工可访问权限
  'data:view': 'employee',
  'data:edit': 'employee',
  'employee:data:view': 'employee',
  'employee:data:edit': 'employee',
  'orders:view': 'employee',
  'orders:create': 'employee',
  'orders:update': 'employee',
  'production:view': 'employee',
  'production:update': 'employee',
  'warehouse:view': 'employee',
  'warehouse:update': 'employee',
  
  // 默认权限（所有用户）
  'profile:view': 'employee',
  'profile:update': 'employee'
}

/**
 * 简化权限管理器类
 */
export class SimplePermissionManager {
  
  /**
   * 获取用户的权限级别
   *
   * 与SimpleAuthService的角色映射逻辑保持一致
   *
   * @param user 用户对象
   * @returns 用户权限级别
   */
  static getUserPermissionLevel(user: User): SimplePermissionLevel {
    // 检查是否为空角色列表
    if (!user.roles || user.roles.length === 0) {
      return 'employee'
    }

    // 管理员角色代码列表（与SimpleAuthService保持一致）
    const adminRoleCodes = [
      'admin',
      'system_admin',
      'administrator',
      'super_admin',
      'root',
      'manager'
    ]

    // 管理员角色名称关键词
    const adminRoleNameKeywords = [
      '管理员',
      '系统管理员',
      '超级管理员',
      'admin',
      'administrator',
      'manager',
      'supervisor'
    ]

    // 检查角色代码
    const hasAdminRoleByCode = user.roles.some(role =>
      adminRoleCodes.includes(role.code.toLowerCase())
    )

    // 检查角色名称
    const hasAdminRoleByName = user.roles.some(role =>
      adminRoleNameKeywords.some(keyword =>
        role.name.toLowerCase().includes(keyword.toLowerCase())
      )
    )

    // 检查权限（如果角色包含管理权限，也视为管理员）
    const hasAdminPermissions = user.roles.some(role =>
      role.permissions && role.permissions.some(perm =>
        perm.code.includes('admin') ||
        perm.code.includes('system') ||
        perm.code.includes('manage')
      )
    )

    const isAdmin = hasAdminRoleByCode || hasAdminRoleByName || hasAdminPermissions
    return isAdmin ? 'admin' : 'employee'
  }
  
  /**
   * 检查用户是否有指定权限
   * 
   * @param user 用户对象
   * @param permission 权限代码
   * @returns 权限检查结果
   */
  static checkPermission(user: User, permission: string): SimplePermissionResult {
    const userLevel = this.getUserPermissionLevel(user)
    
    // 管理员拥有所有权限
    if (userLevel === 'admin') {
      return {
        hasPermission: true,
        userLevel,
        reason: '管理员拥有所有权限'
      }
    }
    
    // 检查权限映射
    const requiredLevel = this.getRequiredPermissionLevel(permission)
    const hasPermission = this.comparePermissionLevels(userLevel, requiredLevel)
    
    return {
      hasPermission,
      userLevel,
      reason: hasPermission 
        ? `用户级别 ${userLevel} 满足权限要求 ${requiredLevel}`
        : `用户级别 ${userLevel} 不满足权限要求 ${requiredLevel}`
    }
  }
  
  /**
   * 获取权限所需的级别
   * 
   * @param permission 权限代码
   * @returns 所需权限级别
   */
  static getRequiredPermissionLevel(permission: string): SimplePermissionLevel {
    // 精确匹配
    if (PERMISSION_LEVEL_MAP[permission]) {
      return PERMISSION_LEVEL_MAP[permission]
    }
    
    // 通配符匹配
    for (const [pattern, level] of Object.entries(PERMISSION_LEVEL_MAP)) {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace('*', '.*'))
        if (regex.test(permission)) {
          return level
        }
      }
    }
    
    // 默认需要员工级别权限
    return 'employee'
  }
  
  /**
   * 比较权限级别
   * 
   * @param userLevel 用户权限级别
   * @param requiredLevel 所需权限级别
   * @returns 是否满足权限要求
   */
  static comparePermissionLevels(
    userLevel: SimplePermissionLevel, 
    requiredLevel: SimplePermissionLevel
  ): boolean {
    // 权限级别优先级：admin > employee
    const levelPriority = {
      'admin': 2,
      'employee': 1
    }
    
    return levelPriority[userLevel] >= levelPriority[requiredLevel]
  }
  
  /**
   * 检查用户是否为管理员
   * 
   * @param user 用户对象
   * @returns 是否为管理员
   */
  static isAdmin(user: User): boolean {
    return this.getUserPermissionLevel(user) === 'admin'
  }
  
  /**
   * 检查用户是否为员工
   * 
   * @param user 用户对象
   * @returns 是否为员工
   */
  static isEmployee(user: User): boolean {
    return this.getUserPermissionLevel(user) === 'employee'
  }
  
  /**
   * 获取用户可访问的功能列表
   * 
   * @param user 用户对象
   * @returns 可访问的功能列表
   */
  static getUserAccessibleFeatures(user: User): string[] {
    const userLevel = this.getUserPermissionLevel(user)
    const features: string[] = []
    
    // 基础功能（所有用户）
    features.push('profile:view', 'profile:update')
    
    // 员工功能
    if (userLevel === 'employee' || userLevel === 'admin') {
      features.push(
        'data:view',
        'data:edit',
        'orders:view',
        'orders:create',
        'orders:update',
        'production:view',
        'production:update',
        'warehouse:view',
        'warehouse:update'
      )
    }
    
    // 管理员功能
    if (userLevel === 'admin') {
      features.push(
        'admin:users:create',
        'admin:users:update',
        'admin:users:delete',
        'admin:roles:manage',
        'system:config',
        'data:delete'
      )
    }
    
    return features
  }
  
  /**
   * 获取权限级别的中文描述
   * 
   * @param level 权限级别
   * @returns 中文描述
   */
  static getPermissionLevelDescription(level: SimplePermissionLevel): string {
    const descriptions = {
      'admin': '系统管理员',
      'employee': '普通员工'
    }
    
    return descriptions[level]
  }
  
  /**
   * 验证权限配置
   * 
   * @returns 验证结果
   */
  static validatePermissionConfig(): {
    isValid: boolean
    warnings: string[]
  } {
    const warnings: string[] = []
    
    // 检查权限映射完整性
    const adminPermissions = Object.entries(PERMISSION_LEVEL_MAP)
      .filter(([_, level]) => level === 'admin')
      .map(([perm, _]) => perm)
    
    const employeePermissions = Object.entries(PERMISSION_LEVEL_MAP)
      .filter(([_, level]) => level === 'employee')
      .map(([perm, _]) => perm)
    
    if (adminPermissions.length === 0) {
      warnings.push('未定义管理员权限')
    }
    
    if (employeePermissions.length === 0) {
      warnings.push('未定义员工权限')
    }
    
    console.log('🔒 [SimplePermissionManager] 权限配置验证完成')
    console.log(`   - 管理员权限: ${adminPermissions.length} 个`)
    console.log(`   - 员工权限: ${employeePermissions.length} 个`)
    console.log(`   - 警告: ${warnings.length} 个`)
    
    return {
      isValid: warnings.length === 0,
      warnings
    }
  }
}

/**
 * 导出简化的权限检查函数
 * 提供与现有系统兼容的接口
 */
export const checkSimplePermission = (user: User, permission: string): boolean => {
  return SimplePermissionManager.checkPermission(user, permission).hasPermission
}

/**
 * 导出简化的角色检查函数
 */
export const checkSimpleRole = (user: User, role: SimplePermissionLevel): boolean => {
  const userLevel = SimplePermissionManager.getUserPermissionLevel(user)
  return SimplePermissionManager.comparePermissionLevels(userLevel, role)
}

export default SimplePermissionManager
