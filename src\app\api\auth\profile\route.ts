/**
 * 用户信息API路由
 * 
 * GET /api/auth/profile
 * 获取当前登录用户的详细信息
 */

import { NextRequest, NextResponse } from 'next/server'
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { UserProfileResponse } from '@/types/auth'

/**
 * 获取用户信息
 */
export async function GET(request: NextRequest) {
  try {
    // 从Cookie中获取Access Token
    const accessToken = request.cookies.get('auth-token')?.value

    if (!accessToken) {
      return NextResponse.json({
        status: 'error',
        data: null,
        message: 'Access Token不存在',
        code: 'TOKEN_NOT_FOUND',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as UserProfileResponse, { status: 401 })
    }

    console.log('🔍 [Profile API] 开始验证Access Token')

    // 使用DataAccessManager进行Token验证
    const tokenValidation = await dataAccessManager.tokenManagement.verifyAccessToken(accessToken)
    if (!tokenValidation.isValid) {
      console.log('❌ [Profile API] Token验证失败:', tokenValidation.error)
      return NextResponse.json({
        status: 'error',
        data: null,
        message: tokenValidation.error || 'Token无效或已过期',
        code: 'TOKEN_INVALID',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as UserProfileResponse, { status: 401 })
    }

    // 从验证结果获取用户信息
    const payload = tokenValidation.payload
    if (!payload || !payload.userId) {
      console.log('❌ [Profile API] Token payload无效')
      return NextResponse.json({
        status: 'error',
        data: null,
        message: 'Token格式无效',
        code: 'TOKEN_INVALID',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId()
      } as UserProfileResponse, { status: 401 })
    }

    // 调用认证服务获取用户信息
    const result = await dataAccessManager.auth.getUserById(payload.userId)

    // 根据获取用户信息的结果返回响应
    if (result.status === 'success') {
      console.log(`✅ [Profile API] 获取用户信息成功: ${result.data!.username}`)
      return NextResponse.json(result, { status: 200 })
    } else {
      console.log(`❌ [Profile API] 获取用户信息失败: ${result.message}`)

      const response = NextResponse.json(result, {
        status: getStatusCodeFromErrorCode(result.code)
      })

      // 如果用户不存在，清除Cookie
      if (result.code === 'USER_NOT_FOUND') {
        response.cookies.delete('auth-token')
        response.cookies.delete('refresh-token')
      }

      return response
    }
  } catch (error) {
    console.error('获取用户信息API错误:', error)
    
    return NextResponse.json({
      status: 'error',
      data: null,
      message: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR',
      timestamp: new Date().toISOString(),
      requestId: generateRequestId()
    } as UserProfileResponse, { status: 500 })
  }
}

/**
 * 根据错误代码获取HTTP状态码
 */
function getStatusCodeFromErrorCode(code: string): number {
  switch (code) {
    case 'TOKEN_INVALID':
    case 'TOKEN_EXPIRED':
      return 401
    case 'USER_NOT_FOUND':
      return 404
    case 'TOKEN_NOT_FOUND':
      return 401
    default:
      return 500
  }
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}
