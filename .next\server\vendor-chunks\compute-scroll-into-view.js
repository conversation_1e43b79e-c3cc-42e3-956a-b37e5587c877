"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/compute-scroll-into-view";
exports.ids = ["vendor-chunks/compute-scroll-into-view"];
exports.modules = {

/***/ "(ssr)/./node_modules/compute-scroll-into-view/dist/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/compute-scroll-into-view/dist/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compute: () => (/* binding */ r)\n/* harmony export */ });\nconst t = (t)=>\"object\" == typeof t && null != t && 1 === t.nodeType, e = (t, e)=>(!e || \"hidden\" !== t) && \"visible\" !== t && \"clip\" !== t, n = (t, n)=>{\n    if (t.clientHeight < t.scrollHeight || t.clientWidth < t.scrollWidth) {\n        const o = getComputedStyle(t, null);\n        return e(o.overflowY, n) || e(o.overflowX, n) || ((t)=>{\n            const e = ((t)=>{\n                if (!t.ownerDocument || !t.ownerDocument.defaultView) return null;\n                try {\n                    return t.ownerDocument.defaultView.frameElement;\n                } catch (t) {\n                    return null;\n                }\n            })(t);\n            return !!e && (e.clientHeight < t.scrollHeight || e.clientWidth < t.scrollWidth);\n        })(t);\n    }\n    return !1;\n}, o = (t, e, n, o, l, r, i, s)=>r < t && i > e || r > t && i < e ? 0 : r <= t && s <= n || i >= e && s >= n ? r - t - o : i > e && s < n || r < t && s > n ? i - e + l : 0, l = (t)=>{\n    const e = t.parentElement;\n    return null == e ? t.getRootNode().host || null : e;\n}, r = (e, r)=>{\n    var i, s, d, h;\n    if (\"undefined\" == typeof document) return [];\n    const { scrollMode: c, block: f, inline: u, boundary: a, skipOverflowHiddenElements: g } = r, p = \"function\" == typeof a ? a : (t)=>t !== a;\n    if (!t(e)) throw new TypeError(\"Invalid target\");\n    const m = document.scrollingElement || document.documentElement, w = [];\n    let W = e;\n    for(; t(W) && p(W);){\n        if (W = l(W), W === m) {\n            w.push(W);\n            break;\n        }\n        null != W && W === document.body && n(W) && !n(document.documentElement) || null != W && n(W, g) && w.push(W);\n    }\n    const b = null != (s = null == (i = window.visualViewport) ? void 0 : i.width) ? s : innerWidth, H = null != (h = null == (d = window.visualViewport) ? void 0 : d.height) ? h : innerHeight, { scrollX: y, scrollY: M } = window, { height: v, width: E, top: x, right: C, bottom: I, left: R } = e.getBoundingClientRect(), { top: T, right: B, bottom: F, left: V } = ((t)=>{\n        const e = window.getComputedStyle(t);\n        return {\n            top: parseFloat(e.scrollMarginTop) || 0,\n            right: parseFloat(e.scrollMarginRight) || 0,\n            bottom: parseFloat(e.scrollMarginBottom) || 0,\n            left: parseFloat(e.scrollMarginLeft) || 0\n        };\n    })(e);\n    let k = \"start\" === f || \"nearest\" === f ? x - T : \"end\" === f ? I + F : x + v / 2 - T + F, D = \"center\" === u ? R + E / 2 - V + B : \"end\" === u ? C + B : R - V;\n    const L = [];\n    for(let t = 0; t < w.length; t++){\n        const e = w[t], { height: l, width: r, top: i, right: s, bottom: d, left: h } = e.getBoundingClientRect();\n        if (\"if-needed\" === c && x >= 0 && R >= 0 && I <= H && C <= b && (e === m && !n(e) || x >= i && I <= d && R >= h && C <= s)) return L;\n        const a = getComputedStyle(e), g = parseInt(a.borderLeftWidth, 10), p = parseInt(a.borderTopWidth, 10), W = parseInt(a.borderRightWidth, 10), T = parseInt(a.borderBottomWidth, 10);\n        let B = 0, F = 0;\n        const V = \"offsetWidth\" in e ? e.offsetWidth - e.clientWidth - g - W : 0, S = \"offsetHeight\" in e ? e.offsetHeight - e.clientHeight - p - T : 0, X = \"offsetWidth\" in e ? 0 === e.offsetWidth ? 0 : r / e.offsetWidth : 0, Y = \"offsetHeight\" in e ? 0 === e.offsetHeight ? 0 : l / e.offsetHeight : 0;\n        if (m === e) B = \"start\" === f ? k : \"end\" === f ? k - H : \"nearest\" === f ? o(M, M + H, H, p, T, M + k, M + k + v, v) : k - H / 2, F = \"start\" === u ? D : \"center\" === u ? D - b / 2 : \"end\" === u ? D - b : o(y, y + b, b, g, W, y + D, y + D + E, E), B = Math.max(0, B + M), F = Math.max(0, F + y);\n        else {\n            B = \"start\" === f ? k - i - p : \"end\" === f ? k - d + T + S : \"nearest\" === f ? o(i, d, l, p, T + S, k, k + v, v) : k - (i + l / 2) + S / 2, F = \"start\" === u ? D - h - g : \"center\" === u ? D - (h + r / 2) + V / 2 : \"end\" === u ? D - s + W + V : o(h, s, r, g, W + V, D, D + E, E);\n            const { scrollLeft: t, scrollTop: n } = e;\n            B = 0 === Y ? 0 : Math.max(0, Math.min(n + B / Y, e.scrollHeight - l / Y + S)), F = 0 === X ? 0 : Math.max(0, Math.min(t + F / X, e.scrollWidth - r / X + V)), k += n - B, D += t - F;\n        }\n        L.push({\n            el: e,\n            top: B,\n            left: F\n        });\n    }\n    return L;\n};\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compute-scroll-into-view/dist/index.js\n");

/***/ })

};
;