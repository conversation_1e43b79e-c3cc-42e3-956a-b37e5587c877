/**
 * 基础安全配置
 * 
 * 根据PRD文档要求，为小企业提供简单实用的安全配置
 * 避免复杂的安全策略，专注于基础安全需求
 */

/**
 * 安全配置接口
 */
export interface SecurityConfig {
  password: {
    minLength: number
    requireNumbers: boolean
    requireSpecialChars: boolean
    requireUppercase: boolean
    requireLowercase: boolean
  }
  session: {
    duration: number // 会话持续时间（秒）
    maxLoginAttempts: number // 最大登录尝试次数
    lockoutDuration: number // 锁定持续时间（秒）
    rememberMeDuration: number // 记住我持续时间（秒）
  }
  token: {
    accessTokenDuration: number // Access Token持续时间（秒）
    refreshTokenDuration: number // Refresh Token持续时间（秒）
    autoRefreshThreshold: number // 自动刷新阈值（分钟）
  }
}

/**
 * PRD要求的基础安全配置
 * 适合小于20人的企业使用，简单易维护
 */
export const SECURITY_CONFIG: SecurityConfig = {
  // 密码策略（硬编码，适合小企业）
  password: {
    minLength: 6, // PRD要求：最小6位
    requireNumbers: false, // 简化要求，不强制数字
    requireSpecialChars: false, // 简化要求，不强制特殊字符
    requireUppercase: false, // 简化要求，不强制大写
    requireLowercase: false // 简化要求，不强制小写
  },

  // 会话配置
  session: {
    duration: 8 * 60 * 60, // 8小时（秒）- PRD要求
    maxLoginAttempts: 5, // 最大5次登录尝试
    lockoutDuration: 30 * 60, // 30分钟锁定（秒）
    rememberMeDuration: 7 * 24 * 60 * 60 // 7天记住我（秒）
  },

  // Token配置
  token: {
    accessTokenDuration: 8 * 60 * 60, // 8小时Access Token
    refreshTokenDuration: 7 * 24 * 60 * 60, // 7天Refresh Token
    autoRefreshThreshold: 5 // Token过期前5分钟自动刷新
  }
}

/**
 * 验证密码是否符合安全策略
 */
export function validatePassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  const config = SECURITY_CONFIG.password

  // 检查最小长度
  if (password.length < config.minLength) {
    errors.push(`密码长度至少${config.minLength}位`)
  }

  // 检查数字要求
  if (config.requireNumbers && !/\d/.test(password)) {
    errors.push('密码必须包含数字')
  }

  // 检查特殊字符要求
  if (config.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含特殊字符')
  }

  // 检查大写字母要求
  if (config.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母')
  }

  // 检查小写字母要求
  if (config.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 检查是否需要刷新Token
 */
export function shouldRefreshToken(tokenExp: number): boolean {
  const now = Math.floor(Date.now() / 1000)
  const thresholdSeconds = SECURITY_CONFIG.token.autoRefreshThreshold * 60
  return (tokenExp - now) <= thresholdSeconds
}

/**
 * 获取会话过期时间
 */
export function getSessionExpirationTime(): Date {
  return new Date(Date.now() + SECURITY_CONFIG.session.duration * 1000)
}

/**
 * 获取记住我过期时间
 */
export function getRememberMeExpirationTime(): Date {
  return new Date(Date.now() + SECURITY_CONFIG.session.rememberMeDuration * 1000)
}

/**
 * 检查账户是否被锁定
 */
export function isAccountLocked(loginAttempts: number, lastAttemptTime?: Date): boolean {
  if (loginAttempts < SECURITY_CONFIG.session.maxLoginAttempts) {
    return false
  }

  if (!lastAttemptTime) {
    return true
  }

  const lockoutEndTime = new Date(lastAttemptTime.getTime() + SECURITY_CONFIG.session.lockoutDuration * 1000)
  return new Date() < lockoutEndTime
}

/**
 * 获取账户解锁时间
 */
export function getAccountUnlockTime(lastAttemptTime: Date): Date {
  return new Date(lastAttemptTime.getTime() + SECURITY_CONFIG.session.lockoutDuration * 1000)
}

/**
 * 安全配置验证
 */
export function validateSecurityConfig(): void {
  console.log('🔒 [Security Config] 基础安全配置已加载')
  console.log(`   - 密码最小长度: ${SECURITY_CONFIG.password.minLength}位`)
  console.log(`   - 会话持续时间: ${SECURITY_CONFIG.session.duration / 3600}小时`)
  console.log(`   - 最大登录尝试: ${SECURITY_CONFIG.session.maxLoginAttempts}次`)
  console.log(`   - 锁定持续时间: ${SECURITY_CONFIG.session.lockoutDuration / 60}分钟`)
  console.log('✅ [Security Config] 安全配置验证完成')
}

/**
 * 获取用户友好的安全策略描述
 */
export function getPasswordPolicyDescription(): string {
  const config = SECURITY_CONFIG.password
  const requirements: string[] = [`至少${config.minLength}位字符`]

  if (config.requireNumbers) requirements.push('包含数字')
  if (config.requireSpecialChars) requirements.push('包含特殊字符')
  if (config.requireUppercase) requirements.push('包含大写字母')
  if (config.requireLowercase) requirements.push('包含小写字母')

  return `密码要求：${requirements.join('、')}`
}
