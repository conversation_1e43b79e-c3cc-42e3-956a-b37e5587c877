/**
 * 403权限不足页面
 *
 * 当用户访问没有权限的页面时显示
 */

'use client'

import React from 'react'
import { Result, Button } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { useRouter } from 'next/navigation'

/**
 * 403权限不足页面组件
 */
export default function ForbiddenPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center">
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          icon={<ExclamationCircleOutlined />}
          extra={[
            <Button type="primary" key="back" onClick={() => router.back()}>
              返回上一页
            </Button>,
            <Button key="home" onClick={() => router.push('/dashboard')}>
              返回首页
            </Button>
          ]}
        />

        <div className="mt-8 text-gray-500 text-sm">
          如需访问此页面，请联系系统管理员申请相应权限
        </div>
      </div>
    </div>
  )
}
