"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-resize-observer";
exports.ids = ["vendor-chunks/rc-resize-observer"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-resize-observer/es/Collection.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-resize-observer/es/Collection.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collection: () => (/* binding */ Collection),\n/* harmony export */   CollectionContext: () => (/* binding */ CollectionContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar CollectionContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * Collect all the resize event from children ResizeObserver\n */ function Collection(_ref) {\n    var children = _ref.children, onBatchResize = _ref.onBatchResize;\n    var resizeIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    var resizeInfosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    var onCollectionResize = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CollectionContext);\n    var onResize = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(size, element, data) {\n        resizeIdRef.current += 1;\n        var currentId = resizeIdRef.current;\n        resizeInfosRef.current.push({\n            size: size,\n            element: element,\n            data: data\n        });\n        Promise.resolve().then(function() {\n            if (currentId === resizeIdRef.current) {\n                onBatchResize === null || onBatchResize === void 0 || onBatchResize(resizeInfosRef.current);\n                resizeInfosRef.current = [];\n            }\n        });\n        // Continue bubbling if parent exist\n        onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(size, element, data);\n    }, [\n        onBatchResize,\n        onCollectionResize\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CollectionContext.Provider, {\n        value: onResize\n    }, children);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-resize-observer/es/Collection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-resize-observer/es/SingleObserver/DomWrapper.js":
/*!*************************************************************************!*\
  !*** ./node_modules/rc-resize-observer/es/SingleObserver/DomWrapper.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomWrapper)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/classCallCheck */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createClass */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inherits */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/inherits.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/createSuper */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/createSuper.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n/**\n * Fallback to findDOMNode if origin ref do not provide any dom element\n */ var DomWrapper = /*#__PURE__*/ function(_React$Component) {\n    (0,_babel_runtime_helpers_esm_inherits__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(DomWrapper, _React$Component);\n    var _super = (0,_babel_runtime_helpers_esm_createSuper__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(DomWrapper);\n    function DomWrapper() {\n        (0,_babel_runtime_helpers_esm_classCallCheck__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this, DomWrapper);\n        return _super.apply(this, arguments);\n    }\n    (0,_babel_runtime_helpers_esm_createClass__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(DomWrapper, [\n        {\n            key: \"render\",\n            value: function render() {\n                return this.props.children;\n            }\n        }\n    ]);\n    return DomWrapper;\n}(react__WEBPACK_IMPORTED_MODULE_4__.Component);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-resize-observer/es/SingleObserver/DomWrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-resize-observer/es/SingleObserver/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/rc-resize-observer/es/SingleObserver/index.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Collection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../Collection */ \"(ssr)/./node_modules/rc-resize-observer/es/Collection.js\");\n/* harmony import */ var _utils_observerUtil__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/observerUtil */ \"(ssr)/./node_modules/rc-resize-observer/es/utils/observerUtil.js\");\n/* harmony import */ var _DomWrapper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DomWrapper */ \"(ssr)/./node_modules/rc-resize-observer/es/SingleObserver/DomWrapper.js\");\n\n\n\n\n\n\n\n\nfunction SingleObserver(props, ref) {\n    var children = props.children, disabled = props.disabled;\n    var elementRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(null);\n    var wrapperRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(null);\n    var onCollectionResize = react__WEBPACK_IMPORTED_MODULE_4__.useContext(_Collection__WEBPACK_IMPORTED_MODULE_5__.CollectionContext);\n    // =========================== Children ===========================\n    var isRenderProps = typeof children === \"function\";\n    var mergedChildren = isRenderProps ? children(elementRef) : children;\n    // ============================= Size =============================\n    var sizeRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef({\n        width: -1,\n        height: -1,\n        offsetWidth: -1,\n        offsetHeight: -1\n    });\n    // ============================= Ref ==============================\n    var canRef = !isRenderProps && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.isValidElement(mergedChildren) && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_3__.supportRef)(mergedChildren);\n    var originRef = canRef ? (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_3__.getNodeRef)(mergedChildren) : null;\n    var mergedRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_3__.useComposeRef)(originRef, elementRef);\n    var getDom = function getDom() {\n        var _elementRef$current;\n        return (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(elementRef.current) || // Support `nativeElement` format\n        (elementRef.current && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(elementRef.current) === \"object\" ? (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((_elementRef$current = elementRef.current) === null || _elementRef$current === void 0 ? void 0 : _elementRef$current.nativeElement) : null) || (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(wrapperRef.current);\n    };\n    react__WEBPACK_IMPORTED_MODULE_4__.useImperativeHandle(ref, function() {\n        return getDom();\n    });\n    // =========================== Observe ============================\n    var propsRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(props);\n    propsRef.current = props;\n    // Handler\n    var onInternalResize = react__WEBPACK_IMPORTED_MODULE_4__.useCallback(function(target) {\n        var _propsRef$current = propsRef.current, onResize = _propsRef$current.onResize, data = _propsRef$current.data;\n        var _target$getBoundingCl = target.getBoundingClientRect(), width = _target$getBoundingCl.width, height = _target$getBoundingCl.height;\n        var offsetWidth = target.offsetWidth, offsetHeight = target.offsetHeight;\n        /**\n     * Resize observer trigger when content size changed.\n     * In most case we just care about element size,\n     * let's use `boundary` instead of `contentRect` here to avoid shaking.\n     */ var fixedWidth = Math.floor(width);\n        var fixedHeight = Math.floor(height);\n        if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {\n            var size = {\n                width: fixedWidth,\n                height: fixedHeight,\n                offsetWidth: offsetWidth,\n                offsetHeight: offsetHeight\n            };\n            sizeRef.current = size;\n            // IE is strange, right?\n            var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;\n            var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;\n            var sizeInfo = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, size), {}, {\n                offsetWidth: mergedOffsetWidth,\n                offsetHeight: mergedOffsetHeight\n            });\n            // Let collection know what happened\n            onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(sizeInfo, target, data);\n            if (onResize) {\n                // defer the callback but not defer to next frame\n                Promise.resolve().then(function() {\n                    onResize(sizeInfo, target);\n                });\n            }\n        }\n    }, []);\n    // Dynamic observe\n    react__WEBPACK_IMPORTED_MODULE_4__.useEffect(function() {\n        var currentElement = getDom();\n        if (currentElement && !disabled) {\n            (0,_utils_observerUtil__WEBPACK_IMPORTED_MODULE_6__.observe)(currentElement, onInternalResize);\n        }\n        return function() {\n            return (0,_utils_observerUtil__WEBPACK_IMPORTED_MODULE_6__.unobserve)(currentElement, onInternalResize);\n        };\n    }, [\n        elementRef.current,\n        disabled\n    ]);\n    // ============================ Render ============================\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_DomWrapper__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        ref: wrapperRef\n    }, canRef ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.cloneElement(mergedChildren, {\n        ref: mergedRef\n    }) : mergedChildren);\n}\nvar RefSingleObserver = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(SingleObserver);\nif (true) {\n    RefSingleObserver.displayName = \"SingleObserver\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefSingleObserver);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-resize-observer/es/SingleObserver/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-resize-observer/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/rc-resize-observer/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _rs: () => (/* reexport safe */ _utils_observerUtil__WEBPACK_IMPORTED_MODULE_6__._rs),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/Children/toArray */ \"(ssr)/./node_modules/rc-util/es/Children/toArray.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var _SingleObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SingleObserver */ \"(ssr)/./node_modules/rc-resize-observer/es/SingleObserver/index.js\");\n/* harmony import */ var _Collection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Collection */ \"(ssr)/./node_modules/rc-resize-observer/es/Collection.js\");\n/* harmony import */ var _utils_observerUtil__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/observerUtil */ \"(ssr)/./node_modules/rc-resize-observer/es/utils/observerUtil.js\");\n\n\n\n\n\n\nvar INTERNAL_PREFIX_KEY = \"rc-observer-key\";\n\n\nfunction ResizeObserver(props, ref) {\n    var children = props.children;\n    var childNodes = typeof children === \"function\" ? [\n        children\n    ] : (0,rc_util_es_Children_toArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(children);\n    if (true) {\n        if (childNodes.length > 1) {\n            (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_3__.warning)(false, \"Find more than one child node with `children` in ResizeObserver. Please use ResizeObserver.Collection instead.\");\n        } else if (childNodes.length === 0) {\n            (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_3__.warning)(false, \"`children` of ResizeObserver is empty. Nothing is in observe.\");\n        }\n    }\n    return childNodes.map(function(child, index) {\n        var key = (child === null || child === void 0 ? void 0 : child.key) || \"\".concat(INTERNAL_PREFIX_KEY, \"-\").concat(index);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_SingleObserver__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n            key: key,\n            ref: index === 0 ? ref : undefined\n        }), child);\n    });\n}\nvar RefResizeObserver = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(ResizeObserver);\nif (true) {\n    RefResizeObserver.displayName = \"ResizeObserver\";\n}\nRefResizeObserver.Collection = _Collection__WEBPACK_IMPORTED_MODULE_5__.Collection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefResizeObserver);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-resize-observer/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-resize-observer/es/utils/observerUtil.js":
/*!******************************************************************!*\
  !*** ./node_modules/rc-resize-observer/es/utils/observerUtil.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _el: () => (/* binding */ _el),\n/* harmony export */   _rs: () => (/* binding */ _rs),\n/* harmony export */   observe: () => (/* binding */ observe),\n/* harmony export */   unobserve: () => (/* binding */ unobserve)\n/* harmony export */ });\n/* harmony import */ var resize_observer_polyfill__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resize-observer-polyfill */ \"(ssr)/./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js\");\n\n// =============================== Const ===============================\nvar elementListeners = new Map();\nfunction onResize(entities) {\n    entities.forEach(function(entity) {\n        var _elementListeners$get;\n        var target = entity.target;\n        (_elementListeners$get = elementListeners.get(target)) === null || _elementListeners$get === void 0 || _elementListeners$get.forEach(function(listener) {\n            return listener(target);\n        });\n    });\n}\n// Note: ResizeObserver polyfill not support option to measure border-box resize\nvar resizeObserver = new resize_observer_polyfill__WEBPACK_IMPORTED_MODULE_0__[\"default\"](onResize);\n// Dev env only\nvar _el =  true ? elementListeners : 0; // eslint-disable-line\nvar _rs =  true ? onResize : 0; // eslint-disable-line\n// ============================== Observe ==============================\nfunction observe(element, callback) {\n    if (!elementListeners.has(element)) {\n        elementListeners.set(element, new Set());\n        resizeObserver.observe(element);\n    }\n    elementListeners.get(element).add(callback);\n}\nfunction unobserve(element, callback) {\n    if (elementListeners.has(element)) {\n        elementListeners.get(element).delete(callback);\n        if (!elementListeners.get(element).size) {\n            resizeObserver.unobserve(element);\n            elementListeners.delete(element);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-resize-observer/es/utils/observerUtil.js\n");

/***/ })

};
;