# ERP系统环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# JWT认证配置
# ===========================================

# JWT Access Token 密钥（生产环境必须设置）
JWT_ACCESS_SECRET=your-super-secret-access-key-change-in-production

# JWT Refresh Token 密钥（生产环境必须设置）
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production

# ===========================================
# 数据库配置
# ===========================================

# 数据库主机
DB_HOST=localhost

# 数据库端口
DB_PORT=3306

# 数据库名称
DB_NAME=erp_system

# 数据库用户名
DB_USER=root

# 数据库密码
DB_PASSWORD=your-database-password

# ===========================================
# 应用配置
# ===========================================

# 应用环境 (development, production, test)
NODE_ENV=development

# 应用端口
PORT=3000

# 应用域名（生产环境）
APP_DOMAIN=localhost:3000

# ===========================================
# 安全配置
# ===========================================

# 会话密钥
SESSION_SECRET=your-session-secret-key

# CORS允许的域名（生产环境设置）
CORS_ORIGIN=http://localhost:3000

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# 日志文件路径
LOG_FILE_PATH=./logs/app.log

# ===========================================
# 邮件配置（可选）
# ===========================================

# SMTP服务器
SMTP_HOST=smtp.example.com

# SMTP端口
SMTP_PORT=587

# SMTP用户名
SMTP_USER=<EMAIL>

# SMTP密码
SMTP_PASSWORD=your-email-password

# 发件人邮箱
FROM_EMAIL=<EMAIL>

# ===========================================
# 文件上传配置（可选）
# ===========================================

# 文件上传目录
UPLOAD_DIR=./uploads

# 最大文件大小（MB）
MAX_FILE_SIZE=10

# ===========================================
# 缓存配置（可选）
# ===========================================

# Redis主机（如果使用Redis缓存）
REDIS_HOST=localhost

# Redis端口
REDIS_PORT=6379

# Redis密码
REDIS_PASSWORD=

# ===========================================
# 第三方服务配置（可选）
# ===========================================

# 短信服务API密钥
SMS_API_KEY=your-sms-api-key

# 支付服务配置
PAYMENT_API_KEY=your-payment-api-key

# ===========================================
# 开发环境配置
# ===========================================

# 是否启用调试模式
DEBUG=true

# 是否启用热重载
HOT_RELOAD=true

# 是否显示详细错误信息
SHOW_ERROR_DETAILS=true
