name: 认证架构合规性检查

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/app/api/auth/**'
      - 'src/services/auth/**'
      - 'src/services/dataAccess/**'
      - 'src/utils/auth/**'
      - 'src/config/**'
      - 'middleware.ts'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/app/api/auth/**'
      - 'src/services/auth/**'
      - 'src/services/dataAccess/**'
      - 'src/utils/auth/**'
      - 'src/config/**'
      - 'middleware.ts'

jobs:
  architecture-compliance:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行架构合规性检查
      run: npm run check:auth-architecture
      
    - name: 运行架构合规性测试
      run: npm run test:architecture
      
    - name: 运行代码覆盖率测试
      run: npm run test:coverage
      
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: architecture
        name: auth-architecture-coverage
        
    - name: 检查覆盖率阈值
      run: |
        # 检查认证相关文件的覆盖率是否达到要求
        npm run test:coverage -- --testPathPattern=auth --coverageThreshold='{"global":{"branches":80,"functions":85,"lines":85,"statements":85}}'

  code-quality:
    runs-on: ubuntu-latest
    needs: architecture-compliance
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: TypeScript 类型检查
      run: npm run type-check
      
    - name: ESLint 检查
      run: npm run lint
      
    - name: 检测重复代码
      run: npm run detect:duplicates
      
    - name: 性能监控检查
      run: npm run monitor:performance
      
    - name: 生成质量报告
      run: |
        echo "## 代码质量报告" > quality-report.md
        echo "### 架构合规性检查" >> quality-report.md
        npm run check:auth-architecture >> quality-report.md 2>&1 || echo "检查完成" >> quality-report.md
        echo "### 测试覆盖率" >> quality-report.md
        npm run test:coverage -- --coverageReporters=text >> quality-report.md 2>&1 || echo "覆盖率检查完成" >> quality-report.md
        
    - name: 上传质量报告
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: quality-report.md

  security-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 安全漏洞扫描
      run: npm audit --audit-level=moderate
      
    - name: 检查敏感信息泄露
      run: |
        # 检查是否有硬编码的密钥或敏感信息
        if grep -r "default-.*-secret" src/ --include="*.ts" --include="*.tsx"; then
          echo "❌ 发现硬编码的默认密钥"
          exit 1
        fi
        
        if grep -r "password.*=.*['\"]" src/ --include="*.ts" --include="*.tsx"; then
          echo "❌ 发现可能的硬编码密码"
          exit 1
        fi
        
        echo "✅ 安全检查通过"

  performance-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 构建项目
      run: npm run build
      
    - name: 分析构建产物大小
      run: |
        echo "## 构建产物分析" > build-analysis.md
        echo "### 文件大小统计" >> build-analysis.md
        du -sh .next/static/chunks/* | sort -hr | head -20 >> build-analysis.md
        
    - name: 上传构建分析
      uses: actions/upload-artifact@v3
      with:
        name: build-analysis
        path: build-analysis.md
