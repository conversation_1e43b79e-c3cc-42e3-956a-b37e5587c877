/**
 * 认证数据访问服务
 * 
 * 负责用户认证相关的数据访问操作
 * 遵循DataAccessManager的架构规范
 */

import {
  User,
  Role,
  Permission,
  LoginRequest,
  LoginResult,
  RefreshTokenRequest,
  TokenResult,
  UserSession,
  ApiResponse,
  AuthErrorCode,
  CreateUserRequest,
  UpdateUserRequest,
  UserStatus,
  JWTPayload
} from '@/types/auth'
import { PasswordManager } from '@/utils/auth/PasswordManager'

/**
 * 认证数据访问接口
 */
export interface IAuthDataAccess {
  // 认证相关
  login(credentials: LoginRequest): Promise<ApiResponse<LoginResult>>
  logout(refreshToken: string): Promise<ApiResponse<void>>
  refreshToken(request: RefreshTokenRequest): Promise<ApiResponse<TokenResult>>
  validateToken(token: string): Promise<ApiResponse<User>>

  // 用户管理
  getUserById(id: string): Promise<ApiResponse<User>>
  getUserByUsername(username: string): Promise<ApiResponse<User>>
  getUsersList(params?: {
    page?: number
    pageSize?: number
    search?: string
    status?: UserStatus
  }): Promise<ApiResponse<{ users: User[], total: number }>>
  createUser(userData: CreateUserRequest): Promise<ApiResponse<User>>
  updateUser(id: string, updates: UpdateUserRequest): Promise<ApiResponse<User>>
  deleteUser(id: string): Promise<ApiResponse<boolean>>
  assignUserRoles(userId: string, roleIds: string[]): Promise<ApiResponse<User>>
  batchUpdateUserStatus(userIds: string[], status: UserStatus): Promise<ApiResponse<{ successCount: number, failureCount: number }>>

  // 会话管理
  createSession(userId: string, sessionData: Partial<UserSession>): Promise<ApiResponse<UserSession>>
  getSession(sessionId: string): Promise<ApiResponse<UserSession>>
  updateSession(sessionId: string, updates: Partial<UserSession>): Promise<ApiResponse<UserSession>>
  deleteSession(sessionId: string): Promise<ApiResponse<boolean>>
  getUserSessions(userId: string): Promise<ApiResponse<UserSession[]>>

  // 密码管理
  changePassword(userId: string, currentPassword: string, newPassword: string): Promise<ApiResponse<boolean>>
  resetPassword(userId: string, newPassword: string): Promise<ApiResponse<boolean>>
  forcePasswordChange(userId: string, force?: boolean): Promise<ApiResponse<boolean>>
  generateSecurePassword(length?: number): Promise<ApiResponse<string>>
  checkForcePasswordChange(userId: string): Promise<ApiResponse<boolean>>
}

/**
 * 模拟数据存储
 * 在实际项目中，这些数据应该存储在数据库中
 */
class MockDataStore {
  private static users: User[] = [
    {
      id: 'user-1',
      username: 'admin',
      email: '<EMAIL>',
      fullName: '系统管理员',
      status: 'active',
      roles: [
        {
          id: 'role-1',
          code: 'admin',
          name: '系统管理员',
          description: '拥有系统所有权限',
          permissions: [
            {
              id: 'perm-1',
              code: 'system:admin',
              name: '系统管理',
              type: 'module',
              resource: 'system',
              action: 'admin',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ],
      permissions: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ]

  private static userPasswords: Map<string, string> = new Map([
    ['user-1', '$2b$12$20ynYICgWvP5peyWMeUNquDINYFT7Q0vAMmLOaoTy1x7duhiHmyYi'] // 密码: admin123
  ])

  // 密码历史记录（用户ID -> 最近5次密码哈希）
  private static passwordHistory: Map<string, string[]> = new Map()

  // 强制修改密码标记（用户ID -> 是否需要强制修改密码）
  private static forcePasswordChange: Map<string, boolean> = new Map()

  private static sessions: UserSession[] = []
  private static loginAttempts: Map<string, { count: number; lastAttempt: number; lockedUntil?: number }> = new Map()

  static getUsers(): User[] {
    return [...this.users]
  }

  static getUserById(id: string): User | undefined {
    return this.users.find(user => user.id === id)
  }

  static getUserByUsername(username: string): User | undefined {
    return this.users.find(user => user.username === username)
  }

  static getUserPassword(userId: string): string | undefined {
    return this.userPasswords.get(userId)
  }

  static setUserPassword(userId: string, passwordHash: string): void {
    // 保存旧密码到历史记录
    const currentPassword = this.userPasswords.get(userId)
    if (currentPassword) {
      const history = this.passwordHistory.get(userId) || []
      history.unshift(currentPassword) // 添加到历史记录开头

      // 只保留最近5次密码
      if (history.length > 5) {
        history.splice(5)
      }

      this.passwordHistory.set(userId, history)
    }

    this.userPasswords.set(userId, passwordHash)
  }

  static getPasswordHistory(userId: string): string[] {
    return this.passwordHistory.get(userId) || []
  }

  static isPasswordInHistory(userId: string, passwordHash: string): boolean {
    const history = this.passwordHistory.get(userId) || []
    return history.includes(passwordHash)
  }

  static setForcePasswordChange(userId: string, force: boolean): void {
    this.forcePasswordChange.set(userId, force)
  }

  static shouldForcePasswordChange(userId: string): boolean {
    return this.forcePasswordChange.get(userId) || false
  }

  static addUser(user: User): void {
    this.users.push(user)
  }

  static updateUser(id: string, updates: Partial<User>): boolean {
    const index = this.users.findIndex(user => user.id === id)
    if (index !== -1) {
      this.users[index] = { ...this.users[index], ...updates, updatedAt: new Date().toISOString() }
      return true
    }
    return false
  }

  static deleteUser(id: string): boolean {
    const index = this.users.findIndex(user => user.id === id)
    if (index !== -1) {
      this.users.splice(index, 1)
      this.userPasswords.delete(id)
      return true
    }
    return false
  }

  static getSessions(): UserSession[] {
    return [...this.sessions]
  }

  static updateSession(sessionId: string, updates: Partial<UserSession>): boolean {
    const index = this.sessions.findIndex(session => session.id === sessionId)
    if (index !== -1) {
      this.sessions[index] = { ...this.sessions[index], ...updates }
      return true
    }
    return false
  }

  static getSessionById(id: string): UserSession | undefined {
    return this.sessions.find(session => session.id === id)
  }

  static getUserSessions(userId: string): UserSession[] {
    return this.sessions.filter(session => session.userId === userId && session.isActive)
  }

  static addSession(session: UserSession): void {
    this.sessions.push(session)
  }

  static updateSession(id: string, updates: Partial<UserSession>): boolean {
    const index = this.sessions.findIndex(session => session.id === id)
    if (index !== -1) {
      this.sessions[index] = { ...this.sessions[index], ...updates }
      return true
    }
    return false
  }

  static deleteSession(id: string): boolean {
    const index = this.sessions.findIndex(session => session.id === id)
    if (index !== -1) {
      this.sessions.splice(index, 1)
      return true
    }
    return false
  }

  static getLoginAttempts(username: string) {
    return this.loginAttempts.get(username) || { count: 0, lastAttempt: 0 }
  }

  static recordLoginAttempt(username: string, success: boolean): void {
    const now = Date.now()
    const attempts = this.getLoginAttempts(username)

    if (success) {
      // 登录成功，清除失败记录
      this.loginAttempts.delete(username)
    } else {
      // 登录失败，增加计数
      const newCount = attempts.count + 1
      const lockedUntil = newCount >= 5 ? now + (30 * 60 * 1000) : undefined // 5次失败锁定30分钟

      this.loginAttempts.set(username, {
        count: newCount,
        lastAttempt: now,
        lockedUntil
      })
    }
  }

  static isAccountLocked(username: string): boolean {
    const attempts = this.getLoginAttempts(username)
    return attempts.lockedUntil ? attempts.lockedUntil > Date.now() : false
  }
}

/**
 * 认证数据访问服务实现
 */
export class AuthDataAccessService implements IAuthDataAccess {
  private static instance: AuthDataAccessService

  private constructor() {
    console.log('🔧 [AuthDataAccessService] 服务实例已创建')
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AuthDataAccessService {
    if (!AuthDataAccessService.instance) {
      AuthDataAccessService.instance = new AuthDataAccessService()
    }
    return AuthDataAccessService.instance
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResult>> {
    try {
      console.log('🔐 [AuthDataAccessService] 开始处理登录请求:', credentials.username)
      const { username, password, rememberMe } = credentials

      // 检查账户是否被锁定
      if (MockDataStore.isAccountLocked(username)) {
        console.log('🔒 [AuthDataAccessService] 账户被锁定:', username)
        MockDataStore.recordLoginAttempt(username, false)
        return {
          status: 'error',
          data: null,
          message: '账户已被锁定，请30分钟后重试',
          code: AuthErrorCode.ACCOUNT_LOCKED,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 查找用户
      console.log('👤 [AuthDataAccessService] 查找用户:', username)
      const user = MockDataStore.getUserByUsername(username)
      if (!user) {
        console.log('❌ [AuthDataAccessService] 用户不存在:', username)
        MockDataStore.recordLoginAttempt(username, false)
        return {
          status: 'error',
          data: null,
          message: '用户名或密码错误',
          code: AuthErrorCode.INVALID_CREDENTIALS,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      console.log('✅ [AuthDataAccessService] 找到用户:', user.id, user.username)

      // 检查用户状态
      console.log('🔍 [AuthDataAccessService] 检查用户状态:', user.status)
      if (user.status !== 'active') {
        MockDataStore.recordLoginAttempt(username, false)
        return {
          status: 'error',
          data: null,
          message: user.status === 'locked' ? '账户已被锁定' : '账户未激活',
          code: user.status === 'locked' ? AuthErrorCode.ACCOUNT_LOCKED : AuthErrorCode.ACCOUNT_INACTIVE,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证密码
      console.log('🔑 [AuthDataAccessService] 开始验证密码')
      const storedPasswordHash = MockDataStore.getUserPassword(user.id)
      console.log('🔍 [AuthDataAccessService] 获取到的密码哈希:', storedPasswordHash ? '存在' : '不存在')
      if (!storedPasswordHash || !await PasswordManager.verifyPassword(password, storedPasswordHash)) {
        console.log('❌ [AuthDataAccessService] 密码验证失败')
        MockDataStore.recordLoginAttempt(username, false)
        return {
          status: 'error',
          data: null,
          message: '用户名或密码错误',
          code: AuthErrorCode.INVALID_CREDENTIALS,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 登录成功，清除失败记录
      console.log('✅ [AuthDataAccessService] 密码验证成功，开始生成会话')
      MockDataStore.recordLoginAttempt(username, true)

      // 生成会话ID（不生成JWT token，将在API层处理）
      const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      // 更新用户最后登录时间
      MockDataStore.updateUser(user.id, { lastLoginAt: new Date().toISOString() })

      // 返回用户信息和会话ID，让API层生成JWT
      const result: LoginResult = {
        user,
        sessionId, // 新增会话ID字段
        rememberMe, // 新增记住我标志
        // accessToken 和 refreshToken 将在API层生成
        expiresIn: 3600 // 1小时
      }

      return {
        status: 'success',
        data: result,
        message: '登录成功',
        code: 'LOGIN_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        status: 'error',
        data: null,
        message: '登录失败，请稍后重试',
        code: 'LOGIN_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  /**
   * 用户登出
   */
  async logout(refreshToken: string): Promise<ApiResponse<void>> {
    try {
      // TODO: Token验证应该在API路由层处理，这里简化处理
      if (!refreshToken) {
        return {
          status: 'error',
          data: null,
          message: 'Token无效',
          code: AuthErrorCode.TOKEN_INVALID,
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      console.log('🔓 [AuthDataAccessService] 处理用户登出请求')
      
      return {
        status: 'success',
        data: null,
        message: '登出成功',
        code: 'LOGOUT_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      console.error('登出失败:', error)
      return {
        status: 'error',
        data: null,
        message: '登出失败',
        code: 'LOGOUT_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  /**
   * 刷新Token
   * ✅ 架构合规：使用统一Token管理服务
   */
  async refreshToken(request: RefreshTokenRequest): Promise<ApiResponse<TokenResult>> {
    try {
      console.log('🔄 [AuthDataAccessService] 开始处理Token刷新请求')
      const { refreshToken } = request

      // 在服务器端验证Refresh Token
      const isServer = typeof window === 'undefined'
      if (!isServer) {
        console.log('❌ [AuthDataAccessService] Token刷新必须在服务器端进行')
        return {
          status: 'error',
          data: null,
          message: 'Token刷新必须在服务器端进行',
          code: 'INVALID_ENVIRONMENT',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // ✅ 架构合规：使用统一Token管理服务
      const { tokenManagementService } = await import('./TokenManagementService')
      
      // 验证Refresh Token
      const refreshResult = await tokenManagementService.verifyRefreshToken(refreshToken)
      if (!refreshResult.isValid) {
        console.log('❌ [AuthDataAccessService] Refresh Token验证失败:', refreshResult.error)
        return {
          status: 'error',
          data: null,
          message: refreshResult.error || 'Refresh Token无效或已过期',
          code: 'TOKEN_INVALID',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      if (!refreshResult.userId || !refreshResult.sessionId) {
        console.log('❌ [AuthDataAccessService] Refresh Token payload无效')
        return {
          status: 'error',
          data: null,
          message: 'Refresh Token格式无效',
          code: 'TOKEN_INVALID',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证用户是否存在
      const user = MockDataStore.getUserById(refreshResult.userId)
      if (!user) {
        console.log('❌ [AuthDataAccessService] 用户不存在:', refreshResult.userId)
        return {
          status: 'error',
          data: null,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 检查用户状态
      if (user.status !== 'active') {
        console.log('❌ [AuthDataAccessService] 用户状态不正常:', user.status)
        return {
          status: 'error',
          data: null,
          message: '用户账户状态异常',
          code: 'ACCOUNT_INACTIVE',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证会话是否有效
      const session = MockDataStore.getSessionById(refreshResult.sessionId)
      if (!session || !session.isActive) {
        console.log('❌ [AuthDataAccessService] 会话无效或已过期:', refreshResult.sessionId)
        return {
          status: 'error',
          data: null,
          message: '会话已过期',
          code: 'SESSION_EXPIRED',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // ✅ 架构合规：使用统一Token管理服务生成新Token
      const newAccessToken = await tokenManagementService.generateAccessToken({
        userId: user.id,
        username: user.username,
        roles: user.roles.map(role => role.code),
        permissions: user.roles.flatMap(role => role.permissions.map(perm => perm.code)),
        sessionId: session.id
      })

      const newRefreshToken = await tokenManagementService.generateRefreshToken(user.id, session.id)

      // 更新会话的最后活动时间
      MockDataStore.updateSession(session.id, {
        lastActivity: new Date().toISOString()
      })

      console.log('✅ [AuthDataAccessService] Token刷新成功:', user.username)
      return {
        status: 'success',
        data: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: 3600 // 1小时
        },
        message: 'Token刷新成功',
        code: 'SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      console.error('💥 [AuthDataAccessService] Token刷新异常:', error)
      return {
        status: 'error',
        data: null,
        message: 'Token刷新失败',
        code: 'TOKEN_REFRESH_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  /**
   * 验证Token并获取用户信息
   */
  async validateToken(token: string): Promise<ApiResponse<User>> {
    try {
      console.log('🔍 [AuthDataAccessService] 开始验证Token')
      
      // 在服务器端验证JWT Token
      const isServer = typeof window === 'undefined'
      if (!isServer) {
        console.log('❌ [AuthDataAccessService] Token验证必须在服务器端进行')
        return {
          status: 'error',
          data: null,
          message: 'Token验证必须在服务器端进行',
          code: 'INVALID_ENVIRONMENT',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 使用TokenManagementService验证Token
      const tokenManagement = new (await import('./TokenManagementService')).TokenManagementService()

      // 验证Token并解析payload
      const tokenValidation = await tokenManagement.verifyAccessToken(token)
      if (!tokenValidation.isValid) {
        console.log('❌ [AuthDataAccessService] Token验证失败:', tokenValidation.error)
        return {
          status: 'error',
          data: null,
          message: tokenValidation.error || 'Token无效或已过期',
          code: 'TOKEN_INVALID',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 从验证结果获取用户信息
      const payload = tokenValidation.payload
      if (!payload || !payload.userId) {
        console.log('❌ [AuthDataAccessService] Token payload无效')
        return {
          status: 'error',
          data: null,
          message: 'Token格式无效',
          code: 'TOKEN_INVALID',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 根据Token中的userId获取用户信息
      const user = MockDataStore.getUserById(payload.userId)
      if (!user) {
        console.log('❌ [AuthDataAccessService] Token中的用户不存在:', payload.userId)
        return {
          status: 'error',
          data: null,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 检查用户状态
      if (user.status !== 'active') {
        console.log('❌ [AuthDataAccessService] 用户状态不正常:', user.status)
        return {
          status: 'error',
          data: null,
          message: '用户账户状态异常',
          code: 'ACCOUNT_INACTIVE',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证会话是否有效（如果Token包含sessionId）
      if (payload.sessionId) {
        const session = MockDataStore.getSessionById(payload.sessionId)
        if (!session || !session.isActive) {
          console.log('❌ [AuthDataAccessService] 会话无效或已过期:', payload.sessionId)
          return {
            status: 'error',
            data: null,
            message: '会话已过期',
            code: 'SESSION_EXPIRED',
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId()
          }
        }
      }

      console.log('✅ [AuthDataAccessService] Token验证成功:', user.username)
      return {
        status: 'success',
        data: user,
        message: 'Token验证成功',
        code: 'SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      console.error('💥 [AuthDataAccessService] Token验证异常:', error)
      return {
        status: 'error',
        data: null,
        message: 'Token验证失败',
        code: 'TOKEN_VALIDATION_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 其他方法的实现将在下一个文件中继续...

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  // 占位方法，将在扩展文件中实现
  async getUserById(id: string): Promise<ApiResponse<User>> {
    const user = MockDataStore.getUserById(id)
    if (!user) {
      return {
        status: 'error',
        data: null,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }

    return {
      status: 'success',
      data: user,
      message: '获取用户信息成功',
      code: 'GET_USER_SUCCESS',
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId()
    }
  }

  async getUserByUsername(username: string): Promise<ApiResponse<User>> {
    const user = MockDataStore.getUserByUsername(username)
    if (!user) {
      return {
        status: 'error',
        data: null,
        message: '用户不存在',
        code: 'USER_NOT_FOUND',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }

    return {
      status: 'success',
      data: user,
      message: '获取用户信息成功',
      code: 'GET_USER_SUCCESS',
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId()
    }
  }

  // 用户管理方法实现
  async createUser(userData: CreateUserRequest): Promise<ApiResponse<User>> {
    try {
      // 验证用户名是否已存在
      const existingUser = MockDataStore.getUserByUsername(userData.username)
      if (existingUser) {
        return {
          status: 'error',
          data: null,
          message: '用户名已存在',
          code: 'USERNAME_EXISTS',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证邮箱是否已存在（如果提供）
      if (userData.email) {
        const existingEmailUser = MockDataStore.getUsers().find(u => u.email === userData.email)
        if (existingEmailUser) {
          return {
            status: 'error',
            data: null,
            message: '邮箱已存在',
            code: 'EMAIL_EXISTS',
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId()
          }
        }
      }

      // 生成用户ID
      const userId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      // 加密密码
      const { hash: passwordHash } = await PasswordManager.hashPassword(userData.password)

      // 获取角色信息（简化实现，实际应从数据库获取）
      const roles: Role[] = userData.roleIds.map(roleId => ({
        id: roleId,
        code: roleId === 'admin' ? 'admin' : 'user',
        name: roleId === 'admin' ? '管理员' : '普通用户',
        description: roleId === 'admin' ? '系统管理员角色' : '普通用户角色',
        permissions: roleId === 'admin' ? [
          {
            id: 'perm-admin',
            code: 'system:admin',
            name: '系统管理',
            type: 'module' as const,
            resource: 'system',
            action: 'admin',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ] : [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))

      // 创建用户对象
      const newUser: User = {
        id: userId,
        username: userData.username,
        email: userData.email,
        fullName: userData.fullName,
        phone: userData.phone,
        departmentId: userData.departmentId,
        employeeId: userData.employeeId,
        status: 'active',
        roles,
        permissions: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      // 保存用户和密码
      MockDataStore.addUser(newUser)
      MockDataStore.setUserPassword(userId, passwordHash)

      return {
        status: 'success',
        data: newUser,
        message: '用户创建成功',
        code: 'CREATE_USER_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '创建用户失败',
        code: 'CREATE_USER_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async updateUser(id: string, updates: UpdateUserRequest): Promise<ApiResponse<User>> {
    try {
      const existingUser = MockDataStore.getUserById(id)
      if (!existingUser) {
        return {
          status: 'error',
          data: null,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证邮箱唯一性（如果更新邮箱）
      if (updates.email && updates.email !== existingUser.email) {
        const existingEmailUser = MockDataStore.getUsers().find(u => u.email === updates.email && u.id !== id)
        if (existingEmailUser) {
          return {
            status: 'error',
            data: null,
            message: '邮箱已存在',
            code: 'EMAIL_EXISTS',
            timestamp: new Date().toISOString(),
            requestId: this.generateRequestId()
          }
        }
      }

      // 处理角色更新
      let updatedRoles = existingUser.roles
      if (updates.roleIds) {
        updatedRoles = updates.roleIds.map(roleId => ({
          id: roleId,
          code: roleId === 'admin' ? 'admin' : 'user',
          name: roleId === 'admin' ? '管理员' : '普通用户',
          description: roleId === 'admin' ? '系统管理员角色' : '普通用户角色',
          permissions: roleId === 'admin' ? [
            {
              id: 'perm-admin',
              code: 'system:admin',
              name: '系统管理',
              type: 'module' as const,
              resource: 'system',
              action: 'admin',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ] : [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
      }

      // 更新用户信息
      const updateData = {
        ...updates,
        roles: updatedRoles,
        updatedAt: new Date().toISOString()
      }

      const success = MockDataStore.updateUser(id, updateData)
      if (!success) {
        return {
          status: 'error',
          data: null,
          message: '更新用户失败',
          code: 'UPDATE_USER_ERROR',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 如果用户状态变更为禁用或锁定，终止该用户的所有会话
      if (updates.status && (updates.status === 'inactive' || updates.status === 'locked')) {
        await this.terminateUserSessions(id)
      }

      const updatedUser = MockDataStore.getUserById(id)!
      return {
        status: 'success',
        data: updatedUser,
        message: '用户更新成功',
        code: 'UPDATE_USER_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '更新用户失败',
        code: 'UPDATE_USER_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async deleteUser(id: string): Promise<ApiResponse<boolean>> {
    try {
      const existingUser = MockDataStore.getUserById(id)
      if (!existingUser) {
        return {
          status: 'error',
          data: false,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 检查是否为系统管理员（不允许删除）
      if (existingUser.username === 'admin') {
        return {
          status: 'error',
          data: false,
          message: '不能删除系统管理员账户',
          code: 'CANNOT_DELETE_ADMIN',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      const success = MockDataStore.deleteUser(id)
      return {
        status: 'success',
        data: success,
        message: success ? '用户删除成功' : '用户删除失败',
        code: success ? 'DELETE_USER_SUCCESS' : 'DELETE_USER_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: false,
        message: error instanceof Error ? error.message : '删除用户失败',
        code: 'DELETE_USER_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  async createSession(userId: string, sessionData: Partial<UserSession>): Promise<ApiResponse<UserSession>> {
    // TODO: 实现会话创建逻辑
    throw new Error('Method not implemented.')
  }

  async getSession(sessionId: string): Promise<ApiResponse<UserSession>> {
    // TODO: 实现会话获取逻辑
    throw new Error('Method not implemented.')
  }

  async updateSession(sessionId: string, updates: Partial<UserSession>): Promise<ApiResponse<UserSession>> {
    // TODO: 实现会话更新逻辑
    throw new Error('Method not implemented.')
  }

  async deleteSession(sessionId: string): Promise<ApiResponse<boolean>> {
    // TODO: 实现会话删除逻辑
    throw new Error('Method not implemented.')
  }

  async getUserSessions(userId: string): Promise<ApiResponse<UserSession[]>> {
    // TODO: 实现用户会话获取逻辑
    throw new Error('Method not implemented.')
  }

  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<ApiResponse<boolean>> {
    // TODO: 实现密码修改逻辑
    throw new Error('Method not implemented.')
  }

  async resetPassword(userId: string, newPassword: string): Promise<ApiResponse<boolean>> {
    try {
      const user = MockDataStore.getUserById(userId)
      if (!user) {
        return {
          status: 'error',
          data: false,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 验证密码强度
      const validation = PasswordManager.validatePassword(newPassword)
      if (!validation.isValid) {
        return {
          status: 'error',
          data: false,
          message: `密码不符合要求: ${validation.feedback.join(', ')}`,
          code: 'INVALID_PASSWORD',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 加密新密码
      const { hash: passwordHash } = await PasswordManager.hashPassword(newPassword)

      // 检查密码是否与历史密码重复
      if (MockDataStore.isPasswordInHistory(userId, passwordHash)) {
        return {
          status: 'error',
          data: false,
          message: '新密码不能与最近5次使用的密码相同',
          code: 'PASSWORD_REUSED',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 更新密码
      MockDataStore.setUserPassword(userId, passwordHash)

      return {
        status: 'success',
        data: true,
        message: '密码重置成功',
        code: 'RESET_PASSWORD_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: false,
        message: error instanceof Error ? error.message : '密码重置失败',
        code: 'RESET_PASSWORD_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 新增用户列表获取方法
  async getUsersList(params?: {
    page?: number
    pageSize?: number
    search?: string
    status?: UserStatus
  }): Promise<ApiResponse<{ users: User[], total: number }>> {
    try {
      let users = MockDataStore.getUsers()

      // 搜索过滤
      if (params?.search) {
        const searchTerm = params.search.toLowerCase()
        users = users.filter(user =>
          user.username.toLowerCase().includes(searchTerm) ||
          user.fullName.toLowerCase().includes(searchTerm) ||
          (user.email && user.email.toLowerCase().includes(searchTerm))
        )
      }

      // 状态过滤
      if (params?.status) {
        users = users.filter(user => user.status === params.status)
      }

      const total = users.length

      // 分页
      if (params?.page && params?.pageSize) {
        const start = (params.page - 1) * params.pageSize
        const end = start + params.pageSize
        users = users.slice(start, end)
      }

      return {
        status: 'success',
        data: { users, total },
        message: '获取用户列表成功',
        code: 'GET_USERS_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '获取用户列表失败',
        code: 'GET_USERS_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 新增用户角色分配方法
  async assignUserRoles(userId: string, roleIds: string[]): Promise<ApiResponse<User>> {
    try {
      const user = MockDataStore.getUserById(userId)
      if (!user) {
        return {
          status: 'error',
          data: null,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      // 构建角色对象（简化实现）
      const roles: Role[] = roleIds.map(roleId => ({
        id: roleId,
        code: roleId === 'admin' ? 'admin' : 'user',
        name: roleId === 'admin' ? '管理员' : '普通用户',
        description: roleId === 'admin' ? '系统管理员角色' : '普通用户角色',
        permissions: roleId === 'admin' ? [
          {
            id: 'perm-admin',
            code: 'system:admin',
            name: '系统管理',
            type: 'module' as const,
            resource: 'system',
            action: 'admin',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ] : [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))

      // 更新用户角色
      const success = MockDataStore.updateUser(userId, {
        roles,
        updatedAt: new Date().toISOString()
      })

      if (!success) {
        return {
          status: 'error',
          data: null,
          message: '角色分配失败',
          code: 'ASSIGN_ROLES_ERROR',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      const updatedUser = MockDataStore.getUserById(userId)!
      return {
        status: 'success',
        data: updatedUser,
        message: '角色分配成功',
        code: 'ASSIGN_ROLES_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '角色分配失败',
        code: 'ASSIGN_ROLES_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 终止用户所有会话的私有方法
  private async terminateUserSessions(userId: string): Promise<void> {
    try {
      // 获取用户的所有活跃会话
      const userSessions = MockDataStore.getSessions().filter(
        session => session.userId === userId && session.isActive
      )

      // 将所有会话标记为非活跃
      for (const session of userSessions) {
        MockDataStore.updateSession(session.id, {
          isActive: false,
          lastActivityAt: new Date().toISOString()
        })
      }

      console.log(`已终止用户 ${userId} 的 ${userSessions.length} 个会话`)
    } catch (error) {
      console.error('终止用户会话失败:', error)
    }
  }

  // 批量更新用户状态的方法
  async batchUpdateUserStatus(userIds: string[], status: UserStatus): Promise<ApiResponse<{ successCount: number, failureCount: number }>> {
    try {
      let successCount = 0
      let failureCount = 0
      const errors: string[] = []

      for (const userId of userIds) {
        try {
          const user = MockDataStore.getUserById(userId)
          if (!user) {
            failureCount++
            errors.push(`用户 ${userId} 不存在`)
            continue
          }

          // 检查是否为系统管理员（不允许禁用）
          if (user.username === 'admin' && status !== 'active') {
            failureCount++
            errors.push(`不能禁用系统管理员账户`)
            continue
          }

          const success = MockDataStore.updateUser(userId, {
            status,
            updatedAt: new Date().toISOString()
          })

          if (success) {
            // 如果状态变更为禁用或锁定，终止该用户的所有会话
            if (status === 'inactive' || status === 'locked') {
              await this.terminateUserSessions(userId)
            }
            successCount++
          } else {
            failureCount++
            errors.push(`更新用户 ${userId} 状态失败`)
          }
        } catch (error) {
          failureCount++
          errors.push(`处理用户 ${userId} 时发生错误: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      }

      return {
        status: 'success',
        data: { successCount, failureCount },
        message: `批量更新完成：成功 ${successCount} 个，失败 ${failureCount} 个${errors.length > 0 ? `。错误：${errors.join('; ')}` : ''}`,
        code: 'BATCH_UPDATE_STATUS_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '批量更新用户状态失败',
        code: 'BATCH_UPDATE_STATUS_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 强制修改密码
  async forcePasswordChange(userId: string, force: boolean = true): Promise<ApiResponse<boolean>> {
    try {
      const user = MockDataStore.getUserById(userId)
      if (!user) {
        return {
          status: 'error',
          data: false,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      MockDataStore.setForcePasswordChange(userId, force)

      return {
        status: 'success',
        data: true,
        message: force ? '已设置强制修改密码' : '已取消强制修改密码',
        code: 'FORCE_PASSWORD_CHANGE_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: false,
        message: error instanceof Error ? error.message : '设置强制修改密码失败',
        code: 'FORCE_PASSWORD_CHANGE_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 生成安全密码
  async generateSecurePassword(length: number = 12): Promise<ApiResponse<string>> {
    try {
      const password = PasswordManager.generateRandomPassword(length, true)

      return {
        status: 'success',
        data: password,
        message: '安全密码生成成功',
        code: 'GENERATE_PASSWORD_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: null,
        message: error instanceof Error ? error.message : '生成密码失败',
        code: 'GENERATE_PASSWORD_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  // 检查用户是否需要强制修改密码
  async checkForcePasswordChange(userId: string): Promise<ApiResponse<boolean>> {
    try {
      const user = MockDataStore.getUserById(userId)
      if (!user) {
        return {
          status: 'error',
          data: false,
          message: '用户不存在',
          code: 'USER_NOT_FOUND',
          timestamp: new Date().toISOString(),
          requestId: this.generateRequestId()
        }
      }

      const shouldForce = MockDataStore.shouldForcePasswordChange(userId)

      return {
        status: 'success',
        data: shouldForce,
        message: shouldForce ? '需要强制修改密码' : '无需强制修改密码',
        code: 'CHECK_FORCE_PASSWORD_SUCCESS',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      return {
        status: 'error',
        data: false,
        message: error instanceof Error ? error.message : '检查强制修改密码状态失败',
        code: 'CHECK_FORCE_PASSWORD_ERROR',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }
}
