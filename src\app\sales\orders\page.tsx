'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  DatePicker,
  Popconfirm,
  Descriptions,
  Divider,
  Alert,
  Progress,
  Timeline,
  Badge,
  Tooltip,
  Steps,
  App,
  Row,
  Col,
  Statistic,
  InputNumber
} from 'antd'
import { SearchOutlined, DeleteOutlined, ExportOutlined, SwapOutlined, InboxOutlined, CheckOutlined, CloseOutlined, ThunderboltOutlined, PlusOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { SalesOrder, SalesOrderItem, OrderChange, MRPResult } from '@/types'
// 移除useSalesStore依赖，使用dataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import dayjs from 'dayjs'


import AddOrderModal from '@/components/sales/AddOrderModal'
// 🔧 新增：导入生产订单相关组件和Hook
import { useMRPProductionOrders } from './hooks/useProductionOrders'
import { HistoricalProductionOrderTable } from './components/ProductionOrderTable'
import { OrderCancellationService } from '@/services/OrderCancellationService'
import { OrderQuantityChangeService } from '@/services/OrderQuantityChangeService'
import { OrderDeliveryDateChangeService } from '@/services/OrderDeliveryDateChangeService'
// ✅ 架构合规：使用DataAccessManager统一监控和合规的Hooks
import { useDebouncedSearch } from '@/hooks/useDebouncedCallback'
import { useDataChangeListener } from '@/hooks/useEventListener'
import { useDataAccessMonitor } from '@/hooks/useDataAccessMonitor'
import { useOrdersData } from '@/hooks/useOrdersData'
// ✅ 架构合规性验证工具已移除（文件不存在）
// 🔧 P4-3数据验证统一：导入统一验证服务
import OrderValidationService from '@/services/validation/OrderValidationService'
// 🔧 P5-1订单详情组件重构：导入通用订单详情组件
import BaseOrderDetailModal from '@/components/common/OrderDetailModal'
import salesOrderConfig from '@/components/common/OrderDetailModal/configs/salesOrderConfig'
import { styleHelpers } from '@/utils/styles/antdHelpers'



const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker
const { Step } = Steps

const OrderManagement: React.FC = () => {
  const { message, modal } = App.useApp()

  // MRP相关状态保留用于UI显示
  const [mrpExecuting, setMRPExecuting] = useState(false)

  const [loading, setLoading] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isChangeModalVisible, setIsChangeModalVisible] = useState(false)
  const [isAddOrderModalVisible, setIsAddOrderModalVisible] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<SalesOrder | null>(null)
  const [selectedChangeType, setSelectedChangeType] = useState<string>('')

  // Form实例 - 用于订单变更
  const [changeForm] = Form.useForm()

  // ✅ 架构合规：使用useOrdersData Hook替代手动数据加载
  const {
    orders: ordersFromHook,
    loading: ordersLoading,
    error: ordersError,
    refreshOrders,
    loadOrders,
    hasOrders,
    isEmpty
  } = useOrdersData({
    autoLoad: true,
    enableCache: true
  })

  // 保持原有的orders状态以兼容现有代码
  const [orders, setOrders] = useState<SalesOrder[]>([])

  // 同步Hook数据到本地状态
  useEffect(() => {
    setOrders(ordersFromHook)
  }, [ordersFromHook])

  // 显示错误信息
  useEffect(() => {
    if (ordersError) {
      message.error(ordersError)
    }
  }, [ordersError, message])

  // ✅ 架构合规性检查功能已移除（依赖的模块不存在）

  // 🔧 P4-2架构升级：使用数据变更监听器自动刷新数据
  useDataChangeListener('sales-orders-page', {
    onOrderCreated: () => {
      console.log('📝 检测到销售订单创建，自动刷新数据')
      refreshOrders()
    },
    onOrderUpdated: () => {
      console.log('📝 检测到销售订单更新，自动刷新数据')
      refreshOrders()
    },
    onOrderDeleted: () => {
      console.log('📝 检测到销售订单删除，自动刷新数据')
      refreshOrders()
    }
  })

  // 🔧 P4-2架构升级：优化搜索性能，使用防抖搜索
  const [searchText, setSearchText] = useState('')
  const [searchInput, setSearchInput] = useState('') // 输入框的即时值
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined)
  const [filterProductionStatus, setFilterProductionStatus] = useState<string | undefined>(undefined)
  const [dateRange, setDateRange] = useState<[any, any] | null>(null)

  // 🔧 P4-2架构升级：使用防抖搜索优化性能
  const debouncedSearch = useDebouncedSearch(
    (query: string) => {
      setSearchText(query)
    },
    300, // 300ms防抖延迟
    []
  )

  // ✅ 架构合规：使用DataAccessManager统一监控体系
  const {
    metrics,
    cacheStats,
    isMonitoring,
    clearCache,
    getPerformanceAlerts,
    formatMemorySize,
    formatPercentage,
    isHealthy,
    needsOptimization
  } = useDataAccessMonitor({
    interval: 60000, // 1分钟间隔，符合架构文档
    enabled: true,
    showDetails: process.env.NODE_ENV === 'development'
  })

  // 批量操作相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedOrders, setSelectedOrders] = useState<SalesOrder[]>([])
  const [batchLoading, setBatchLoading] = useState(false)

  // MRP相关状态 - 仅保留UI显示需要的
  const [mrpResult, setMrpResult] = useState<MRPResult | null>(null)
  const [showMRPResult, setShowMRPResult] = useState(false)
  const [mrpExecutionStep, setMrpExecutionStep] = useState(0)

  const [mrpExecutionSteps] = useState([
    '启动MRP',
    'MRP分析',
    '生成生产订单',
    '完成'
  ])

  const [productInventory] = useState({})

  // 🔧 新增：获取选中订单的历史生产订单
  const {
    productionOrders: historicalProductionOrders,
    loading: productionOrdersLoading
  } = useMRPProductionOrders(selectedOrder?.orderNumber || '')

  // API响应处理函数
  const handleApiResponse = async <T>(
    apiCall: () => Promise<{ status: string; data?: T; message?: string }>,
    operationName: string
  ): Promise<T | null> => {
    try {
      const response = await apiCall()
      if (response.status === 'success' && response.data) {
        return response.data
      } else {
        message.error(`${operationName}失败: ${response.message || '未知错误'}`)
        return null
      }
    } catch (error) {
      console.error(`${operationName}异常:`, error)
      message.error(`${operationName}失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return null
    }
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'red', text: '未审核' },
      'confirmed': { color: 'green', text: '已审核' },
      'completed': { color: 'gray', text: '完成' },
      'cancelled': { color: 'orange', text: '已取消' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取生产状态标签（与订单状态联动）
  const getProductionStatusTag = (orderStatus: string, productionStatus: string) => {
    // 如果订单未审核或已取消，不显示生产状态
    if (orderStatus === 'pending' || orderStatus === 'cancelled') {
      return <span style={{ color: '#999' }}>-</span>
    }

    // 如果订单已审核，显示生产状态（默认为未开始）
    const actualStatus = orderStatus === 'confirmed' && !productionStatus ? 'not_started' : productionStatus

    const statusMap = {
      'not_started': { color: 'orange', text: '未开始' },
      'pending': { color: 'blue', text: '待生产' },
      'in_progress': { color: 'green', text: '生产中' },
      'completed': { color: 'cyan', text: '已完成' }
    }
    const config = statusMap[actualStatus as keyof typeof statusMap] || { color: 'orange', text: '未开始' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取MRP状态标签
  const getMRPStatusTag = (mrpStatus: string) => {
    const statusMap = {
      'not_started': { color: 'default', text: '未启动' },
      'in_progress': { color: 'processing', text: '执行中' },
      'completed': { color: 'success', text: '已完成' },
      'failed': { color: 'error', text: '执行失败' }
    }
    const config = statusMap[mrpStatus as keyof typeof statusMap] || { color: 'default', text: '未启动' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取付款状态标签
  const getPaymentStatusTag = (status: string) => {
    const statusMap = {
      'unpaid': { color: 'red', text: '未付款' },
      'partial': { color: 'orange', text: '部分付款' },
      'paid': { color: 'green', text: '已付款' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 计算智能交期
  const calculateDeliveryDate = (productModelCode: string, quantity: number): string => {
    const inventory = productInventory[productModelCode as keyof typeof productInventory]
    if (!inventory) return '待确认'
    
    const { stock, dailyCapacity } = inventory
    const needProduction = Math.max(0, quantity - stock)
    const productionDays = Math.ceil(needProduction / dailyCapacity)
    const totalDays = productionDays + 3
    
    const deliveryDate = new Date()
    deliveryDate.setDate(deliveryDate.getDate() + totalDays)
    return deliveryDate.toISOString().split('T')[0]
  }

  const convertUnit = (quantity: number, fromUnit: string, toUnit: string, productModelCode: string): number => {
    const defaultWeightPerPiece = 12.0

    if (fromUnit === '个' && toUnit === '吨') {
      return (quantity * defaultWeightPerPiece) / 1000000
    } else if (fromUnit === '吨' && toUnit === '个') {
      return (quantity * 1000000) / defaultWeightPerPiece
    } else if (fromUnit === '个' && toUnit === '克') {
      return quantity * defaultWeightPerPiece
    } else if (fromUnit === '克' && toUnit === '个') {
      return quantity / defaultWeightPerPiece
    }

    return quantity
  }

  // 表格列定义
  const columns: ColumnsType<SalesOrder> = [
    {
      title: '销售订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 140,
      fixed: 'left',
      render: (orderNumber: string, record: SalesOrder) => (
        <Button
          type="link"
          onClick={() => handleViewDetail(record)}
          style={{ padding: 0, height: 'auto', fontWeight: 'bold' }}
        >
          {orderNumber}
        </Button>
      )
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      defaultSortOrder: 'descend' as const,
      render: (createdAt: string) => {
        if (!createdAt) return '-'
        return dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      ellipsis: true
    },
    {
      title: '订单日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
      sorter: (a, b) => new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()
    },
    {
      title: '订单金额',
      dataIndex: 'finalAmount',
      key: 'finalAmount',
      width: 150,
      sorter: (a, b) => a.finalAmount - b.finalAmount,
      render: (finalAmount: number, record: SalesOrder) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
            ¥{(finalAmount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </div>
          {(record.discountAmount || 0) > 0 && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              折扣: ¥{(record.discountAmount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
          )}
        </div>
      )
    },
    {
      title: '生产状态',
      dataIndex: 'productionStatus',
      key: 'productionStatus',
      width: 120,
      render: (productionStatus: string, record: SalesOrder) =>
        getProductionStatusTag(record.status, productionStatus)
    },
    {
      title: '付款状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 100,
      render: (status: string) => getPaymentStatusTag(status)
    },
    {
      title: '变更次数',
      key: 'changeCount',
      width: 100,
      render: (_, record) => (
        <div>
          {record.changes.length > 0 ? (
            <Tooltip title="点击查看变更历史">
              <Badge count={record.changes.length} size="small">
                <Button type="link" size="small">
                  {record.changes.length}次
                </Button>
              </Badge>
            </Tooltip>
          ) : (
            <span style={{ color: '#666' }}>无</span>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">

          <Button
            type="link"
            icon={<SwapOutlined />}
            onClick={() => handleOrderChange(record)}
          >
            变更
          </Button>
          <Popconfirm
            title="确定要删除这个订单吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的订单数据
  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchText ||
      order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      order.customerContact.toLowerCase().includes(searchText.toLowerCase())

    const matchesStatus = !filterStatus || order.status === filterStatus
    const matchesProductionStatus = !filterProductionStatus || order.productionStatus === filterProductionStatus

    // 日期范围过滤
    const matchesDateRange = !dateRange || !dateRange[0] || !dateRange[1] ||
      (new Date(order.orderDate) >= dateRange[0].toDate() && new Date(order.orderDate) <= dateRange[1].toDate())

    return matchesSearch && matchesStatus && matchesProductionStatus && matchesDateRange
  })

  // 统计数据
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    confirmed: orders.filter(o => o.status === 'confirmed').length,
    completed: orders.filter(o => o.status === 'completed').length,
    cancelled: orders.filter(o => o.status === 'cancelled').length,
    totalAmount: orders.reduce((sum, o) => sum + o.finalAmount, 0),
    delayedOrders: orders.filter(o => new Date(o.deliveryDate) < new Date() && o.status !== 'completed' && o.status !== 'cancelled').length
  }

  const router = useRouter()





  const handleViewDetail = (order: SalesOrder) => {
    setSelectedOrder(order)
    setIsDetailModalVisible(true)
  }

  const handleOrderChange = (order: SalesOrder) => {
    setSelectedOrder(order)
    setSelectedChangeType('') // 重置变更类型
    setIsChangeModalVisible(true)
    changeForm.resetFields()
  }

  // 🔧 新增：根据变更类型自动填充原始值
  const handleChangeTypeSelect = (changeType: string) => {
    if (!selectedOrder) return

    setSelectedChangeType(changeType) // 更新选择的变更类型
    let originalValue = ''

    switch (changeType) {
      case 'quantity':
        // 获取订单总数量
        const totalQuantity = selectedOrder.items?.reduce((sum, item) => sum + item.quantity, 0) || 0
        originalValue = totalQuantity.toString()
        break
      case 'delivery_date':
        // 获取交期，格式化为YYYY-MM-DD
        try {
          const date = new Date(selectedOrder.deliveryDate)
          originalValue = date.toISOString().split('T')[0] // 格式化为YYYY-MM-DD
        } catch (error) {
          originalValue = selectedOrder.deliveryDate.split('T')[0] // 备用方案
        }
        break

      case 'cancel':
        // 订单取消时，原始值为当前状态
        const statusMap: Record<string, string> = {
          'pending': '未审核',
          'confirmed': '已审核',
          'completed': '完成',
          'cancelled': '已取消'
        }
        originalValue = statusMap[selectedOrder.status] || selectedOrder.status
        break
      default:
        originalValue = ''
    }

    // 自动填充原始值，并清空新值
    changeForm.setFieldsValue({
      originalValue: originalValue,
      newValue: '' // 清空新值，让用户重新输入
    })
  }

  // 🔧 新增：根据变更类型获取输入提示
  const getPlaceholderByChangeType = (changeType: string, field: 'originalValue' | 'newValue') => {
    if (!changeType) return field === 'originalValue' ? '请输入原始值' : '请输入新值'

    const placeholders = {
      quantity: {
        originalValue: '当前订单总数量',
        newValue: '请输入新的数量'
      },
      delivery_date: {
        originalValue: '当前交期日期',
        newValue: '请选择新的交期日期'
      },
      cancel: {
        originalValue: '当前订单状态',
        newValue: '取消原因'
      }
    }

    return placeholders[changeType as keyof typeof placeholders]?.[field] ||
           (field === 'originalValue' ? '请输入原始值' : '请输入新值')
  }

  // 🔧 新增：根据变更类型渲染不同的输入组件
  const renderInputByChangeType = (changeType: string, field: 'originalValue' | 'newValue') => {
    const placeholder = getPlaceholderByChangeType(changeType, field)
    const isReadOnly = field === 'originalValue' && !!selectedChangeType

    switch (changeType) {
      case 'quantity':
        return (
          <InputNumber
            placeholder={placeholder}
            min={0}
            style={{ width: '100%' }}
            readOnly={isReadOnly}
          />
        )
      case 'delivery_date':
        if (field === 'originalValue') {
          return <Input placeholder={placeholder} readOnly />
        } else {
          return <DatePicker placeholder={placeholder} style={{ width: '100%' }} />
        }

      default:
        return (
          <Input
            placeholder={placeholder}
            readOnly={isReadOnly}
          />
        )
    }
  }

  const handleDelete = async (id: string) => {
    const result = await handleApiResponse(
      () => dataAccessManager.orders.delete(id),
      '删除订单'
    )

    if (result !== null) {
      await refreshOrders()
      message.success('订单删除成功')
    } else {
      message.error('删除订单失败')
    }
  }

  // 新增订单处理函数
  const handleAddOrder = () => {
    setIsAddOrderModalVisible(true)
  }

  const handleAddOrderSuccess = async (newOrder: SalesOrder) => {
    // 刷新订单列表以获取最新数据
    await refreshOrders()
    message.success('订单创建成功')
    setIsAddOrderModalVisible(false)
  }

  const handleAddOrderCancel = () => {
    setIsAddOrderModalVisible(false)
  }

  const handleStartMRP = async (order: SalesOrder) => {
    try {
      // 验证前置条件
      if (order.status !== 'confirmed') {
        message.error('只有已审核的订单才能启动MRP')
        return
      }

      if (order.mrpStatus === 'completed') {
        message.warning('该订单的MRP已经执行完成')
        return
      }

      if (order.mrpStatus === 'in_progress') {
        message.warning('该订单的MRP正在执行中，请等待完成')
        return
      }

      // 开始MRP执行
      setMRPExecuting(true)
      setMrpExecutionStep(1)
      setShowMRPResult(false)
      setMrpResult(null)

      // 更新订单MRP状态为执行中
      try {
        await dataAccessManager.orders.update(order.id, {
          mrpStatus: 'in_progress' as const,
          updatedAt: new Date().toISOString()
        })

        // 🔧 修复：立即更新selectedOrder状态，确保按钮立即禁用
        if (selectedOrder && selectedOrder.id === order.id) {
          setSelectedOrder({
            ...selectedOrder,
            mrpStatus: 'in_progress' as const,
            updatedAt: new Date().toISOString()
          })
        }
        // 立即刷新本地状态
        await refreshOrders()
      } catch (error) {
        console.error('更新订单MRP状态失败:', error)
      }

      // 动态导入MRP服务
      const { mrpService } = await import('@/services/mrpService')

      // 步骤1: 启动MRP
      message.info('正在启动MRP...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      setMrpExecutionStep(2)

      // 步骤2: MRP分析
      message.info('正在进行MRP分析...')
      await new Promise(resolve => setTimeout(resolve, 1500))
      setMrpExecutionStep(3)

      // 步骤3: 生成生产订单
      message.info('正在生成生产订单...')

      // 执行MRP
      const mrpResult = await mrpService.executeMRP({
        salesOrder: order,
        executedBy: '当前用户', // 实际应该从用户状态获取
        executionDate: new Date().toISOString()
      })

      setMrpExecutionStep(4)
      await new Promise(resolve => setTimeout(resolve, 500))

      // 步骤4: 完成
      setMRPExecuting(false)
      setMrpResult(mrpResult)
      setShowMRPResult(true)

      // 更新订单MRP状态为已完成
      try {
        await dataAccessManager.orders.update(order.id, {
          mrpStatus: 'completed' as const,
          mrpExecutedAt: new Date().toISOString(),
          mrpExecutedBy: '当前用户',
          mrpResultId: mrpResult.id,
          updatedAt: new Date().toISOString()
        })

        // 🔧 修复：立即更新selectedOrder状态，确保按钮状态正确
        if (selectedOrder && selectedOrder.id === order.id) {
          setSelectedOrder({
            ...selectedOrder,
            mrpStatus: 'completed' as const,
            mrpExecutedAt: new Date().toISOString(),
            mrpExecutedBy: '当前用户',
            mrpResultId: mrpResult.id,
            updatedAt: new Date().toISOString()
          })
        }
      } catch (error) {
        console.error('更新订单MRP完成状态失败:', error)
      }

      // 步骤5: MRP执行完成，刷新订单数据
      await refreshOrders() // 刷新订单列表以获取最新状态

      if (mrpResult.generatedProductionOrders && mrpResult.generatedProductionOrders.length > 0) {
        message.success(`MRP执行完成！生成了 ${mrpResult.totalProductionOrders} 个生产订单，请前往生产管理模块查看`)
      } else {
        message.success(`MRP执行完成！未生成新的生产订单（可能库存充足）`)
      }

    } catch (error) {
      setMRPExecuting(false)

      // 更新订单MRP状态为失败
      const updateResult = await handleApiResponse(
        () => dataAccessManager.orders.update(order.id, {
          mrpStatus: 'failed' as const,
          updatedAt: new Date().toISOString()
        }),
        '更新订单MRP状态'
      )

      if (updateResult) {
        // 🔧 修复：立即更新selectedOrder状态
        if (selectedOrder && selectedOrder.id === order.id) {
          setSelectedOrder({
            ...selectedOrder,
            mrpStatus: 'failed' as const,
            updatedAt: new Date().toISOString()
          })
        }

        await refreshOrders()
      }

      message.error(`MRP执行失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }



  const handleChangeModalOk = () => {
    changeForm.validateFields().then(async (values) => {
      if (!selectedOrder) return

      // 🔧 P4-3数据验证统一：使用OrderValidationService验证订单变更
      const changeValidation = OrderValidationService.validateOrderChange(
        selectedOrder,
        values.changeType,
        values.originalValue,
        values.newValue
      )

      if (!changeValidation.isValid) {
        message.error(`变更验证失败：${changeValidation.errors[0]}`)
        return
      }

      // 显示警告信息（如果有）
      if (changeValidation.warnings && changeValidation.warnings.length > 0) {
        changeValidation.warnings.forEach(warning => {
          message.warning(warning)
        })
      }

      const now = new Date().toISOString()
      const newChange: OrderChange = {
        id: Date.now().toString(),
        orderNumber: selectedOrder.orderNumber,
        ...values,
        changeStatus: 'pending',
        applicant: '当前用户',
        customerConfirmed: false,
        productionStatus: selectedOrder.productionStatus,
        createdAt: now
      }

      // 更新订单变更记录
      const currentOrder = orders.find(o => o.id === selectedOrder.id)
      if (currentOrder) {
        const updatedChanges = [...(currentOrder.changes || []), newChange]
        const result = await handleApiResponse(
          () => dataAccessManager.orders.update(selectedOrder.id, {
            changes: updatedChanges,
            updatedAt: now
          }),
          '提交变更申请'
        )

        if (result) {
          await refreshOrders()
          setIsChangeModalVisible(false)
          changeForm.resetFields()
          message.success('订单变更申请提交成功')
        } else {
          message.error('提交变更申请失败')
        }
      }
    })
  }

  const handleChangeModalCancel = () => {
    setIsChangeModalVisible(false)
    setSelectedChangeType('') // 重置变更类型
    changeForm.resetFields()
  }

  // 处理变更审批
  const handleChangeApproval = async (changeId: string, action: 'approve' | 'reject', reason?: string) => {
    if (!selectedOrder) return

    try {
      const now = new Date().toISOString()
      const currentOrder = orders.find(o => o.id === selectedOrder.id)
      if (!currentOrder) return

      // 更新变更记录状态
      const updatedChanges = currentOrder.changes?.map(change => {
        if (change.id === changeId) {
          return {
            ...change,
            changeStatus: action === 'approve' ? 'approved' as const : 'rejected' as const,
            approver: '当前用户',
            approvedAt: now,
            ...(reason && { rejectionReason: reason })
          }
        }
        return change
      }) || []

      // 如果变更被批准，需要执行相应的变更逻辑
      const approvedChange = updatedChanges.find(c => c.id === changeId)
      if (action === 'approve' && approvedChange) {
        switch (approvedChange.changeType) {
          case 'cancel':
            // 验证订单是否可以取消
            const cancelValidation = await OrderCancellationService.validateCancellation(selectedOrder)

            if (!cancelValidation.canCancel) {
              message.error(`无法取消订单: ${cancelValidation.reason}`)
              return
            }

            if (cancelValidation.warnings.length > 0) {
              // 显示警告信息，让用户确认
              await new Promise<void>((resolve, reject) => {
                modal.confirm({
                  title: '订单取消确认',
                  content: (
                    <div>
                      <p>确定要取消此订单吗？</p>
                      <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                        {cancelValidation.warnings.map((warning, index) => (
                          <li key={index} style={{ color: '#fa8c16' }}>{warning}</li>
                        ))}
                      </ul>
                    </div>
                  ),
                  onOk: () => resolve(),
                  onCancel: () => reject(new Error('用户取消操作'))
                })
              })
            }

            await handleOrderCancellation(selectedOrder, approvedChange)
            break;

          case 'quantity':
            // 执行数量变更
            await handleQuantityChange(selectedOrder, approvedChange)
            break;

          case 'delivery_date':
            // 执行交期变更
            await handleDeliveryDateChange(selectedOrder, approvedChange)
            break;
        }

        // 更新变更记录状态为已执行
        const finalChanges = updatedChanges.map(change => {
          if (change.id === changeId) {
            return {
              ...change,
              changeStatus: 'executed' as const,
              executedAt: new Date().toISOString()
            }
          }
          return change
        })

        // 更新订单变更记录
        const updateResult = await handleApiResponse(
          () => dataAccessManager.orders.update(selectedOrder.id, {
            changes: finalChanges,
            updatedAt: now
          }),
          '更新订单变更记录'
        )

        if (!updateResult) {
          message.error(`变更${action === 'approve' ? '批准' : '拒绝'}失败`)
          return
        }
      } else {
        // 如果是拒绝变更，只更新变更记录
        const updateResult = await handleApiResponse(
          () => dataAccessManager.orders.update(selectedOrder.id, {
            changes: updatedChanges,
            updatedAt: now
          }),
          '更新变更记录'
        )

        if (!updateResult) {
          message.error(`变更${action === 'approve' ? '批准' : '拒绝'}失败`)
          return
        }
      }

      await refreshOrders()
      message.success(`变更${action === 'approve' ? '批准' : '拒绝'}成功`)
    } catch (error) {
      message.error(`变更${action === 'approve' ? '批准' : '拒绝'}失败: ${error instanceof Error ? error.message : '未知错误'}`)
      console.error('变更审批失败:', error)
    }
  }

  // 处理订单取消的核心逻辑
  const handleOrderCancellation = async (order: SalesOrder, change: OrderChange) => {
    try {
      // 使用专门的订单取消服务
      const result = await OrderCancellationService.executeOrderCancellation(order, change)

      if (result.success) {
        message.success(
          `订单 ${order.orderNumber} 已成功取消，` +
          `同时取消了 ${result.productionOrdersCancelled} 个生产订单和 ${result.workOrdersCancelled} 个工单`
        )
      } else {
        throw new Error(result.errors.join('; '))
      }
    } catch (error) {
      console.error('订单取消处理失败:', error)
      throw error
    }
  }

  // 处理数量变更的核心逻辑
  const handleQuantityChange = async (order: SalesOrder, change: OrderChange) => {
    try {
      // 使用专门的数量变更服务
      const result = await OrderQuantityChangeService.executeQuantityChange(order, change)

      if (result.success) {
        message.success(
          `订单 ${order.orderNumber} 数量变更成功，` +
          `从 ${change.originalValue} 变更为 ${change.newValue}，` +
          `影响了 ${result.productionOrdersAffected} 个生产订单和 ${result.workOrdersAffected} 个工单`
        )
      } else {
        throw new Error(result.errors.join('; '))
      }
    } catch (error) {
      console.error('数量变更执行失败:', error)
      throw error
    }
  }

  // 处理交期变更的核心逻辑
  const handleDeliveryDateChange = async (order: SalesOrder, change: OrderChange) => {
    try {
      // 使用专门的交期变更服务
      const result = await OrderDeliveryDateChangeService.executeDeliveryDateChange(order, change)

      if (result.success) {
        const successMessage = `订单 ${order.orderNumber} 交期变更成功，` +
          `从 ${change.originalValue} 变更为 ${change.newValue}，` +
          `影响了 ${result.productionOrdersAffected} 个生产订单和 ${result.workOrdersAffected} 个工单`

        if (result.scheduleAdjustmentsRequired) {
          message.warning(
            successMessage + `\n注意：有 ${result.scheduleImpact.conflictingOrders.length} 个订单的排程需要调整`
          )
        } else {
          message.success(successMessage)
        }
      } else {
        throw new Error(result.errors.join('; '))
      }
    } catch (error) {
      console.error('交期变更执行失败:', error)
      throw error
    }
  }

  // 批量操作函数
  const handleBatchApprove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要审核的订单')
      return
    }

    const pendingOrders = selectedOrders.filter(order => order.status === 'pending')
    if (pendingOrders.length === 0) {
      message.warning('所选订单中没有未审核的订单')
      return
    }

    modal.confirm({
      title: '批量审核确认',
      content: `确定要审核 ${pendingOrders.length} 个订单吗？`,
      onOk: async () => {
        setBatchLoading(true)
        try {
          // 批量更新订单状态
          const updatePromises = pendingOrders.map(order =>
            handleApiResponse(
              () => dataAccessManager.orders.update(order.id, {
                status: 'confirmed' as const,
                productionStatus: 'not_started' as const,
                updatedAt: new Date().toISOString()
              }),
              `审核订单 ${order.orderNumber}`
            )
          )

          const results = await Promise.all(updatePromises)
          const successCount = results.filter(result => result !== null).length

          if (successCount > 0) {
            await refreshOrders()
            setSelectedRowKeys([])
            setSelectedOrders([])
            message.success(`成功审核 ${successCount} 个订单`)
          } else {
            message.error('批量审核失败，没有订单被成功审核')
          }
        } catch (error) {
          message.error(`批量审核失败: ${error instanceof Error ? error.message : '未知错误'}`)
          console.error('批量审核异常:', error)
        } finally {
          setBatchLoading(false)
        }
      }
    })
  }

  const handleBatchUnapprove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要反审核的订单')
      return
    }

    const confirmedOrders = selectedOrders.filter(order => order.status === 'confirmed')
    if (confirmedOrders.length === 0) {
      message.warning('所选订单中没有已审核的订单')
      return
    }

    modal.confirm({
      title: '批量反审核确认',
      content: `确定要反审核 ${confirmedOrders.length} 个订单吗？`,
      onOk: async () => {
        setBatchLoading(true)
        try {
          // 批量更新订单状态
          const updatePromises = confirmedOrders.map(order =>
            handleApiResponse(
              () => dataAccessManager.orders.update(order.id, {
                status: 'pending' as const,
                productionStatus: 'not_started' as const,
                updatedAt: new Date().toISOString()
              }),
              `反审核订单 ${order.orderNumber}`
            )
          )

          const results = await Promise.all(updatePromises)
          const successCount = results.filter(result => result !== null).length

          if (successCount > 0) {
            await refreshOrders()
            setSelectedRowKeys([])
            setSelectedOrders([])
            message.success(`成功反审核 ${successCount} 个订单`)
          } else {
            message.error('批量反审核失败，没有订单被成功反审核')
          }
        } catch (error) {
          message.error(`批量反审核失败: ${error instanceof Error ? error.message : '未知错误'}`)
          console.error('批量反审核异常:', error)
        } finally {
          setBatchLoading(false)
        }
      }
    })
  }

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的订单')
      return
    }

    const deletableOrders = selectedOrders.filter(order =>
      order.status === 'pending' || order.status === 'cancelled'
    )

    if (deletableOrders.length === 0) {
      message.warning('所选订单中没有可删除的订单（只能删除待审核或已取消的订单）')
      return
    }

    modal.confirm({
      title: '批量删除确认',
      content: `确定要删除 ${deletableOrders.length} 个订单吗？此操作不可恢复！`,
      onOk: async () => {
        setBatchLoading(true)
        try {
          const deletePromises = deletableOrders.map(order =>
            handleApiResponse(
              () => dataAccessManager.orders.delete(order.id),
              `删除订单 ${order.orderNumber}`
            )
          )

          const results = await Promise.all(deletePromises)
          const successCount = results.filter(result => result !== null).length

          if (successCount > 0) {
            await refreshOrders()
            setSelectedRowKeys([])
            setSelectedOrders([])
            message.success(`成功删除 ${successCount} 个订单`)
          } else {
            message.error('批量删除失败，没有订单被成功删除')
          }
        } catch (error) {
          message.error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`)
          console.error('批量删除异常:', error)
        } finally {
          setBatchLoading(false)
        }
      }
    })
  }

  const handleChangeSubmit = async () => {
    try {
      const values = await changeForm.validateFields()
      if (!selectedOrder) return

      const change: OrderChange = {
        id: Date.now().toString(),
        orderId: selectedOrder.id,
        changeType: values.changeType,
        originalValue: '',
        newValue: '',
        reason: values.reason,
        requestedBy: '当前用户',
        requestedAt: new Date().toISOString(),
        status: 'pending'
      }

      // 根据变更类型设置原始值和新值
      switch (values.changeType) {
        case 'quantity':
          const totalQuantity = selectedOrder.items?.reduce((sum, item) => sum + item.quantity, 0) || 0
          change.originalValue = totalQuantity.toString()
          change.newValue = values.newQuantity.toString()
          await handleQuantityChange(selectedOrder, change)
          break
        case 'deliveryDate':
          change.originalValue = selectedOrder.deliveryDate
          change.newValue = values.newDeliveryDate.format('YYYY-MM-DD')
          await handleDeliveryDateChange(selectedOrder, change)
          break
        case 'cancel':
          await handleOrderCancellation(selectedOrder, change)
          break
      }

      await refreshOrders()
      setIsChangeModalVisible(false)
      changeForm.resetFields()
    } catch (error) {
      console.error('订单变更失败:', error)
      message.error('订单变更失败')
    }
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="订单总数"
              value={stats.total}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="待审核"
              value={stats.pending}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已确认"
              value={stats.confirmed}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总金额"
              value={stats.totalAmount}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#f50' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作区域 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col xs={24} lg={18}>
            <Space wrap size="middle">
              <Input
                placeholder="搜索订单号、客户名称"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: '256px' }}
              />
              <Select
                placeholder="订单状态"
                value={filterStatus}
                onChange={setFilterStatus}
                style={{ width: '128px' }}
                allowClear
              >
                <Option value="pending">待审核</Option>
                <Option value="confirmed">已确认</Option>
                <Option value="completed">已完成</Option>
                <Option value="cancelled">已取消</Option>
              </Select>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                placeholder={['开始日期', '结束日期']}
              />
            </Space>
          </Col>
          <Col xs={24} lg={6}>
            <Space>
              <Button icon={<ExportOutlined />}>导出</Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setIsAddOrderModalVisible(true)}
              >
                新建订单
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 批量操作区域 */}
      {selectedRowKeys.length > 0 && (
        <Card style={{ marginBottom: '16px' }}>
          <Alert
            message={`已选择 ${selectedRowKeys.length} 个订单`}
            type="info"
            showIcon
            action={
              <Space>
                <Button
                  size="small"
                  onClick={handleBatchApprove}
                  loading={batchLoading}
                >
                  批量审核
                </Button>
                <Button
                  size="small"
                  onClick={handleBatchUnapprove}
                  loading={batchLoading}
                >
                  批量反审核
                </Button>
                <Button
                  size="small"
                  danger
                  onClick={handleBatchDelete}
                  loading={batchLoading}
                >
                  批量删除
                </Button>
              </Space>
            }
          />
        </Card>
      )}

      {/* 订单列表 */}
      <Card title="销售订单列表">
        <Table
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={ordersLoading || loading}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys, rows) => {
              setSelectedRowKeys(keys as string[])
              setSelectedOrders(rows)
            },
            getCheckboxProps: (record) => ({
              disabled: record.status === 'completed'
            })
          }}
          pagination={{
            total: filteredOrders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          scroll={{ x: 1600 }}
        />
      </Card>

      {/* 订单详情模态框 */}
      <BaseOrderDetailModal
        visible={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        order={selectedOrder}
        config={salesOrderConfig}
      />

      {/* 新建订单模态框 */}
      <AddOrderModal
        visible={isAddOrderModalVisible}
        onCancel={() => setIsAddOrderModalVisible(false)}
        onSuccess={() => {
          setIsAddOrderModalVisible(false)
          refreshOrders()
        }}
      />

      {/* 订单变更模态框 */}
      <Modal
        title="订单变更"
        open={isChangeModalVisible}
        onOk={handleChangeSubmit}
        onCancel={() => setIsChangeModalVisible(false)}
        width={600}
      >
        <Form form={changeForm} layout="vertical">
          <Form.Item
            name="changeType"
            label="变更类型"
            rules={[{ required: true, message: '请选择变更类型' }]}
          >
            <Select placeholder="请选择变更类型">
              <Option value="quantity">数量变更</Option>
              <Option value="deliveryDate">交期变更</Option>
              <Option value="cancel">订单取消</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="reason"
            label="变更原因"
            rules={[{ required: true, message: '请输入变更原因' }]}
          >
            <TextArea rows={4} placeholder="请详细说明变更原因" />
          </Form.Item>
          {selectedChangeType === 'quantity' && (
            <Form.Item
              name="newQuantity"
              label="新数量"
              rules={[{ required: true, message: '请输入新数量' }]}
            >
              <InputNumber min={1} style={{ width: '100%' }} />
            </Form.Item>
          )}
          {selectedChangeType === 'deliveryDate' && (
            <Form.Item
              name="newDeliveryDate"
              label="新交期"
              rules={[{ required: true, message: '请选择新交期' }]}
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  )
}

export default function OrderManagementPage() {
  return (
    <App>
      <OrderManagement />
    </App>
  )
}
