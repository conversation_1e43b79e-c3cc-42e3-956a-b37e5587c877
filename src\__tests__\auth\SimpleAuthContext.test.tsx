/**
 * 简化认证上下文测试
 * 
 * 测试PRD要求的极简版认证状态管理
 * 包括登录、登出、权限检查等功能
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { message } from 'antd'
import { SimpleAuthProvider, useSimpleAuth } from '@/contexts/SimpleAuthContext'
import { SimpleAuthService } from '@/services/auth/SimpleAuthService'

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn()
}))

jest.mock('antd', () => ({
  message: {
    success: jest.fn(),
    error: jest.fn()
  }
}))

jest.mock('@/services/auth/SimpleAuthService', () => ({
  SimpleAuthService: {
    login: jest.fn(),
    verifyToken: jest.fn(),
    hasPermission: jest.fn()
  }
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Test component that uses the auth context
const TestComponent: React.FC = () => {
  const {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    hasRole,
    isAdmin,
    isEmployee
  } = useSimpleAuth()

  return (
    <div>
      <div data-testid="user-info">
        {isLoading ? 'Loading...' : (
          isAuthenticated ? `User: ${user?.username} (${user?.role})` : 'Not authenticated'
        )}
      </div>
      <div data-testid="admin-status">{isAdmin ? 'Admin' : 'Not Admin'}</div>
      <div data-testid="employee-status">{isEmployee ? 'Employee' : 'Not Employee'}</div>
      <button 
        data-testid="login-btn" 
        onClick={() => login('admin', 'admin123')}
      >
        Login
      </button>
      <button 
        data-testid="logout-btn" 
        onClick={logout}
      >
        Logout
      </button>
      <div data-testid="permission-test">
        {hasPermission('admin:users:create') ? 'Has Permission' : 'No Permission'}
      </div>
      <div data-testid="role-test">
        {hasRole('admin') ? 'Has Admin Role' : 'No Admin Role'}
      </div>
    </div>
  )
}

describe('SimpleAuthContext', () => {
  const mockPush = jest.fn()
  const mockPathname = '/dashboard'

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock useRouter
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    })
    
    // Mock usePathname
    require('next/navigation').usePathname.mockReturnValue(mockPathname)
    
    // Clear localStorage
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
  })

  it('应该正确初始化未认证状态', async () => {
    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Not authenticated')
    })

    expect(screen.getByTestId('admin-status')).toHaveTextContent('Not Admin')
    expect(screen.getByTestId('employee-status')).toHaveTextContent('Not Employee')
  })

  it('应该在有有效token时自动登录', async () => {
    const mockUser = {
      id: 'user-admin',
      username: 'admin',
      role: 'admin' as const
    }

    // Mock localStorage返回token
    localStorageMock.getItem.mockReturnValue('valid-token')
    
    // Mock token验证成功
    ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('User: admin (admin)')
    })

    expect(screen.getByTestId('admin-status')).toHaveTextContent('Admin')
    expect(screen.getByTestId('employee-status')).toHaveTextContent('Employee')
  })

  it('应该在token无效时跳转到登录页', async () => {
    // Mock localStorage返回无效token
    localStorageMock.getItem.mockReturnValue('invalid-token')
    
    // Mock token验证失败
    ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(null)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/login')
    })
  })

  it('应该成功处理登录', async () => {
    const mockLoginResult = {
      user: {
        id: 'user-admin',
        username: 'admin',
        role: 'admin' as const
      },
      token: 'new-token'
    }

    ;(SimpleAuthService.login as jest.Mock).mockResolvedValue(mockLoginResult)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    // 等待初始化完成
    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Not authenticated')
    })

    // 点击登录按钮
    fireEvent.click(screen.getByTestId('login-btn'))

    await waitFor(() => {
      expect(SimpleAuthService.login).toHaveBeenCalledWith('admin', 'admin123')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'new-token')
      expect(message.success).toHaveBeenCalledWith('登录成功')
      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })
  })

  it('应该处理登录失败', async () => {
    const loginError = new Error('用户名或密码错误')
    ;(SimpleAuthService.login as jest.Mock).mockRejectedValue(loginError)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    // 等待初始化完成
    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Not authenticated')
    })

    // 点击登录按钮
    fireEvent.click(screen.getByTestId('login-btn'))

    await waitFor(() => {
      expect(message.error).toHaveBeenCalledWith('用户名或密码错误')
    })
  })

  it('应该正确处理登出', async () => {
    const mockUser = {
      id: 'user-admin',
      username: 'admin',
      role: 'admin' as const
    }

    // 先设置已登录状态
    localStorageMock.getItem.mockReturnValue('valid-token')
    ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    // 等待登录状态
    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('User: admin (admin)')
    })

    // 点击登出按钮
    fireEvent.click(screen.getByTestId('logout-btn'))

    await waitFor(() => {
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(message.success).toHaveBeenCalledWith('已安全退出')
      expect(mockPush).toHaveBeenCalledWith('/login')
    })
  })

  it('应该正确检查权限', async () => {
    const mockUser = {
      id: 'user-admin',
      username: 'admin',
      role: 'admin' as const
    }

    localStorageMock.getItem.mockReturnValue('valid-token')
    ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)
    ;(SimpleAuthService.hasPermission as jest.Mock).mockReturnValue(true)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('permission-test')).toHaveTextContent('Has Permission')
    })

    expect(SimpleAuthService.hasPermission).toHaveBeenCalledWith('admin', 'admin:users:create')
  })

  it('应该正确检查角色', async () => {
    const mockUser = {
      id: 'user-admin',
      username: 'admin',
      role: 'admin' as const
    }

    localStorageMock.getItem.mockReturnValue('valid-token')
    ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockUser)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('role-test')).toHaveTextContent('Has Admin Role')
    })
  })

  it('应该在公开路径跳过认证检查', async () => {
    // Mock公开路径
    require('next/navigation').usePathname.mockReturnValue('/login')

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Not authenticated')
    })

    // 不应该尝试验证token或跳转
    expect(SimpleAuthService.verifyToken).not.toHaveBeenCalled()
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('应该正确区分员工和管理员权限', async () => {
    const mockEmployee = {
      id: 'user-employee',
      username: 'employee',
      role: 'employee' as const
    }

    localStorageMock.getItem.mockReturnValue('valid-token')
    ;(SimpleAuthService.verifyToken as jest.Mock).mockResolvedValue(mockEmployee)

    render(
      <SimpleAuthProvider>
        <TestComponent />
      </SimpleAuthProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('User: employee (employee)')
      expect(screen.getByTestId('admin-status')).toHaveTextContent('Not Admin')
      expect(screen.getByTestId('employee-status')).toHaveTextContent('Employee')
    })
  })
})
